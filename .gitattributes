# 设置默认行为，以防人们没有设置 core.autocrlf
* text=auto

# 明确声明应该被标准化的文本文件
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.html text
*.css text
*.less text
*.scss text
*.md text
*.yml text
*.yaml text
*.xml text
*.svg text

# 声明二进制文件，不应该被修改
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.otf binary
*.pdf binary
*.zip binary
*.gz binary
*.tar binary
*.dmg binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary

# 特定于 Windows 的文件应该使用 CRLF 行尾
*.bat text eol=crlf
*.cmd text eol=crlf

# 特定于 Unix 的文件应该使用 LF 行尾
*.sh text eol=lf
