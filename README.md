# 旭动企业管理系统

基于 Ant Design Pro 和 Electron 构建的企业管理系统桌面应用。

## 功能特性

- 基于 Ant Design Pro 的现代化 UI 界面
- 支持 Windows 和 macOS 平台
- 集成了车险台账、财产险台账和增值服务台账等功能
- 支持多租户管理
- 支持自动登录和会话保持

## 开发环境

### 前提条件

- Node.js 16+
- npm 或 yarn

### 安装依赖

```bash
npm install
# 或
yarn
```

### 开发模式

```bash
# 启动 Web 开发服务器
npm run start:dev

# 启动 Electron 开发模式
npm run electron:dev
```

### 构建应用

```bash
# 构建 Web 应用
npm run build

# 构建 Windows 应用
npm run dist:win

# 构建 macOS 应用
npm run dist:mac
```

## 项目结构

- `src/` - React 应用源代码
- `electron/` - Electron 应用源代码
- `public/` - 静态资源文件
- `scripts/` - 构建和辅助脚本
- `config/` - 项目配置文件

## 注意事项

- 登录凭证存储在应用程序的用户数据目录中
- 应用程序会自动保存登录状态，下次启动时自动登录
- 如需清除登录状态，请删除应用程序的用户数据目录

## 多公司配置说明

### 配置太一和旭东公司

系统支持两种公司配置：太一企业和旭东企业。每个公司有不同的登录菜单和功能权限。

#### 切换公司配置

1. **开发环境**：
   - 修改 `src/config/company.ts` 文件中的 `CURRENT_COMPANY` 值
   - 设置为 `'taiyi'` 或 `'xudong'`
   - 使用对应的启动命令：`npm run start:taiyi` 或 `npm run start:xudong`

2. **生产环境**：
   - 使用对应的构建命令：
     - 太一企业：`npm run build:taiyi`
     - 旭东企业：`npm run build:xudong`
   - 打包应用：
     - Windows：`npm run dist:win:taiyi` 或 `npm run dist:win:xudong`
     - macOS：`npm run dist:mac:taiyi` 或 `npm run dist:mac:xudong`

3. **测试环境**：
   - 使用对应的构建命令：
     - 太一企业：`npm run build:taiyi:test`
     - 旭东企业：`npm run build:xudong:test`
   - 打包应用：
     - Windows：`npm run dist:win:taiyi:test` 或 `npm run dist:win:xudong:test`
     - macOS：`npm run dist:mac:taiyi:test` 或 `npm run dist:mac:xudong:test`
   - 测试环境后端地址：
     - 太一企业：`http://101.42.109.196:2601/jeecg-boot`
     - 旭东企业：`http://101.42.109.196:3601/jeecg-boot`

#### 菜单配置

不同公司的菜单配置在 `src/config/menuConfig.ts` 文件中定义：

- **太一企业**：包含全部功能菜单（首页、企业管理、信息管理、系统管理等）
- **旭东企业**：仅包含部分功能菜单（首页、企业管理、车险台账、财险台账）

可以通过修改 `COMPANY_MENUS` 对象来自定义每个公司可见的菜单项。

## 许可证

版权所有 © 2024 旭动企业
