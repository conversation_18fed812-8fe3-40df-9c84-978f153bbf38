/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 开发环境代理配置
  dev: {
    '/sys/': {
      // 要代理的地址
      target: 'http://127.0.0.1:8089/jeecg-boot',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
    },
    '/online/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    '/generic/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    '/jmreport/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    '/daily-config/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    '/corp/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    '/wechat/': {
      target: 'http://127.0.0.1:8089/jeecg-boot',
      changeOrigin: true,
    },
    // clickAuto 接口不再使用
  },
  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
