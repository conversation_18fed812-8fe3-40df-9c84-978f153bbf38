# 菜单权限控制说明

## 当前状态
菜单权限控制已被**禁用**，所有菜单都会显示给所有用户。

## 修改内容

### 1. 路由配置 (`config/routes.ts`)
- 移除了所有路由的 `access: 'menuFilter'` 配置
- 所有路由现在都可以直接访问

### 2. 权限控制 (`src/access.ts`)
- `menuFilter` 函数现在始终返回 `true`
- 保留了权限控制框架，便于以后重新启用

### 3. 应用配置 (`src/app.tsx`)
- `menuDataRender` 函数直接返回所有菜单数据
- 不再进行任何菜单过滤

### 4. 菜单配置 (`src/config/menuConfig.ts`)
- 保留了原有的菜单配置结构
- 添加了注释说明当前权限控制已禁用

## 如何重新启用权限控制

如果以后需要重新启用菜单权限控制，请按以下步骤操作：

### 1. 恢复路由配置
在需要权限控制的路由上添加 `access: 'menuFilter'`：
```typescript
{
  path: '/system/cityConfig',
  name: 'cityConfig',
  component: './System/CityConfig',
  access: 'menuFilter', // 重新添加权限控制
},
```

### 2. 恢复权限控制逻辑
修改 `src/access.ts` 中的 `menuFilter` 函数：
```typescript
menuFilter: (route: any) => {
  // 恢复原有的权限检查逻辑
  if (!route.name || /* 特殊路由判断 */) {
    return true;
  }
  const hasAccess = menuPermissions.includes(route.name);
  return hasAccess;
},
```

### 3. 恢复菜单过滤
修改 `src/app.tsx` 中的 `menuDataRender` 函数，重新启用菜单过滤逻辑。

## 注意事项

1. **权限框架保留**：虽然禁用了权限控制，但保留了完整的权限控制框架
2. **配置文件保留**：所有权限相关的配置文件都保留了，只是不再生效
3. **后端接口**：后端的权限接口仍然存在，前端只是不再使用这些权限数据
4. **安全考虑**：请确保后端接口有适当的安全控制，不要仅依赖前端权限控制

## 相关文件

- `config/routes.ts` - 路由配置
- `src/access.ts` - 权限控制逻辑
- `src/app.tsx` - 应用配置
- `src/config/menuConfig.ts` - 菜单配置
- `src/services/menuPermission.ts` - 菜单权限服务（保留）
- `src/services/menuFilter.ts` - 菜单过滤服务（保留）
