<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>页面重定向中...</title>
  <style>
    body {
      font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background-color: #f0f2f5;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      color: #1890ff;
      margin-bottom: 1rem;
    }
    p {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
    .loading {
      display: inline-block;
      width: 50px;
      height: 50px;
      border: 3px solid rgba(24, 144, 255, 0.3);
      border-radius: 50%;
      border-top-color: #1890ff;
      animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>页面重定向中</h1>
    <p>正在跳转到主页面，请稍候...</p>
    <div class="loading"></div>
  </div>

  <script>
    // 获取当前路径
    const currentPath = window.location.pathname;
    const currentHash = window.location.hash;
    
    // 设置重定向计时器
    setTimeout(() => {
      // 如果有 hash，保留 hash
      if (currentHash) {
        window.location.href = './index.html' + currentHash;
      } else {
        // 否则跳转到仪表盘
        window.location.href = './index.html#/dashboard/analysis';
      }
    }, 1500);
  </script>
</body>
</html>
