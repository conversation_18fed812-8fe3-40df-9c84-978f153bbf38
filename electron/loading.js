// 加载脚本
console.log('Loading script 已加载');

// 显示加载状态
function showLoading() {
  // 检查是否已存在加载指示器
  if (document.getElementById('app-loading')) {
    return;
  }

  // 创建加载指示器
  const loadingDiv = document.createElement('div');
  loadingDiv.id = 'app-loading';
  loadingDiv.style.position = 'fixed';
  loadingDiv.style.top = '0';
  loadingDiv.style.left = '0';
  loadingDiv.style.width = '100%';
  loadingDiv.style.height = '100%';
  loadingDiv.style.backgroundColor = '#f0f2f5';
  loadingDiv.style.display = 'flex';
  loadingDiv.style.flexDirection = 'column';
  loadingDiv.style.alignItems = 'center';
  loadingDiv.style.justifyContent = 'center';
  loadingDiv.style.zIndex = '9999';

  // 添加旋转加载图标
  loadingDiv.innerHTML = `
    <div style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #1890ff; border-radius: 50%; animation: spin 1s linear infinite;"></div>
    <p style="margin-top: 20px; font-size: 16px; color: #333;">正在加载资源，请稍候...</p>
    <p style="margin-top: 10px; font-size: 14px; color: #666;">正在为您加载资源，请稍候片刻</p>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `;

  // 添加到文档
  document.body.appendChild(loadingDiv);

  console.log('显示加载指示器');
}

// 隐藏加载状态
function hideLoading() {
  const loadingDiv = document.getElementById('app-loading');
  if (loadingDiv) {
    loadingDiv.style.display = 'none';
    console.log('隐藏加载指示器');
  }
}

// 修复资源路径
function fixResourcePaths() {
  console.log('尝试修复资源路径...');

  // 添加基本路径标签如果不存在
  if (!document.querySelector('base')) {
    const baseTag = document.createElement('base');
    baseTag.href = './';
    document.head.insertBefore(baseTag, document.head.firstChild);
    console.log('添加了 base 标签');
  }

  // 修复样式表链接
  const allLinks = document.querySelectorAll('link[rel="stylesheet"]');
  allLinks.forEach((link, index) => {
    if (link.href) {
      let newHref = link.href;

      // 修复各种可能的路径问题
      if (newHref.includes('http://localhost:8000/')) {
        newHref = newHref.replace('http://localhost:8000/', './');
      } else if (newHref.includes('http://localhost/')) {
        newHref = newHref.replace('http://localhost/', './');
      } else if (newHref.includes('http://') && !newHref.includes('http://www.')) {
        newHref = newHref.replace('http://', './');
      } else if (newHref.startsWith('/')) {
        newHref = '.' + newHref;
      }

      if (newHref !== link.href) {
        console.log(`修复样式表 ${index + 1}: ${link.href} -> ${newHref}`);

        // 创建新的样式表链接
        const newLink = document.createElement('link');
        newLink.rel = 'stylesheet';
        newLink.href = newHref;
        document.head.appendChild(newLink);
      }
    }
  });

  // 修复脚本链接
  const allScripts = document.querySelectorAll('script[src]');
  allScripts.forEach((script, index) => {
    if (script.src) {
      let newSrc = script.src;

      // 修复各种可能的路径问题
      if (newSrc.includes('http://localhost:8000/')) {
        newSrc = newSrc.replace('http://localhost:8000/', './');
      } else if (newSrc.includes('http://localhost/')) {
        newSrc = newSrc.replace('http://localhost/', './');
      } else if (newSrc.includes('http://') && !newSrc.includes('http://www.')) {
        newSrc = newSrc.replace('http://', './');
      } else if (newSrc.startsWith('/')) {
        newSrc = '.' + newSrc;
      }

      if (newSrc !== script.src) {
        console.log(`修复脚本 ${index + 1}: ${script.src} -> ${newSrc}`);

        // 创建新的脚本标签
        const newScript = document.createElement('script');
        newScript.src = newSrc;
        newScript.async = script.async;
        newScript.defer = script.defer;
        document.body.appendChild(newScript);
      }
    }
  });

  // 修复图片路径
  const allImages = document.querySelectorAll('img');
  allImages.forEach((img, index) => {
    if (img.src) {
      let newSrc = img.src;

      // 修复各种可能的路径问题
      if (newSrc.includes('http://localhost:8000/')) {
        newSrc = newSrc.replace('http://localhost:8000/', './');
      } else if (newSrc.includes('http://localhost/')) {
        newSrc = newSrc.replace('http://localhost/', './');
      } else if (newSrc.includes('http://') && !newSrc.includes('http://www.')) {
        newSrc = newSrc.replace('http://', './');
      } else if (newSrc.startsWith('/')) {
        newSrc = '.' + newSrc;
      }

      if (newSrc !== img.src) {
        console.log(`修复图片 ${index + 1}: ${img.src} -> ${newSrc}`);
        img.src = newSrc;
      }
    }
  });

  // 修复内联样式中的 URL
  const allStyles = document.querySelectorAll('style');
  allStyles.forEach((style, index) => {
    if (style.textContent) {
      let newContent = style.textContent;

      // 修复各种可能的路径问题
      newContent = newContent.replace(/url\(['"]*http:\/\/localhost:8000\/([^'"\)]+)['"]*\)/g, "url('./$1')");
      newContent = newContent.replace(/url\(['"]*http:\/\/localhost\/([^'"\)]+)['"]*\)/g, "url('./$1')");
      newContent = newContent.replace(/url\(['"]*\/([^'"\)]+)['"]*\)/g, "url('./$1')");

      if (newContent !== style.textContent) {
        console.log(`修复内联样式 ${index + 1}`);
        style.textContent = newContent;
      }
    }
  });

  console.log('资源路径修复完成');
}

// 当 DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOMContentLoaded 事件触发');

  // 显示加载指示器
  if (document.body) {
    showLoading();
  } else {
    // 如果 body 还不存在，等待它创建
    const observer = new MutationObserver((mutations, obs) => {
      if (document.body) {
        showLoading();
        obs.disconnect(); // 停止观察
      }
    });

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
  }

  // 尝试修复资源路径
  setTimeout(fixResourcePaths, 500);
});

// 检查登录状态
function checkLoginStatus() {
  const token = localStorage.getItem('token') || localStorage.getItem('X-Access-Token');
  if (token) {
    console.log('检测到登录令牌');
    return true;
  }
  return false;
}

// 强制跳转到仪表盘
function redirectToDashboard() {
  console.log('强制跳转到仪表盘');
  window.location.hash = '#/dashboard/analysis';
}

// 强制跳转到登录页面
function redirectToLogin() {
  console.log('强制跳转到登录页面');
  window.location.hash = '#/user/login';
}

// 当页面完全加载后执行
window.addEventListener('load', () => {
  console.log('window.load 事件触发');

  // 再次尝试修复资源路径
  fixResourcePaths();

  // 延迟隐藏加载指示器
  setTimeout(hideLoading, 1000);

  // 延迟检查登录状态和页面跳转
  setTimeout(() => {
    // 如果已登录但当前是登录页面，则跳转到仪表盘
    if (checkLoginStatus() && window.location.href.includes('user/login')) {
      redirectToDashboard();
    }

    // 如果未登录且当前不是登录页面，则跳转到登录页面
    if (!checkLoginStatus() && !window.location.href.includes('user/login')) {
      redirectToLogin();
    }

    // 如果当前是空白页面，则根据登录状态跳转
    if (window.location.hash === '') {
      if (checkLoginStatus()) {
        redirectToDashboard();
      } else {
        redirectToLogin();
      }
    }
  }, 1500);
});

// 导出函数，以便其他脚本可以使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    showLoading,
    hideLoading,
    fixResourcePaths,
    checkLoginStatus,
    redirectToDashboard,
    redirectToLogin
  };
}
