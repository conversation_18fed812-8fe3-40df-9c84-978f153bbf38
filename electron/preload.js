// preload.js
const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 暴露 Electron API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取应用版本
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),

  // 打开外部链接
  openExternalLink: (url) => ipcRenderer.invoke('open-external-link', url),

  // 加载主应用
  loadMainApp: () => ipcRenderer.invoke('load-main-app'),

  // 登录功能
  login: (username, password) => ipcRenderer.invoke('login', username, password),

  // 获取本地存储的登录信息
  getLoginInfo: () => ipcRenderer.invoke('get-login-info'),

  // 设置本地存储的登录信息
  setLoginInfo: (loginInfo) => ipcRenderer.invoke('set-login-info', loginInfo),

  // 打开开发者工具
  openDevTools: () => {
    if (window.electronAPI) {
      const devToolsScript = document.createElement('script');
      devToolsScript.textContent = `
        console.log('开发者工具已打开');
        // 添加一些调试辅助函数
        window.debug = {
          // 检查当前登录状态
          checkLoginStatus: function() {
            console.log('登录状态:');
            console.log('- token:', localStorage.getItem('token') || '未设置');
            console.log('- X-Access-Token:', localStorage.getItem('X-Access-Token') || '未设置');
            console.log('- userInfo:', localStorage.getItem('userInfo') || '未设置');
            return {
              token: localStorage.getItem('token'),
              accessToken: localStorage.getItem('X-Access-Token'),
              userInfo: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : null
            };
          },

          // 检查当前路由
          checkRoute: function() {
            console.log('当前路由:', window.location.hash || window.location.pathname);
            return window.location.hash || window.location.pathname;
          },

          // 模拟请求
          request: async function(url, options = {}) {
            console.log('发送请求:', url, options);
            try {
              const response = await fetch(url, options);
              const data = await response.json();
              console.log('请求响应:', data);
              return data;
            } catch (error) {
              console.error('请求错误:', error);
              throw error;
            }
          },

          // 跳转到指定路由
          navigateTo: function(route) {
            console.log('跳转到:', route);
            window.location.hash = route;
          }
        };
        console.log('已添加调试工具，可以使用 window.debug 访问');
      `;
      document.head.appendChild(devToolsScript);
    }
  },

  // 版本信息
  versions: {
    electron: process.versions.electron,
    chrome: process.versions.chrome,
    node: process.versions.node
  }
});

// 优化内存函数
function optimizeMemory() {
  console.log('执行内存优化...');

  // 清除未使用的内存
  if (window.gc) {
    window.gc();
  }

  // 清除控制台
  console.clear();
}

// 调试 DOM 结构函数
function debugDOMStructure() {
  console.log('调试 DOM 结构:');

  // 检查文档是否已加载
  if (document.readyState === 'loading') {
    console.log('文档正在加载中...');
    return;
  }

  // 检查 body 是否存在
  if (!document.body) {
    console.log('document.body 不存在');
    return;
  }

  // 检查 body 的子元素
  console.log('body 子元素数量:', document.body.children.length);

  // 检查 #root 元素
  const rootElement = document.getElementById('root');
  if (rootElement) {
    console.log('#root 元素存在');
    console.log('#root 子元素数量:', rootElement.children.length);
  } else {
    console.log('#root 元素不存在');
  }
}

// 修复资源路径函数
function fixResourcePaths() {
  console.log('尝试修复资源路径...');

  // 添加基本路径标签如果不存在
  if (!document.querySelector('base')) {
    const baseTag = document.createElement('base');
    baseTag.href = './';
    document.head.insertBefore(baseTag, document.head.firstChild);
    console.log('添加了 base 标签');
  }

  // 修复样式表链接
  const allLinks = document.querySelectorAll('link[rel="stylesheet"]');
  allLinks.forEach((link, index) => {
    if (link.href) {
      let newHref = link.href;

      // 修复各种可能的路径问题
      if (newHref.includes('http://localhost:8000/')) {
        newHref = newHref.replace('http://localhost:8000/', './');
      } else if (newHref.includes('http://localhost/')) {
        newHref = newHref.replace('http://localhost/', './');
      } else if (newHref.includes('http://') && !newHref.includes('http://www.')) {
        newHref = newHref.replace('http://', './');
      } else if (newHref.startsWith('/')) {
        newHref = '.' + newHref;
      }

      if (newHref !== link.href) {
        console.log(`修复样式表 ${index + 1}: ${link.href} -> ${newHref}`);

        // 创建新的样式表链接
        const newLink = document.createElement('link');
        newLink.rel = 'stylesheet';
        newLink.href = newHref;
        document.head.appendChild(newLink);
      }
    }
  });

  // 修复脚本链接
  const allScripts = document.querySelectorAll('script[src]');
  allScripts.forEach((script, index) => {
    if (script.src) {
      let newSrc = script.src;

      // 修复各种可能的路径问题
      if (newSrc.includes('http://localhost:8000/')) {
        newSrc = newSrc.replace('http://localhost:8000/', './');
      } else if (newSrc.includes('http://localhost/')) {
        newSrc = newSrc.replace('http://localhost/', './');
      } else if (newSrc.includes('http://') && !newSrc.includes('http://www.')) {
        newSrc = newSrc.replace('http://', './');
      } else if (newSrc.startsWith('/')) {
        newSrc = '.' + newSrc;
      }

      if (newSrc !== script.src) {
        console.log(`修复脚本 ${index + 1}: ${script.src} -> ${newSrc}`);

        // 创建新的脚本标签
        const newScript = document.createElement('script');
        newScript.src = newSrc;
        newScript.async = script.async;
        newScript.defer = script.defer;
        document.body.appendChild(newScript);
      }
    }
  });

  // 修复图片路径
  const allImages = document.querySelectorAll('img');
  allImages.forEach((img, index) => {
    if (img.src) {
      let newSrc = img.src;

      // 修复各种可能的路径问题
      if (newSrc.includes('http://localhost:8000/')) {
        newSrc = newSrc.replace('http://localhost:8000/', './');
      } else if (newSrc.includes('http://localhost/')) {
        newSrc = newSrc.replace('http://localhost/', './');
      } else if (newSrc.includes('http://') && !newSrc.includes('http://www.')) {
        newSrc = newSrc.replace('http://', './');
      } else if (newSrc.startsWith('/')) {
        newSrc = '.' + newSrc;
      }

      if (newSrc !== img.src) {
        console.log(`修复图片 ${index + 1}: ${img.src} -> ${newSrc}`);
        img.src = newSrc;
      }
    }
  });

  // 修复内联样式中的 URL
  const allStyles = document.querySelectorAll('style');
  allStyles.forEach((style, index) => {
    if (style.textContent) {
      let newContent = style.textContent;

      // 修复各种可能的路径问题
      newContent = newContent.replace(/url\(['"]?http:\/\/localhost:8000\/([^'"\)]+)['"]?\)/g, "url('./$1')");
      newContent = newContent.replace(/url\(['"]?http:\/\/localhost\/([^'"\)]+)['"]?\)/g, "url('./$1')");
      newContent = newContent.replace(/url\(['"]?\/([^'"\)]+)['"]?\)/g, "url('./$1')");

      if (newContent !== style.textContent) {
        console.log(`修复内联样式 ${index + 1}`);
        style.textContent = newContent;
      }
    }
  });

  console.log('资源路径修复完成');
}

// 创建简单登录表单函数
function createSimpleLoginForm() {
  const rootElement = document.getElementById('root') || document.body;

  // 清空现有内容
  rootElement.innerHTML = '';

  // 创建简单的登录表单
  const loginForm = document.createElement('div');
  loginForm.style.fontFamily = 'Arial, sans-serif';
  loginForm.style.maxWidth = '400px';
  loginForm.style.margin = '100px auto';
  loginForm.style.padding = '20px';
  loginForm.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
  loginForm.style.borderRadius = '5px';
  loginForm.style.backgroundColor = '#fff';

  loginForm.innerHTML = `
    <h2 style="text-align: center; color: #1890ff;">旭动企业管理系统</h2>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">用户名</label>
      <input type="text" id="username" placeholder="请输入用户名" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
    </div>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">密码</label>
      <input type="password" id="password" placeholder="请输入密码" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
    </div>
    <button id="login-btn" style="width: 100%; padding: 10px; background-color: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">登录</button>
    <p style="text-align: center; margin-top: 15px; color: #999;">版本: ${process.versions.electron}</p>
  `;

  rootElement.appendChild(loginForm);

  // 添加登录按钮事件
  document.getElementById('login-btn').addEventListener('click', () => {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
      alert('请输入用户名和密码');
      return;
    }

    alert('登录功能在本地模式下不可用，这只是一个界面演示');
  });

  console.log('简单登录表单已创建');
}

// 当 DOM 加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
  console.log('DOMContentLoaded 事件触发');

  // 调用调试函数
  debugDOMStructure();

  // 检查页面中的重要元素
  console.log('检查页面元素:');
  console.log('- #root 元素存在:', !!document.getElementById('root'));
  console.log('- #app 元素存在:', !!document.getElementById('app'));

  // 检查样式和脚本加载情况
  const allLinks = document.querySelectorAll('link');
  const allScripts = document.querySelectorAll('script');

  console.log('样式表加载情况:');
  allLinks.forEach((link, index) => {
    console.log(`- 样式表 ${index + 1}:`, link.href || 'inline');
  });

  console.log('脚本加载情况:');
  allScripts.forEach((script, index) => {
    console.log(`- 脚本 ${index + 1}:`, script.src || 'inline');
  });

  // 尝试修复资源路径
  fixResourcePaths();

  // 替换版本信息
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector);
    if (element) element.innerText = text;
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency]);
  }

  // 添加事件监听器，当页面切换到后台时清理内存
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      optimizeMemory();
    }
  });

  // 页面加载完成后清理内存并再次调用调试函数
  window.addEventListener('load', () => {
    console.log('window.load 事件触发');
    debugDOMStructure();

    // 再次尝试修复资源路径
    setTimeout(fixResourcePaths, 500);

    // 如果页面中没有内容，尝试手动创建一个简单的登录表单
    setTimeout(() => {
      if (!document.getElementById('root') || document.getElementById('root').children.length === 0) {
        console.log('页面内容为空，尝试创建简单登录表单...');
        createSimpleLoginForm();
      }
    }, 2000);

    setTimeout(optimizeMemory, 3000);
  });
});
