-- 菜单表数据插入语句
-- 使用 UUID 生成主键，所有菜单默认可见

-- 首页/仪表盘
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'guide', '首页', NULL, 1, '/welcome', 'Welcome', 1, 1, 1, 'book', '首页/仪表盘', 'admin', NOW(), 0);

-- 企业管理（父菜单）
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'corp', '企业管理', NULL, 2, '/corp', NULL, 1, 0, 1, 'table', '企业管理', 'admin', NOW(), 0);

-- 获取企业管理菜单ID
SET @corp_id = (SELECT id FROM `sys_menu` WHERE `menu_key` = 'corp' LIMIT 1);

-- 车险台账
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdLedgerList', '车险台账', @corp_id, 1, '/corp/pdLedgerList', 'Corp/PdLedgerList', 1, 1, 1, 'car', '车险台账', 'admin', NOW(), 0);

-- 财险台账
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdInsuranceLedgerList', '财险台账', @corp_id, 2, '/corp/pdInsuranceLedgerList', 'Corp/PdInsuranceLedgerList', 1, 1, 1, 'bank', '财险台账', 'admin', NOW(), 0);

-- 增值服务台账
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdAddedLedgerList', '增值服务台账', @corp_id, 3, '/corp/pdAddedLedgerList', 'Corp/PdAddedLedgerList', 1, 1, 1, 'gift', '增值服务台账', 'admin', NOW(), 0);

-- 信息管理（父菜单）
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'info', '信息管理', NULL, 3, '/info', NULL, 1, 0, 1, 'file', '信息管理', 'admin', NOW(), 0);

-- 获取信息管理菜单ID
SET @info_id = (SELECT id FROM `sys_menu` WHERE `menu_key` = 'info' LIMIT 1);

-- 信息源列表
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdChatSourceList', '信息源列表', @info_id, 1, '/info/pdChatSourceList', 'Info/PdChatSourceList', 1, 1, 1, 'message', '信息源列表', 'admin', NOW(), 0);

-- 信息源详情列表
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdChatSourceDetList', '信息源详情列表', @info_id, 2, '/info/pdChatSourceDetList', 'Info/PdChatSourceDetList', 1, 1, 1, 'file-text', '信息源详情列表', 'admin', NOW(), 0);

-- 任务中心
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdTaskCenter', '任务中心', @info_id, 3, '/info/pdTaskCenter', 'Info/PdTaskCenter', 1, 1, 1, 'dashboard', '任务中心', 'admin', NOW(), 0);

-- 场景库
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdSceneList', '场景库', @info_id, 4, '/info/pdSceneList', 'Info/PdSceneList', 1, 1, 1, 'appstore', '场景库', 'admin', NOW(), 0);

-- 姓名库
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdNameList', '姓名库', @info_id, 5, '/info/pdNameList', 'Info/PdNameList', 1, 1, 1, 'user', '姓名库', 'admin', NOW(), 0);

-- 采集信息
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'pdCarInfo', '采集信息', @info_id, 6, '/info/pdCarInfo', 'Info/PdCarInfo', 1, 1, 1, 'car', '采集信息', 'admin', NOW(), 0);

-- 系统管理（父菜单）
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'system', '系统管理', NULL, 4, '/system', NULL, 1, 0, 1, 'setting', '系统管理', 'admin', NOW(), 0);

-- 获取系统管理菜单ID
SET @system_id = (SELECT id FROM `sys_menu` WHERE `menu_key` = 'system' LIMIT 1);

-- 完成情况
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'completion', '完成情况', @system_id, 1, '/system/completion', 'SystemCompletion', 1, 1, 1, 'check-circle', '完成情况', 'admin', NOW(), 0);

-- 批量系统补全
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'batchCompletion', '批量系统补全', @system_id, 2, '/system/batchCompletion', 'SystemBatchCompletion', 1, 1, 1, 'cloud-upload', '批量系统补全', 'admin', NOW(), 0);

-- 多租户公司配置
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'tenantConfig', '多租户公司配置', @system_id, 3, '/system/tenantConfig', 'TenantConfig', 1, 1, 1, 'team', '多租户公司配置', 'admin', NOW(), 0);

-- 系统配置
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'sysDeployConfig', '系统配置', @system_id, 4, '/system/sysDeployConfig', 'System/SysDeployConfig', 1, 1, 1, 'tool', '系统配置', 'admin', NOW(), 0);

-- 默认链接配置
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'defaultLinkConfig', '默认链接配置', @system_id, 5, '/system/defaultLinkConfig', 'System/DefaultLinkConfig', 1, 1, 1, 'link', '默认链接配置', 'admin', NOW(), 0);

-- 租户管理
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'tenantManage', '租户管理', @system_id, 6, '/system/tenantManage', 'System/TenantManage', 1, 1, 1, 'team', '租户管理', 'admin', NOW(), 0);

-- 格式转换
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'formatConverter', '格式转换', @system_id, 7, '/system/formatConverter', 'System/FormatConverter', 1, 1, 1, 'swap', '格式转换', 'admin', NOW(), 0);

-- 定时任务
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'quartzJob', '定时任务', @system_id, 8, '/system/quartzJob', 'System/QuartzJob', 1, 1, 1, 'clock-circle', '定时任务', 'admin', NOW(), 0);

-- 菜单配置
INSERT INTO `sys_menu` (`id`, `menu_key`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_route`, `is_leaf`, `is_visible`, `icon`, `description`, `create_by`, `create_time`, `del_flag`)
VALUES (REPLACE(UUID(), '-', ''), 'menuConfig', '菜单配置', @system_id, 9, '/system/menuConfig', 'System/MenuConfig', 1, 1, 1, 'menu', '菜单配置', 'admin', NOW(), 0);

-- 根据太一公司的菜单配置，设置默认可见的菜单
UPDATE `sys_menu` SET `is_visible` = 1 WHERE `menu_key` IN (
  'guide', 'corp', 'info', 'system',
  'pdLedgerList', 'pdInsuranceLedgerList', 'pdAddedLedgerList',
  'pdChatSourceList', 'pdChatSourceDetList', 'pdTaskCenter',
  'pdSceneList', 'pdNameList', 'pdCarInfo',
  'completion', 'batchCompletion', 'tenantConfig',
  'sysDeployConfig', 'defaultLinkConfig', 'formatConverter',
  'quartzJob', 'menuConfig', 'tenantManage'
);
