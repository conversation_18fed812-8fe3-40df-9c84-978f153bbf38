<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>每日设置与系统补全</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    }
    body {
      background-color: #f0f2f5;
      padding: 20px;
    }
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      min-height: 700px;
    }
    .sidebar {
      width: 280px;
      border-right: 1px solid #e8e8e8;
      padding: 20px 0;
      background-color: #fafafa;
    }
    .main-content {
      flex: 1;
      padding: 20px;
    }
    .header {
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 15px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .header h1 {
      font-size: 20px;
      color: #333;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 20px;
    }
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      position: relative;
    }
    .tab.active {
      color: #1890ff;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #1890ff;
    }
    .company-list {
      list-style: none;
    }
    .company-item {
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s;
      border-left: 3px solid transparent;
    }
    .company-item:hover {
      background-color: #e6f7ff;
    }
    .company-item.active {
      background-color: #e6f7ff;
      border-left-color: #1890ff;
      color: #1890ff;
    }
    .company-add {
      padding: 12px 20px;
      margin-top: 10px;
      border-top: 1px dashed #e8e8e8;
    }
    .company-add input {
      width: 100%;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    .form-section {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
    }
    .form-section h3 {
      margin-bottom: 15px;
      font-size: 16px;
      color: #333;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .form-section h3 .subtitle {
      font-size: 14px;
      color: #999;
      font-weight: normal;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    .form-control {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      transition: all 0.3s;
    }
    .form-control:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      outline: none;
    }
    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .checkbox-item {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
    .btn {
      padding: 8px 15px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      background-color: #fff;
    }
    .btn-primary {
      background-color: #1890ff;
      color: #fff;
      border-color: #1890ff;
    }
    .btn-primary:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
    .btn-group {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
    .quick-setup {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
      margin-bottom: 20px;
    }
    .setup-card {
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 15px;
      transition: all 0.3s;
    }
    .setup-card:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }
    .setup-card h4 {
      margin-bottom: 10px;
      color: #333;
    }
    .setup-card .value {
      font-size: 24px;
      color: #1890ff;
      margin-bottom: 10px;
    }
    .setup-card .actions {
      display: flex;
      justify-content: space-between;
    }
    .month-selector {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
    }
    .month-btn {
      padding: 6px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      background-color: #fff;
    }
    .month-btn.active {
      background-color: #1890ff;
      color: #fff;
      border-color: #1890ff;
    }
    .ledger-types {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
    .ledger-type-card {
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 15px;
      transition: all 0.3s;
      background-color: #fff;
      overflow: hidden;
      margin-bottom: 0;
    }
    .ledger-type-card:hover {
      border-color: #40a9ff;
      background-color: #e6f7ff;
    }
    .ledger-type-card.selected {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
    .ledger-type-card .checkbox {
      margin-right: 10px;
      display: flex;
      align-items: center;
    }
    .ledger-type-card .info {
      flex: 1;
    }
    .ledger-type-card .title {
      font-weight: 500;
      margin-bottom: 3px;
      color: #333;
    }
    .ledger-type-card .desc {
      font-size: 12px;
      color: #888;
    }
    .search-box {
      padding: 0 20px 15px;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 10px;
    }
    .search-box input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .ledger-header {
      padding: 12px 15px;
      display: flex;
      align-items: center;
      cursor: pointer;
      position: relative;
      transition: background-color 0.3s;
      border-bottom: 1px solid transparent;
    }
    .ledger-header:hover {
      background-color: #f7f7f7;
    }
    .ledger-header.expanded-header {
      border-bottom-color: #e8e8e8;
    }
    .expand-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      transition: transform 0.2s ease-in-out;
      font-style: normal;
      color: #888;
      font-size: 14px;
    }
    .expand-icon.expanded {
      transform: translateY(-50%) rotate(180deg);
    }
    .ledger-settings {
      display: none;
      padding: 20px 15px 15px 40px;
      background: #fafafa;
      animation: slideDown 0.3s ease-out;
    }
    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .settings-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px 20px;
    }
    .form-group {
      margin-bottom: 0;
    }
    .form-group label {
      display: block;
      margin-bottom: 6px;
      color: #555;
      font-size: 13px;
      font-weight: 500;
    }
    .form-control {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 左侧公司列表 -->
    <div class="sidebar">
      <div class="search-box">
        <input type="text" placeholder="搜索公司..." />
      </div>

      <div class="group-management" style="padding: 10px 20px; border-bottom: 1px solid #e8e8e8;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <span style="font-weight: 500;">公司分组</span>
          <button class="btn" style="padding: 2px 8px; font-size: 12px;">新建分组</button>
        </div>
        <select class="form-control" style="margin-bottom: 8px;">
          <option value="all">所有公司</option>
          <option value="car">汽车行业 (25)</option>
          <option value="finance">金融行业 (30)</option>
          <option value="manufacture">制造业 (45)</option>
          <option value="other">其他行业 (15)</option>
        </select>
        <button class="btn btn-primary" style="width: 100%;">应用设置到整个分组</button>
      </div>

      <div class="company-filters" style="padding: 10px 20px; border-bottom: 1px solid #e8e8e8;">
        <div style="margin-bottom: 8px; font-weight: 500;">快速筛选:</div>
        <div class="filter-tags" style="display: flex; flex-wrap: wrap; gap: 8px;">
          <span class="filter-tag" style="padding: 4px 8px; background: #f0f0f0; border-radius: 4px; cursor: pointer;">全部</span>
          <span class="filter-tag" style="padding: 4px 8px; background: #f0f0f0; border-radius: 4px; cursor: pointer;">汽车相关</span>
          <span class="filter-tag" style="padding: 4px 8px; background: #f0f0f0; border-radius: 4px; cursor: pointer;">金融相关</span>
          <span class="filter-tag" style="padding: 4px 8px; background: #f0f0f0; border-radius: 4px; cursor: pointer;">制造业</span>
        </div>
      </div>
      <div class="selection-controls" style="padding: 10px 20px; border-bottom: 1px solid #e8e8e8;">
        <label style="display: flex; align-items: center; margin-bottom: 8px;">
          <input type="checkbox" id="select-all" style="margin-right: 8px;">
          <span>全选</span>
        </label>
        <button class="btn btn-primary" style="width: 100%; margin-top: 5px;">应用到选中公司</button>
      </div>
      <ul class="company-list">
        <li class="company-item active">公司A</li>
        <li class="company-item">公司B</li>
        <li class="company-item">公司C</li>
        <li class="company-item">公司D</li>
        <li class="company-item">公司E</li>
      </ul>
      <div class="company-add">
        <input type="text" placeholder="输入新公司名称" />
        <button class="btn btn-primary" style="width: 100%;">添加新公司</button>
      </div>
    </div>

    <!-- 右侧设置界面 -->
    <div class="main-content">
      <div class="header">
        <h1>公司A - 台账设置</h1>
        <div style="display: flex; align-items: center;">
          <div class="tabs">
            <div class="tab active" data-tab="monthly-settings">每日设置</div>
            <div class="tab" data-tab="system-completion">系统补全</div>
            <div class="tab" data-tab="link-settings">链接设置</div>
            <div class="tab" data-tab="category-settings">分类设置</div>
            <div class="tab" data-tab="system-settings">系统设置</div>
          </div>
          <button class="btn btn-primary" style="margin-left: 15px;">导入配置</button>
        </div>
      </div>

      <!-- Monthly Settings Content -->
      <div class="tab-content active" id="monthly-settings">
        <div class="form-section">
          <h3>每月基础设置</h3>
          <!-- H5 点击数 Range -->
          <div class="form-group" style="margin-top: 15px; background-color: #f0f8ff; padding: 15px; border-radius: 4px; border: 1px solid #cce5ff;">
            <label for="monthly-h5-clicks-min" style="font-size: 15px; font-weight: 600; color: #0056b3; margin-bottom: 10px;">H5 点击数范围 (关键指标)</label>
            <div style="display: flex; gap: 15px; align-items: center;">
              <input type="number" id="monthly-h5-clicks-min" class="form-control" placeholder="最小值" value="100000" style="flex: 1; text-align: center; font-size: 16px;">
              <span style="font-weight: 500; color: #555;">至</span>
              <input type="number" id="monthly-h5-clicks-max" class="form-control" placeholder="最大值" value="150000" style="flex: 1; text-align: center; font-size: 16px;">
            </div>
             <small style="color: #0056b3; font-size: 11px; margin-top: 8px; display: block;">设置每月预期的H5点击总数范围。</small>
          </div>
        </div>
        <!-- 台账类型设置 -->
        <div class="form-section">
          <h3>
            台账类型设置
            <span class="subtitle">选择需要生成的台账类型</span>
          </h3>
          <div class="ledger-types">
            <!-- 车险台账 -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('car-insurance-settings-daily')">
                <div class="checkbox">
                  <input type="checkbox" id="daily-type-1" data-card-id="daily-card-1" checked>
                </div>
                <div class="info">
                  <div class="title">车险台账</div>
                  <div class="desc">包含车辆保险相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="car-insurance-settings-daily" class="ledger-settings">
                <div class="settings-content">
                  <!-- PV/UV/翻页率 Fields Removed -->

                  <!-- Grouped Percentage Section (Restored) -->
                  <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="45">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="55">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                  </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>

            <!-- 财险台账 -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('property-insurance-settings-daily')">
                <div class="checkbox">
                  <input type="checkbox" id="daily-type-2" data-card-id="daily-card-2" checked>
                </div>
                <div class="info">
                  <div class="title">财险台账</div>
                  <div class="desc">包含财产保险相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="property-insurance-settings-daily" class="ledger-settings">
                <div class="settings-content">
                  <!-- PV/UV/翻页率 Fields Removed -->

                  <!-- Grouped Percentage Section (Restored) -->
                  <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                  </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>

            <!-- 增值服务台账 -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('value-added-settings-daily')">
                <div class="checkbox">
                  <input type="checkbox" id="daily-type-3" data-card-id="daily-card-3">
                </div>
                <div class="info">
                  <div class="title">增值服务台账</div>
                  <div class="desc">包含增值服务相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="value-added-settings-daily" class="ledger-settings">
                <div class="settings-content">
                  <!-- PV/UV/翻页率 Fields Removed -->

                  <!-- Grouped Percentage Section (Restored) -->
                  <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="20">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="30">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                  </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加业务城市设置 -->
        <div class="form-section">
          <h3>
            业务城市
            <span class="subtitle">选择业务所在的城市</span>
          </h3>
          <div class="checkbox-group" style="display: flex; flex-wrap: wrap; gap: 15px;">
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">北京</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">上海</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">广州</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">深圳</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">杭州</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">成都</span>
            </label>
          </div>
        </div>

        <div class="btn-group">
          <button class="btn">取消</button>
          <button class="btn btn-primary">保存设置</button>
        </div>
      </div>

      <!-- 系统补全内容 -->
      <div class="tab-content" id="system-completion">
        <!-- 添加数据导入选项 -->
        <div class="form-section">
          <h3>
            数据导入选项
            <span class="subtitle">选择导入方式</span>
          </h3>
          <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <button class="btn btn-primary" style="margin-right: 10px;">导入表格数据</button>
            <span style="color: #666;">上传Excel或CSV文件导入数据</span>
          </div>
        </div>

        <div class="form-section">
          <h3>系统补全设置</h3>
          <div class="form-group">
            <label for="date-range">选择日期范围</label>
            <div style="display: flex; gap: 10px; align-items: center;">
              <input type="date" id="date-start" class="form-control">
              <span>至</span>
              <input type="date" id="date-end" class="form-control">
            </div>
          </div>

          <!-- H5 点击数 (Highlighted) -->
          <div class="form-group" style="margin-top: 15px; background-color: #fffbe6; padding: 15px; border-radius: 4px; border: 1px solid #ffe58f;">
            <label for="completion-h5-clicks" style="font-size: 15px; font-weight: 600; color: #d46b08; margin-bottom: 10px;">H5 点击数 (关键指标)</label>
            <input type="number" id="completion-h5-clicks" class="form-control" value="10000" style="font-size: 16px;">
            <small style="color: #d46b08; font-size: 11px; margin-top: 8px; display: block;">设置本次补全所基于的总H5点击数。</small>
          </div>
        </div>

        <!-- 台账类型选择 -->
        <div class="form-section">
          <h3>
            台账类型选择
            <span class="subtitle">选择需要补全的台账类型及配置</span>
          </h3>
          <div class="ledger-types">
            <!-- 车险台账 (System Completion) -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('car-insurance-settings-completion')">
                <div class="checkbox">
                  <input type="checkbox" id="completion-type-1" data-card-id="completion-card-1" checked>
                </div>
                <div class="info">
                  <div class="title">车险台账</div>
                  <div class="desc">包含车辆保险相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="car-insurance-settings-completion" class="ledger-settings">
                <div class="settings-content">
                   <!-- PV/UV/翻页率 Fields Removed -->

                   <!-- Grouped Percentage Section (Restored) -->
                   <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="45">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="55">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                   </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>

            <!-- 财险台账 (System Completion) -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('property-insurance-settings-completion')">
                <div class="checkbox">
                  <input type="checkbox" id="completion-type-2" data-card-id="completion-card-2">
                </div>
                <div class="info">
                  <div class="title">财险台账</div>
                  <div class="desc">包含财产保险相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="property-insurance-settings-completion" class="ledger-settings">
                <div class="settings-content">
                   <!-- PV/UV/翻页率 Fields Removed -->

                   <!-- Grouped Percentage Section (Restored) -->
                   <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                   </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>

            <!-- 增值服务台账 (System Completion) -->
            <div class="ledger-type-card">
              <div class="ledger-header" onclick="toggleSettings('value-added-settings-completion')">
                <div class="checkbox">
                  <input type="checkbox" id="completion-type-3" data-card-id="completion-card-3">
                </div>
                <div class="info">
                  <div class="title">增值服务台账</div>
                  <div class="desc">包含增值服务相关记录</div>
                </div>
                <i class="expand-icon">▼</i>
              </div>
              <div id="value-added-settings-completion" class="ledger-settings">
                <div class="settings-content">
                   <!-- PV/UV/翻页率 Fields Removed -->

                   <!-- Grouped Percentage Section (Restored) -->
                   <div class="percentage-group" style="grid-column: 1 / -1; display: flex; flex-direction: column; gap: 15px; border-top: 1px solid #ddd; padding-top: 15px; margin-top: 10px;">
                     <div class="form-group">
                        <label>台账数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="20">
                           <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="30">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">此类型台账数占总H5点击数的比例范围</small>
                     </div>
                     <div class="form-group">
                        <label>聊天用户数占比区间 (%):</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                           <input type="number" class="form-control" placeholder="最小值" style="width: 80px;" value="25">
                            <span>至</span>
                           <input type="number" class="form-control" placeholder="最大值" style="width: 80px;" value="35">
                           <span>%</span>
                        </div>
                        <small style="color: #888; font-size: 11px; margin-top: 3px; display: block;">聊天用户数占此类型台账数的比例范围</small>
                     </div>
                   </div>
                   <!-- End Grouped Percentage Section -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加业务城市设置 -->
        <div class="form-section">
          <h3>
            业务城市
            <span class="subtitle">选择需要补全数据的城市</span>
          </h3>
          <div class="checkbox-group" style="display: flex; flex-wrap: wrap; gap: 15px;">
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">北京</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">上海</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">广州</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" checked>
              <span style="margin-left: 8px;">深圳</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">杭州</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox">
              <span style="margin-left: 8px;">成都</span>
            </label>
          </div>
        </div>

        <div class="btn-group">
          <button class="btn">取消</button>
          <button class="btn btn-primary">开始补全</button>
        </div>
      </div>

      <!-- Add content container for "链接设置" -->
      <div class="tab-content" id="link-settings">
        <div class="form-section">
          <h3>链接设置 <span class="subtitle">为不同链接类型配置参数</span></h3>
          <div class="ledger-types" style="grid-template-columns: 1fr; gap: 20px;">

            <div class="ledger-type-card" style="padding: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #eee;">
                 <h4 style="margin: 0; font-size: 16px;">类型: 车险</h4>
              </div>
              <div class="settings-content" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px 25px;">
                <div class="form-group">
                  <label>PV 区间:</label>
                  <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="10000">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="20000">
                  </div>
                </div>
                <div class="form-group">
                  <label>UV 区间比例 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="30">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="40">
                      <span>%</span>
                   </div>
                </div>
                <div class="form-group">
                   <label>合作公司区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="50">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="60">
                   </div>
                </div>
                <div class="form-group">
                   <label>运营指标区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="1000">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="2000">
                   </div>
                </div>
                 <div class="form-group" style="grid-column: 1 / span 1;">
                   <label>翻页率区间 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="10">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="20">
                     <span>%</span>
                   </div>
                 </div>
              </div>
            </div>

            <div class="ledger-type-card" style="padding: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #eee;">
                 <h4 style="margin: 0; font-size: 16px;">类型: 财险</h4>
              </div>
              <div class="settings-content" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px 25px;">
                 <div class="form-group">
                  <label>PV 区间:</label>
                  <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="8000">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="15000">
                  </div>
                </div>
                <div class="form-group">
                  <label>UV 区间比例 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="25">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="35">
                      <span>%</span>
                   </div>
                </div>
                <div class="form-group">
                   <label>合作公司区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="40">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="55">
                   </div>
                </div>
                <div class="form-group">
                   <label>运营指标区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="900">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="1800">
                   </div>
                </div>
                 <div class="form-group" style="grid-column: 1 / span 1;">
                   <label>翻页率区间 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="8">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="18">
                     <span>%</span>
                   </div>
                 </div>
              </div>
            </div>

            <div class="ledger-type-card" style="padding: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #eee;">
                 <h4 style="margin: 0; font-size: 16px;">类型: 增值服务</h4>
              </div>
              <div class="settings-content" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px 25px;">
                 <div class="form-group">
                  <label>PV 区间:</label>
                  <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="5000">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="12000">
                  </div>
                </div>
                <div class="form-group">
                  <label>UV 区间比例 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="20">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="30">
                      <span>%</span>
                   </div>
                </div>
                <div class="form-group">
                   <label>合作公司区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="30">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="50">
                   </div>
                </div>
                <div class="form-group">
                   <label>运营指标区间:</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="500">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="1500">
                   </div>
                </div>
                 <div class="form-group" style="grid-column: 1 / span 1;">
                   <label>翻页率区间 (%):</label>
                   <div style="display: flex; gap: 10px; align-items: center;">
                     <input type="number" class="form-control" placeholder="最小值" value="5">
                     <span>至</span>
                     <input type="number" class="form-control" placeholder="最大值" value="15">
                     <span>%</span>
                   </div>
                 </div>
              </div>
            </div>

            <div class="ledger-type-card" style="padding: 20px; background-color: #f9f9f9; border-style: dashed;">
                <button class="btn" style="width: 100%; padding: 10px;">+ 添加新链接类型</button>
            </div>

          </div>
        </div>
        <div class="btn-group">
          <button class="btn">取消</button>
          <button class="btn btn-primary">保存链接设置</button>
        </div>
      </div>

      <div class="tab-content" id="category-settings">
        <div class="form-section">
          <h3>分类设置 <span class="subtitle">为当前公司设置分类信息</span></h3>
           <div style="max-width: 600px;">
             <div class="form-group">
               <label for="company-category">公司分类</label>
               <select id="company-category" class="form-control">
                 <option value="">选择分类...</option>
                 <option value="car" selected>汽车行业</option>
                 <option value="finance">金融行业</option>
                 <option value="manufacture">制造业</option>
                 <option value="tech">科技</option>
                 <option value="other">其他</option>
               </select>
             </div>
             <div class="form-group">
               <label for="company-intro">公司简介</label>
               <textarea id="company-intro" class="form-control" rows="4" placeholder="输入公司简介..."></textarea>
             </div>
              <div class="form-group">
               <label for="company-logo">公司Logo</label>
               <div style="border: 1px dashed #d9d9d9; padding: 20px; text-align: center; background-color: #fafafa;">
                 <p style="margin-bottom: 10px; color: #888;">点击或拖拽上传Logo</p>
                 <button class="btn">选择文件</button>
                 <input type="file" id="company-logo" style="display: none;">
                 <img src="placeholder-logo.png" alt="Current Logo" style="max-width: 100px; max-height: 50px; margin-top: 10px; display: block; margin-left: auto; margin-right: auto;" />
               </div>
             </div>
           </div>
        </div>
        <div class="btn-group">
          <button class="btn">取消</button>
          <button class="btn btn-primary">保存分类设置</button>
        </div>
      </div>

      <!-- 系统设置内容 -->
      <div class="tab-content" id="system-settings">
        <div class="form-section" style="background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
          <h3 style="color: #1890ff; border-bottom: 1px solid #e8e8e8; padding-bottom: 12px;">
            <i class="fas fa-cog" style="margin-right: 8px;"></i>基础配置
          </h3>
          <p style="margin-bottom: 20px; color: #666;">以下配置用于设置系统的基本功能和显示方式</p>

          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                <span style="font-weight: 500; color: #333;">姓名切换为先生/女士</span>
                <div style="position: relative; display: inline-block;">
                  <input type="checkbox" id="nameSwitch" style="transform: scale(1.3); cursor: pointer;">
                </div>
              </label>
              <small style="color: #888; font-size: 12px; display: block;">开启后，系统将自动将姓名替换为先生/女士称呼</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                <span style="font-weight: 500; color: #333;">车牌号码脱敏</span>
                <div style="position: relative; display: inline-block;">
                  <input type="checkbox" id="plateNoMask" style="transform: scale(1.3); cursor: pointer;">
                </div>
              </label>
              <small style="color: #888; font-size: 12px; display: block;">开启后，系统将对车牌号码进行脱敏处理，如：粤A****8</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                <span style="font-weight: 500; color: #333;">车架号脱敏</span>
                <div style="position: relative; display: inline-block;">
                  <input type="checkbox" id="vinNoMask" style="transform: scale(1.3); cursor: pointer;">
                </div>
              </label>
              <small style="color: #888; font-size: 12px; display: block;">开启后，系统将对车架号进行脱敏处理，如：LSVA****9876</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                <span style="font-weight: 500; color: #333;">手机号脱敏</span>
                <div style="position: relative; display: inline-block;">
                  <input type="checkbox" id="phoneSwitch" style="transform: scale(1.3); cursor: pointer;">
                </div>
              </label>
              <small style="color: #888; font-size: 12px; display: block;">开启后，系统将对手机号进行脱敏处理，如：138****8888</small>
            </div>
          </div>

          <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); margin-top: 20px;">
            <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: block;">
              以上数据实现方式 <i class="fas fa-question-circle" style="color: #999; font-size: 14px;"></i>
            </label>
            <div style="margin-top: 8px; display: flex; align-items: center; justify-content: space-between;">
              <div style="display: flex; gap: 20px;">
                <label style="display: flex; align-items: center; cursor: pointer;">
                  <input type="radio" name="queryType" value="db" checked style="margin-right: 8px;">
                  <span>数据库存储</span>
                </label>
                <label style="display: flex; align-items: center; cursor: pointer;">
                  <input type="radio" name="queryType" value="query" style="margin-right: 8px;">
                  <span>查询实现脱敏</span>
                </label>
              </div>
              <div style="position: relative; display: inline-block;">
                <input type="checkbox" id="queryTypeSwitch" style="transform: scale(1.3); cursor: pointer;">
              </div>
            </div>
            <small style="color: #888; font-size: 12px; margin-top: 8px; display: block; padding: 8px; background-color: #fffbe6; border-radius: 4px; border-left: 3px solid #faad14;">
              <i class="fas fa-info-circle" style="color: #faad14; margin-right: 5px;"></i>
              说明：数据库存储后期无法还原数据
            </small>
          </div>

          <div class="form-group" id="removeQueryMaskGroup" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); margin-top: 20px; display: none;">
            <label style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
              <span style="font-weight: 500; color: #333;">去除查询脱敏 <i class="fas fa-question-circle" style="color: #999; font-size: 14px;"></i></span>
              <div style="position: relative; display: inline-block;">
                <input type="checkbox" id="removeQueryMask" style="transform: scale(1.3); cursor: pointer;">
              </div>
            </label>
            <small style="color: #888; font-size: 12px; display: block; padding: 8px; background-color: #f6ffed; border-radius: 4px; border-left: 3px solid #52c41a;">
              <i class="fas fa-info-circle" style="color: #52c41a; margin-right: 5px;"></i>
              说明：需要为查询实现脱敏，已存储为脱敏数据的无法实现
            </small>
          </div>

          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 20px;">
            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: block;">
                客服IP来源 <i class="fas fa-question-circle" style="color: #999; font-size: 14px;"></i>
              </label>
              <select class="form-control" id="serviceIpType" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                <option value="server">服务器IP</option>
                <option value="tenant">租户设置中IP(若租户IP为空取服务器IP)</option>
              </select>
              <small style="color: #888; font-size: 12px; margin-top: 8px; display: block;">选择客服IP的来源方式</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
              <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: block;">服务器IP</label>
              <input type="text" class="form-control" id="serverIp" placeholder="请输入服务器IP地址" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
              <small style="color: #888; font-size: 12px; margin-top: 8px; display: block;">设置服务器的IP地址，当选择租户IP且租户IP为空时使用</small>
            </div>
          </div>
        </div>

        <div class="form-section" style="background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); margin-top: 25px;">
          <h3 style="color: #1890ff; border-bottom: 1px solid #e8e8e8; padding-bottom: 12px;">
            <i class="fas fa-comments" style="margin-right: 8px;"></i>聊天源配置
          </h3>
          <p style="margin-bottom: 20px; color: #666;">以下配置用于设置不同业务类型的聊天源数量，需要确保场景库中有足够的场景数量</p>

          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); border-top: 3px solid #1890ff;">
              <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: flex; align-items: center;">
                <i class="fa fa-car" style="color: #1890ff; margin-right: 8px;"></i>
                车险聊天源总条数
                <i class="fa fa-question-circle" style="color: #999; font-size: 14px; margin-left: 5px;"></i>
              </label>
              <input type="number" class="form-control" id="carInsuranceCount" min="0" value="0" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
              <small style="color: #888; font-size: 12px; margin-top: 8px; display: block;">填写数量，系统将会生成对应的条数，需要场景库中相符</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); border-top: 3px solid #52c41a;">
              <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: flex; align-items: center;">
                <i class="fa fa-bank" style="color: #52c41a; margin-right: 8px;"></i>
                财险聊天源总条数
                <i class="fa fa-question-circle" style="color: #999; font-size: 14px; margin-left: 5px;"></i>
              </label>
              <input type="number" class="form-control" id="propertyInsuranceCount" min="0" value="0" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
              <small style="color: #888; font-size: 12px; margin-top: 8px; display: block;">填写数量，系统将会生成对应的条数，需要场景库中相符</small>
            </div>

            <div class="form-group" style="background-color: #fff; padding: 15px; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); border-top: 3px solid #fa8c16;">
              <label style="font-weight: 500; color: #333; margin-bottom: 10px; display: flex; align-items: center;">
                <i class="fa fa-gift" style="color: #fa8c16; margin-right: 8px;"></i>
                增值服务聊天源总条数
                <i class="fa fa-question-circle" style="color: #999; font-size: 14px; margin-left: 5px;"></i>
              </label>
              <input type="number" class="form-control" id="valueAddedServiceCount" min="0" value="0" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
              <small style="color: #888; font-size: 12px; margin-top: 8px; display: block;">填写数量，系统将会生成对应的条数，需要场景库中相符</small>
            </div>
          </div>

          <div style="background-color: #e6f7ff; border-radius: 4px; padding: 12px; margin-top: 20px; display: flex; align-items: flex-start; border-left: 3px solid #1890ff;">
            <i class="fa fa-info-circle" style="color: #1890ff; margin-right: 10px; margin-top: 2px;"></i>
            <div>
              <div style="font-weight: 500; color: #333; margin-bottom: 5px;">提示</div>
              <div style="color: #666; font-size: 12px;">请确保场景库中有足够的场景数量，否则可能导致生成的聊天记录不完整。建议定期检查场景库数量与此处配置是否匹配。</div>
            </div>
          </div>
        </div>



        <div style="margin-top: 25px; display: flex; justify-content: flex-end; gap: 15px;">
          <button class="btn" style="padding: 10px 20px; border-radius: 4px; border: 1px solid #d9d9d9; background-color: #fff; cursor: pointer; font-size: 14px; transition: all 0.3s;">取消</button>
          <button class="btn btn-primary" style="padding: 10px 25px; border-radius: 4px; border: none; background-color: #1890ff; color: white; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s; display: flex; align-items: center; box-shadow: 0 2px 0 rgba(0,0,0,0.045);">
            <i class="fa fa-save" style="margin-right: 8px;"></i>
            保存配置
          </button>
        </div>

        <div style="margin-top: 15px; padding: 12px; background-color: #fffbe6; border-radius: 4px; border-left: 3px solid #faad14; color: #666; font-size: 12px; display: flex; align-items: center;">
          <i class="fa fa-exclamation-circle" style="color: #faad14; margin-right: 10px; font-size: 16px;"></i>
          注意：配置保存后需要重启系统才能生效
        </div>
      </div>

    </div>
  </div>

  <script>
    // 标签切换功能
    document.addEventListener('DOMContentLoaded', function() {
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有标签和内容的active类
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
          document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

          // 添加当前标签和对应内容的active类
          this.classList.add('active');
          const tabId = this.getAttribute('data-tab');
          // Check if element exists before adding class
          const contentElement = document.getElementById(tabId);
          if (contentElement) {
              contentElement.classList.add('active');
          } else {
              console.error("Tab content not found for ID:", tabId); // Add error handling
          }
        });
      });

      // 月份按钮点击事件
      const monthBtns = document.querySelectorAll('.month-btn');
      monthBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          monthBtns.forEach(b => b.classList.remove('active'));
          this.classList.add('active');
        });
      });

      // 台账类型卡片点击事件
      const ledgerCards = document.querySelectorAll('.ledger-type-card');
      ledgerCards.forEach(card => {
        card.addEventListener('click', function() {
          const checkbox = this.querySelector('input[type="checkbox"]');
          checkbox.checked = !checkbox.checked;
          this.classList.toggle('selected', checkbox.checked);
        });
      });

      // 公司列表点击事件
      const companyItems = document.querySelectorAll('.company-item');
      companyItems.forEach(item => {
        item.addEventListener('click', function() {
          companyItems.forEach(i => i.classList.remove('active'));
          this.classList.add('active');
          document.querySelector('.header h1').textContent = this.textContent + ' - 台账设置';
        });
      });
    });

    function toggleSettings(settingsId) {
      const settings = document.getElementById(settingsId);
      const header = settings.previousElementSibling;
      const icon = header.querySelector('.expand-icon');

      if (settings.style.display === 'none' || !settings.style.display) {
        settings.style.display = 'block';
        icon.classList.add('expanded');
        header.classList.add('expanded-header');
      } else {
        settings.style.display = 'none';
        icon.classList.remove('expanded');
        header.classList.remove('expanded-header');
      }
    }

    // Handle checkbox clicks and selection state
    document.querySelectorAll('.checkbox input[type="checkbox"]').forEach(checkbox => {
      const card = checkbox.closest('.ledger-type-card');
      if (checkbox.checked) {
        card.classList.add('selected');
      } else {
         card.classList.remove('selected');
      }

      checkbox.addEventListener('click', (e) => {
        e.stopPropagation();
        const card = e.target.closest('.ledger-type-card');
        card.classList.toggle('selected', e.target.checked);
      });
    });

    // Initialize all settings panels to be collapsed
    document.querySelectorAll('.ledger-settings').forEach(panel => {
       panel.style.display = 'none';
       // Ensure header and icon are also in collapsed state initially
       const header = panel.previousElementSibling;
       const icon = header.querySelector('.expand-icon');
       icon.classList.remove('expanded');
       header.classList.remove('expanded-header');
    });

    // 系统设置页面的交互功能
    document.addEventListener('DOMContentLoaded', function() {
      // 处理查询类型切换
      const queryTypeRadios = document.querySelectorAll('input[name="queryType"]');
      const queryTypeSwitch = document.getElementById('queryTypeSwitch');
      const removeQueryMaskGroup = document.getElementById('removeQueryMaskGroup');

      // 初始化开关状态
      queryTypeSwitch.checked = queryTypeRadios[1].checked;

      // 单选按钮变化时更新开关状态
      queryTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
          if (this.value === 'query') {
            removeQueryMaskGroup.style.display = 'block';
            queryTypeSwitch.checked = true;
          } else {
            removeQueryMaskGroup.style.display = 'none';
            queryTypeSwitch.checked = false;
          }
        });
      });

      // 开关变化时更新单选按钮状态
      queryTypeSwitch.addEventListener('change', function() {
        if (this.checked) {
          queryTypeRadios[1].checked = true; // 查询实现脱敏
          removeQueryMaskGroup.style.display = 'block';
        } else {
          queryTypeRadios[0].checked = true; // 数据库存储
          removeQueryMaskGroup.style.display = 'none';
        }
      });

      // 移除了主题色选择相关代码

      // 移除了开关样式相关代码

      // 保存配置按钮点击事件
      const saveConfigBtn = document.querySelector('#system-settings .btn-primary');
      if (saveConfigBtn) {
        saveConfigBtn.addEventListener('click', function() {
          // 这里可以添加保存配置的逻辑
          alert('系统配置保存成功，配置将在系统重启后生效');
        });
      }
    });
  </script>
</body>
</html>