{"name": "太一企业", "version": "1.0.0", "private": true, "description": "太一企业管理系统", "author": "福州太一市企业管理有限公司 <info@太一企业.com>", "repository": "**************:ant-design/ant-design-pro.git", "main": "main.js", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "node scripts/clean-build.js && cross-env NODE_ENV=production max build && node scripts/fix-html-for-production.js production", "build:dev": "node scripts/clean-build.js && cross-env NODE_ENV=development max build", "build:taiyi": "node scripts/build-company.js taiyi production", "build:xudong": "node scripts/build-company.js xudong production", "build:taiyi:test": "node scripts/build-company.js taiyi test", "build:xudong:test": "node scripts/build-company.js xudong test", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env NODE_ENV=development UMI_ENV=dev max dev", "start:dev": "cross-env NODE_ENV=development REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env NODE_ENV=development MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env NODE_ENV=production REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env NODE_ENV=development REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "start:taiyi": "node scripts/start-company.js taiyi", "start:xudong": "node scripts/start-company.js xudong", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit", "electron": "electron .", "electron:dev": "concurrently \"npm run start:dev\" \"wait-on http://localhost:8000 && electron .\"", "optimize-assets": "node scripts/optimize-assets.js", "dist:win": "npm run build && npm run optimize-assets && electron-builder --win --publish=never", "dist:mac": "npm run build && npm run optimize-assets && electron-builder --mac --publish=never", "dist:win:taiyi": "npm run build:taiyi && npm run optimize-assets && electron-builder --win --publish=never", "dist:mac:taiyi": "npm run build:taiyi && npm run optimize-assets && electron-builder --mac --publish=never", "dist:win:xudong": "npm run build:xudong && npm run optimize-assets && electron-builder --win --publish=never", "dist:mac:xudong": "npm run build:xudong && npm run optimize-assets && electron-builder --mac --publish=never", "dist:win:taiyi:test": "npm run build:taiyi:test && npm run optimize-assets && electron-builder --win --publish=never", "dist:mac:taiyi:test": "npm run build:taiyi:test && npm run optimize-assets && electron-builder --mac --publish=never", "dist:win:xudong:test": "npm run build:xudong:test && npm run optimize-assets && electron-builder --win --publish=never", "dist:mac:xudong:test": "npm run build:xudong:test && npm run optimize-assets && electron-builder --mac --publish=never"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "^2.7.19", "@types/react-dnd": "^3.0.2", "antd": "^5.21.2", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "querystring": "^0.2.1", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-json-pretty": "^2.2.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@electron/rebuild": "^3.7.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^5.0.0", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.3.24", "@umijs/max": "^4.3.24", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^35.2.0", "electron-builder": "^26.0.12", "eslint": "^8.57.1", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.6", "imagemin": "^8.0.1", "imagemin-jpegtran": "^7.0.0", "imagemin-pngquant": "^9.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11", "wait-on": "^7.2.0"}, "engines": {"node": ">=12.0.0"}, "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless"], "ignore": [".dockerignore", ".git", ".github", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "CNAME", "create-umi"]}, "build": {"appId": "com.太一企业.enterprise", "productName": "太一企业管理系统", "copyright": "Copyright © 2024 福州太一市企业管理有限公司", "files": ["dist/**/*", "production/**/*", "electron/**/*", "main.js", "package.json"], "extraResources": [{"from": "electron", "to": "electron"}, {"from": "dist/scripts", "to": "dist/scripts"}, {"from": "production/scripts", "to": "production/scripts"}], "directories": {"output": "release"}, "mac": {"category": "public.app-category.business", "icon": "public/resource/applogo/icon.png", "target": ["dmg"]}, "win": {"icon": "public/resource/applogo/icon.png", "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}