/**
 * 清理构建文件脚本
 * 确保每次构建前清理旧的文件，避免缓存问题
 */
const fs = require('fs');
const path = require('path');

// 需要清理的目录列表
const dirsToClean = ['dist', 'production'];

// 清理目录函数
function cleanDirectory(dirPath) {
  const fullPath = path.resolve(process.cwd(), dirPath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`目录不存在，跳过清理: ${dirPath}`);
    return;
  }

  console.log(`开始清理目录: ${dirPath}`);
  
  try {
    // 递归删除目录内容
    const files = fs.readdirSync(fullPath);
    
    for (const file of files) {
      const filePath = path.join(fullPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // 递归删除子目录
        fs.rmSync(filePath, { recursive: true, force: true });
      } else {
        // 删除文件
        fs.unlinkSync(filePath);
      }
    }
    
    console.log(`✓ 成功清理目录: ${dirPath}`);
  } catch (error) {
    console.error(`清理目录失败 ${dirPath}:`, error.message);
  }
}

// 主函数
function main() {
  console.log('开始清理构建文件...');
  
  // 清理所有指定目录
  dirsToClean.forEach(cleanDirectory);
  
  console.log('构建文件清理完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { cleanDirectory, main };
