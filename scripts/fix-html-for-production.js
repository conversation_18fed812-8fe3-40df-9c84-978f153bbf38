/**
 * 为生产环境或测试环境修复HTML文件
 */
const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);
const envType = args[0] || 'production'; // 默认为生产环境，可选值：production, test

// 确定构建输出目录
// 在Jenkins环境中，输出目录可能是'production'，在本地环境中通常是'dist'
let distDir = 'dist';

// 如果提供了第二个参数作为目录
if (args[1]) {
  distDir = args[1];
  console.log(`使用指定的输出目录: ${distDir}`);
} else {
  // 检查是否在Jenkins环境中
  const isJenkins = process.env.JENKINS_URL || process.env.JENKINS_HOME;
  // 检查production目录是否存在
  const productionDir = path.resolve(process.cwd(), 'production');
  if (isJenkins && fs.existsSync(productionDir)) {
    distDir = 'production';
    console.log('检测到Jenkins环境，使用production目录');
  }
}

// 检查指定的目录是否存在
const outputDir = path.resolve(process.cwd(), distDir);
if (!fs.existsSync(outputDir)) {
  console.log(`警告: 指定的输出目录 ${distDir} 不存在，尝试查找其他可能的目录...`);

  // 尝试查找其他可能的目录
  const possibleDirs = ['dist', 'production', 'build', 'out'];
  for (const dir of possibleDirs) {
    if (dir !== distDir && fs.existsSync(path.resolve(process.cwd(), dir))) {
      distDir = dir;
      console.log(`找到可用的输出目录: ${distDir}`);
      break;
    }
  }
}

// 读取环境变量
const envFile = path.resolve(process.cwd(), `.env.${envType}`);
const envContent = fs.readFileSync(envFile, 'utf8');

// 解析环境变量
const envVars = {};
envContent.split('\n').forEach(line => {
  if (line && !line.startsWith('#')) {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  }
});

// 添加当前环境类型
envVars.NODE_ENV = envType;

console.log('注入环境变量:', JSON.stringify(envVars, null, 2));

// 读取HTML文件
const htmlFile = path.resolve(process.cwd(), distDir, 'index.html');
if (!fs.existsSync(htmlFile)) {
  console.error('HTML文件不存在:', htmlFile);

  // 列出当前目录和输出目录的内容，帮助调试
  console.log('当前工作目录:', process.cwd());
  console.log('当前目录内容:');
  try {
    const currentDirFiles = fs.readdirSync(process.cwd());
    console.log(currentDirFiles);
  } catch (err) {
    console.error('无法读取当前目录:', err);
  }

  console.log(`${distDir}目录内容:`);
  try {
    const distDirPath = path.resolve(process.cwd(), distDir);
    if (fs.existsSync(distDirPath)) {
      const distDirFiles = fs.readdirSync(distDirPath);
      console.log(distDirFiles);

      // 尝试查找任何HTML文件
      const htmlFiles = distDirFiles.filter(file => file.endsWith('.html'));
      if (htmlFiles.length > 0) {
        console.log(`找到其他HTML文件: ${htmlFiles.join(', ')}`);
        // 使用找到的第一个HTML文件
        const alternativeHtmlFile = path.resolve(process.cwd(), distDir, htmlFiles[0]);
        console.log(`使用替代HTML文件: ${alternativeHtmlFile}`);
        const htmlContent = fs.readFileSync(alternativeHtmlFile, 'utf8');
        // 创建index.html
        fs.writeFileSync(htmlFile, htmlContent, 'utf8');
        console.log(`已创建index.html文件`);
      } else {
        console.error(`${distDir}目录中没有找到任何HTML文件`);
        process.exit(1);
      }
    } else {
      console.error(`${distDir}目录不存在`);
      process.exit(1);
    }
  } catch (err) {
    console.error(`无法读取${distDir}目录:`, err);
    process.exit(1);
  }
}

let htmlContent = fs.readFileSync(htmlFile, 'utf8');

// 创建环境变量注入脚本
const envScript = `
<script>
  window.__PRODUCTION_ENV__ = ${JSON.stringify(envVars)};
  console.log('${envType}环境变量已加载:', window.__PRODUCTION_ENV__);

  // 强制清理缓存的脚本
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister();
      }
    });
  }

  // 清理应用缓存
  if ('caches' in window) {
    caches.keys().then(function(names) {
      for (let name of names) {
        caches.delete(name);
      }
    });
  }
</script>
`;

// 创建处理客户端路由的脚本
const routerScript = `
<script>
  // 处理客户端路由
  (function() {
    // 如果当前URL不是根路径，且不包含文件扩展名，则重定向到根路径
    var pathname = window.location.pathname;
    var isFile = /\\.\\w+$/.test(pathname);

    if (pathname !== '/' && !isFile) {
      // 保存当前路径到sessionStorage
      sessionStorage.setItem('redirectPath', pathname);
      // 重定向到根路径
      window.location.replace(window.location.origin);
    }

    // 如果是从重定向过来的，恢复路径
    if (pathname === '/' || pathname === '') {
      var redirectPath = sessionStorage.getItem('redirectPath');
      if (redirectPath) {
        sessionStorage.removeItem('redirectPath');
        // 使用history API更新路径，不刷新页面
        window.history.replaceState(null, '', redirectPath);
      }
    }
  })();
</script>
`;

// 确保scripts目录存在
const scriptsDir = path.resolve(process.cwd(), distDir, 'scripts');
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir, { recursive: true });
  console.log('创建scripts目录:', scriptsDir);
}

// 创建loading.js文件
const loadingJsContent = `
// 页面加载脚本
(function() {
  // 创建加载动画
  function createLoadingAnimation() {
    var loadingContainer = document.createElement('div');
    loadingContainer.id = 'app-loading-container';
    loadingContainer.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:#f0f2f5;z-index:9999;';

    var loadingContent = document.createElement('div');
    loadingContent.style.cssText = 'text-align:center;';

    var spinner = document.createElement('div');
    spinner.style.cssText = 'width:50px;height:50px;border:3px solid rgba(24,144,255,0.2);border-radius:50%;border-top-color:#1890ff;animation:spin 1s linear infinite;margin:0 auto 20px;';

    var loadingText = document.createElement('div');
    loadingText.textContent = '加载中...';
    loadingText.style.cssText = 'color:#1890ff;font-size:16px;';

    loadingContent.appendChild(spinner);
    loadingContent.appendChild(loadingText);
    loadingContainer.appendChild(loadingContent);

    // 添加动画样式
    var style = document.createElement('style');
    style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    document.head.appendChild(style);

    document.body.appendChild(loadingContainer);

    return loadingContainer;
  }

  // 在DOM加载完成后显示加载动画
  document.addEventListener('DOMContentLoaded', function() {
    var loadingElement = createLoadingAnimation();

    // 在页面完全加载后移除加载动画
    window.addEventListener('load', function() {
      setTimeout(function() {
        if (loadingElement && loadingElement.parentNode) {
          loadingElement.parentNode.removeChild(loadingElement);
        }
      }, 500); // 延迟500ms移除，使过渡更平滑
    });
  });

  // 修复资源路径问题
  function fixResourcePaths() {
    // 修复CSS中的URL路径
    var allLinks = document.querySelectorAll('link[rel="stylesheet"]');
    allLinks.forEach(function(link) {
      if (link.href && link.href.startsWith('/')) {
        link.href = '.' + link.href;
      }
    });

    // 修复脚本路径
    var allScripts = document.querySelectorAll('script[src]');
    allScripts.forEach(function(script) {
      if (script.src && script.src.startsWith('/')) {
        script.src = '.' + script.src;
      }
    });

    // 修复图片路径
    var allImages = document.querySelectorAll('img[src]');
    allImages.forEach(function(img) {
      if (img.src && img.src.startsWith('/')) {
        img.src = '.' + img.src;
      }
    });
  }

  // 在DOM加载完成后修复资源路径
  document.addEventListener('DOMContentLoaded', fixResourcePaths);
})();
`;

const loadingJsPath = path.join(scriptsDir, 'loading.js');
fs.writeFileSync(loadingJsPath, loadingJsContent, 'utf8');
console.log('loading.js脚本已创建:', loadingJsPath);

// 修改HTML内容
// 1. 替换所有以 / 开头的资源路径为相对路径
htmlContent = htmlContent.replace(/href="\//g, 'href="./');
htmlContent = htmlContent.replace(/src="\//g, 'src="./');

// 1.1 为所有JS和CSS文件添加时间戳参数来强制刷新缓存
const timestamp = Date.now();
console.log(`添加时间戳参数: v=${timestamp}`);
htmlContent = htmlContent.replace(/href="\.\/([^"?]+\.css)"/g, `href="./$1?v=${timestamp}"`);
htmlContent = htmlContent.replace(/src="\.\/([^"?]+\.js)"/g, `src="./$1?v=${timestamp}"`);

// 2. 添加 base 标签确保所有相对路径都基于根目录
if (!htmlContent.includes('<base href=')) {
  htmlContent = htmlContent.replace('<head>', '<head>\n  <base href="./">');
}

// 2.1 添加防缓存的meta标签
const cacheControlMeta = `
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">`;

if (!htmlContent.includes('Cache-Control')) {
  htmlContent = htmlContent.replace('<base href="./">', `<base href="./">${cacheControlMeta}`);
}

// 3. 添加环境变量脚本
if (htmlContent.includes('</head>')) {
  htmlContent = htmlContent.replace('</head>', `${envScript}${routerScript}</head>`);
} else {
  console.error('无法找到</head>标签');
  process.exit(1);
}

// 4. 确保loading.js脚本被正确引用
if (!htmlContent.includes('scripts/loading.js')) {
  const scriptTag = '<script async src="./scripts/loading.js"></script>';
  htmlContent = htmlContent.replace('<base href="./">', `<base href="./">\n  ${scriptTag}`);
}

// 写入修改后的HTML文件
fs.writeFileSync(htmlFile, htmlContent, 'utf8');
console.log('HTML文件已成功修复');

// 创建一个404.html文件，内容与index.html相同，用于处理直接访问子路由的情况
const notFoundFile = path.resolve(process.cwd(), distDir, '404.html');
fs.writeFileSync(notFoundFile, htmlContent, 'utf8');
console.log('404.html文件已创建，用于处理直接访问子路由的情况');
