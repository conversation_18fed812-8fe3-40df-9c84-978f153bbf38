const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

// 尝试导入 imagemin 及其插件
let imagemin, imageminJpegtran, imageminPngquant;
try {
  imagemin = require('imagemin');
  imageminJpegtran = require('imagemin-jpegtran');
  imageminPngquant = require('imagemin-pngquant');
} catch (error) {
  console.warn('警告: imagemin 相关依赖未安装，图片优化将被跳过');
}

// 优化图片函数
async function optimizeImages(directory) {
  if (!imagemin || !imageminJpegtran || !imageminPngquant) {
    console.log(`跳过 ${directory} 目录中的图片优化（缺少依赖）`);
    return;
  }

  console.log(`正在优化 ${directory} 目录中的图片...`);
  
  // 查找所有图片文件
  const files = fs.readdirSync(directory, { withFileTypes: true });
  const imageFiles = files
    .filter(file => !file.isDirectory() && /\.(jpe?g|png|gif)$/i.test(file.name))
    .map(file => path.join(directory, file.name));
  
  console.log(`找到 ${imageFiles.length} 个图片文件`);
  
  // 跳过小于 10KB 的文件
  const filesToOptimize = [];
  for (const file of imageFiles) {
    const stats = fs.statSync(file);
    if (stats.size < 10 * 1024) {
      console.log(`跳过小文件: ${file}`);
    } else {
      filesToOptimize.push(file);
    }
  }
  
  if (filesToOptimize.length === 0) {
    console.log('没有需要优化的图片');
    return;
  }
  
  // 优化图片
  try {
    const optimizedFiles = await imagemin(filesToOptimize, {
      destination: directory,
      plugins: [
        imageminJpegtran(),
        imageminPngquant({
          quality: [0.6, 0.8]
        })
      ]
    });
    
    console.log(`成功优化 ${optimizedFiles.length} 个文件`);
  } catch (error) {
    console.error('图片优化失败:', error);
  }
}

// 删除源码映射文件
function removeSourceMaps(directory) {
  console.log('删除不必要的文件...');
  
  // 查找所有 .map 文件
  const files = fs.readdirSync(directory, { withFileTypes: true });
  const mapFiles = files
    .filter(file => !file.isDirectory() && file.name.endsWith('.map'))
    .map(file => path.join(directory, file.name));
  
  console.log(`找到 ${mapFiles.length} 个源码映射文件`);
  
  // 删除文件
  let deletedCount = 0;
  for (const file of mapFiles) {
    try {
      fs.unlinkSync(file);
      deletedCount++;
    } catch (error) {
      console.error(`删除文件 ${file} 失败:`, error);
    }
  }
  
  console.log(`成功删除 ${deletedCount} 个源码映射文件`);
}

// 主函数
async function main() {
  console.log('开始优化资源...');
  
  // 优化 public 目录中的图片
  await optimizeImages('public');
  
  // 优化 dist 目录中的图片
  await optimizeImages('dist');
  
  // 删除 dist 目录中的源码映射文件
  removeSourceMaps('dist');
  
  console.log('资源优化完成！');
}

// 执行主函数
main().catch(error => {
  console.error('资源优化过程中发生错误:', error);
  process.exit(1);
});
