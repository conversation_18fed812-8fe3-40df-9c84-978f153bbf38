/**
 * 多公司版本开发脚本
 * 用于在开发环境中切换不同公司的版本
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取命令行参数
const args = process.argv.slice(2);
const companyKey = args[0] || 'taiyi'; // 默认为太一

// 验证公司标识是否有效
const validCompanies = ['xudong', 'taiyi'];
if (!validCompanies.includes(companyKey)) {
  console.error(`错误: 无效的公司标识 "${companyKey}". 有效选项: ${validCompanies.join(', ')}`);
  process.exit(1);
}

console.log(`开始切换到 ${companyKey} 公司版本...`);

// 更新公司配置文件
const companyConfigPath = path.resolve(process.cwd(), 'src/config/company.ts');
let companyConfigContent = fs.readFileSync(companyConfigPath, 'utf8');

// 替换公司标识
companyConfigContent = companyConfigContent.replace(
  /export const CURRENT_COMPANY = ['"].*['"]/,
  `export const CURRENT_COMPANY = '${companyKey}'`
);

// 写入更新后的公司配置文件
fs.writeFileSync(companyConfigPath, companyConfigContent, 'utf8');
console.log(`已更新公司配置文件，设置当前公司为 ${companyKey}`);

// 更新环境变量文件
try {
  console.log('更新开发环境变量文件...');
  const envDevPath = path.resolve(process.cwd(), '.env.development');
  let envDevContent = fs.readFileSync(envDevPath, 'utf8');

  // 设置公司标识
  envDevContent = envDevContent.replace(
    /REACT_APP_COMPANY=.*/,
    `REACT_APP_COMPANY=${companyKey}`
  );

  // 写入更新后的环境变量文件
  fs.writeFileSync(envDevPath, envDevContent, 'utf8');
  console.log(`已更新开发环境变量文件，设置公司标识为 ${companyKey}`);
  console.log('开发环境使用本地API地址: http://127.0.0.1:8089/jeecg-boot');
} catch (error) {
  console.error('更新开发环境变量文件失败:', error);
  // 继续启动过程，不中断
}

// 执行启动命令
try {
  console.log(`开始启动 ${companyKey} 公司版本开发环境...`);
  execSync(`cross-env NODE_ENV=development REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev`, { stdio: 'inherit' });
} catch (error) {
  console.error('启动失败:', error);
  process.exit(1);
}
