/**
 * 更新应用图标脚本
 * 根据当前选择的公司配置更新应用图标
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取命令行参数
const args = process.argv.slice(2);
const companyKey = args[0] || process.env.COMPANY_KEY || 'xudong'; // 默认为旭动

// 验证公司标识是否有效
const validCompanies = ['xudong', 'taiyi'];
if (!validCompanies.includes(companyKey)) {
  console.error(`错误: 无效的公司标识 "${companyKey}". 有效选项: ${validCompanies.join(', ')}`);
  process.exit(1);
}

console.log(`开始更新 ${companyKey} 公司的应用图标...`);

// 获取公司配置
const systemConfigPath = path.resolve(process.cwd(), 'src/config/systemConfig.ts');
const systemConfigContent = fs.readFileSync(systemConfigPath, 'utf8');

// 提取公司配置信息
const companyConfigRegex = new RegExp(`${companyKey}:\\s*{([^}]+)}`, 's');
const companyConfigMatch = systemConfigContent.match(companyConfigRegex);

if (!companyConfigMatch) {
  console.error(`无法在 systemConfig.ts 中找到 ${companyKey} 的配置信息`);
  process.exit(1);
}

// 提取公司logo
const logoRegex = /logo:\s*['"]([^'"]+)['"]/;
const logoMatch = companyConfigMatch[1].match(logoRegex);

if (!logoMatch) {
  console.error(`无法提取 ${companyKey} 的logo信息`);
  process.exit(1);
}

const logoFile = logoMatch[1];
console.log(`公司logo文件: ${logoFile}`);

// 源图标路径
const sourceLogoPath = path.resolve(process.cwd(), 'public/resource/img', logoFile);
if (!fs.existsSync(sourceLogoPath)) {
  console.error(`错误: 找不到源图标文件 ${sourceLogoPath}`);
  process.exit(1);
}

// 目标图标目录
const appLogoDir = path.resolve(process.cwd(), 'public/resource/applogo');
if (!fs.existsSync(appLogoDir)) {
  fs.mkdirSync(appLogoDir, { recursive: true });
  console.log(`创建应用图标目录: ${appLogoDir}`);
}

// 复制图标文件
const targetIconPath = path.resolve(appLogoDir, 'icon.png');
fs.copyFileSync(sourceLogoPath, targetIconPath);
console.log(`已将 ${logoFile} 复制到 ${targetIconPath}`);

console.log('应用图标更新完成！');
