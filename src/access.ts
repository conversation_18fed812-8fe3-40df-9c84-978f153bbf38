/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: any; userPermission?: any } | undefined) {
  const { currentUser, userPermission } = initialState ?? {};

  // 获取菜单权限列表
  const menuPermissions = userPermission?.menu || [];
  console.log('Access: 当前菜单权限列表:', menuPermissions);

  return {
    // 原有的管理员权限
    canAdmin: currentUser && currentUser.access === 'admin',

    // Jeecg 权限控制
    hasPermission: (permission?: string) => {
      // 如果没有指定权限，则默认有权限
      if (!permission) return true;

      // 如果没有权限数据，则没有权限
      if (!userPermission || !userPermission.codeList) return false;

      // 检查是否有指定的权限
      return userPermission.codeList.includes(permission);
    },

    // 菜单权限过滤器 - 显示所有菜单（已禁用权限控制）
    menuFilter: (route: any) => {
      // 所有菜单都可见，保留权限控制框架便于以后启用
      console.log(`Access: 菜单 ${route.name} 权限检查已禁用，默认可见`);
      return true;
    },
  };
}
