import React from 'react';
import { hasButtonPermission } from '@/services/menuPermission';

interface AuthorizedProps {
  authority: string;
  children: React.ReactNode;
  noMatch?: React.ReactNode;
}

/**
 * 权限组件
 * 用于控制按钮等元素的显示
 * @param authority 权限标识
 * @param children 有权限时显示的内容
 * @param noMatch 无权限时显示的内容
 */
const Authorized: React.FC<AuthorizedProps> = ({
  authority,
  children,
  noMatch = null
}) => {
  const hasPermission = hasButtonPermission(authority);
  
  return hasPermission ? <>{children}</> : <>{noMatch}</>;
};

export default Authorized;
