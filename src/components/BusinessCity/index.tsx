import React, { useState, useEffect } from 'react';
import { Tree, Spin, Empty, message } from 'antd';
import type { TreeProps } from 'antd';
import { request } from '@umijs/max';

// 地区数据类型
export interface Region {
  code: string;
  name: string;
  level: number;
  parentCode?: string;
  children?: Region[];
}

// 组件属性类型
interface BusinessCityProps {
  onChange?: (selectedKeys: string[]) => void;
  value?: string[];
  multiple?: boolean;
  checkable?: boolean;
  selectable?: boolean;
  defaultExpandAll?: boolean;
  defaultExpandedKeys?: string[];
}

/**
 * 业务城市选择组件
 *
 * 用于选择省市区三级地区
 */
const BusinessCity: React.FC<BusinessCityProps> = ({
  onChange,
  value,
  multiple = true,
  checkable = true,
  selectable = true,
  defaultExpandAll = false,
  defaultExpandedKeys = [],
}) => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(value || []);
  const [expandedKeys, setExpandedKeys] = useState<string[]>(defaultExpandedKeys);

  // 获取地区数据
  const fetchRegionData = async () => {
    setLoading(true);
    try {
      // 使用已知的地区列表接口
      const response = await request('/sys/region/list', {
        method: 'GET',
        params: { level: 1 }, // 获取省级地区
      });

      if (response.success && response.result) {
        // 获取省级数据后，为每个省获取市级数据
        const provinces = response.result;
        const provincesWithCities = await Promise.all(
          provinces.map(async (province) => {
            // 获取该省下的城市
            const citiesResponse = await request('/sys/region/list', {
              method: 'GET',
              params: { parentCode: province.code },
            });

            if (citiesResponse.success && citiesResponse.result) {
              return {
                ...province,
                children: citiesResponse.result,
              };
            }
            return province;
          })
        );

        console.log('业务城市数据:', provincesWithCities);
        const formattedData = formatTreeData(provincesWithCities);
        console.log('格式化后的树形数据:', formattedData);
        setTreeData(formattedData);

        // 如果设置了默认展开所有节点
        if (defaultExpandAll && provincesWithCities.length > 0) {
          const allKeys = getAllKeys(provincesWithCities);
          setExpandedKeys(allKeys);
        }
      } else {
        message.error('获取地区数据失败');
      }
    } catch (error) {
      console.error('获取地区数据出错:', error);
      message.error('获取地区数据出错');
    } finally {
      setLoading(false);
    }
  };

  // 格式化树形数据
  const formatTreeData = (data: Region[]) => {
    return data.map(item => ({
      title: item.name,
      key: item.code,
      children: item.children && item.children.length > 0 ? formatTreeData(item.children) : undefined,
      isLeaf: !item.children || item.children.length === 0,
    }));
  };

  // 获取所有节点的key
  const getAllKeys = (data: Region[]): string[] => {
    let keys: string[] = [];
    data.forEach(item => {
      keys.push(item.code);
      if (item.children && item.children.length > 0) {
        keys = [...keys, ...getAllKeys(item.children)];
      }
    });
    return keys;
  };

  // 初始化加载数据
  useEffect(() => {
    fetchRegionData();
  }, []);

  // 监听外部value变化
  useEffect(() => {
    if (value) {
      setSelectedKeys(value);
    }
  }, [value]);

  // 处理选择变化
  const handleSelect: TreeProps['onSelect'] = (selectedKeys) => {
    if (!selectable) return;

    // 如果是单选模式
    if (!multiple) {
      const selected = selectedKeys.length > 0 ? [selectedKeys[0].toString()] : [];
      setSelectedKeys(selected);
      onChange?.(selected);
    }
  };

  // 处理勾选变化
  const handleCheck: TreeProps['onCheck'] = (checked) => {
    if (!checkable) return;

    const checkedKeys = checked as string[];
    setSelectedKeys(checkedKeys);
    onChange?.(checkedKeys);
  };

  // 处理展开/收起
  const handleExpand = (expandedKeys: string[]) => {
    setExpandedKeys(expandedKeys);
  };

  return (
    <Spin spinning={loading}>
      {treeData.length > 0 ? (
        <Tree
          checkable={checkable}
          selectable={selectable}
          multiple={multiple}
          selectedKeys={selectable ? selectedKeys : []}
          checkedKeys={checkable ? selectedKeys : []}
          expandedKeys={expandedKeys}
          onSelect={handleSelect}
          onCheck={handleCheck}
          onExpand={handleExpand}
          treeData={treeData}
          height={400}
        />
      ) : (
        <Empty description="暂无地区数据" />
      )}
    </Spin>
  );
};

export default BusinessCity;
