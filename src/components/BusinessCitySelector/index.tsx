import React, { useState, useEffect } from 'react';
import { Form, Tag, Space, Typography } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import { FormCitySelector } from '@/components/CitySelector';
import { getCityInfosByCodes } from '@/utils/cityUtils';

const { Text } = Typography;

export interface BusinessCitySelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  showSelected?: boolean;
  required?: boolean;
  disabled?: boolean;
}

/**
 * 业务城市选择器组件
 * 基于 FormCitySelector 封装，专门用于业务城市的选择和回显
 */
const BusinessCitySelector: React.FC<BusinessCitySelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请选择业务城市',
  style,
  showSelected = true,
  required = false,
  disabled = false,
}) => {
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [cityDisplayInfo, setCityDisplayInfo] = useState<Array<{code: string, name: string, type: number}>>([]);

  // 处理城市选择变化
  const handleCityChange = (cities: string[]) => {
    setSelectedCities(cities);

    // 提取城市编码并传递给父组件
    const cityCodes = cities.map(city => {
      const parts = city.split(':');
      return parts[0]; // 只返回城市编码
    });

    onChange?.(cityCodes);
  };

  // 当外部传入的 value 变化时，更新内部状态
  useEffect(() => {
    if (value && value.length > 0) {
      // 如果传入的是纯编码格式，需要转换为标准格式
      const loadCityInfo = async () => {
        try {
          const cityInfos = await getCityInfosByCodes(value);
          setCityDisplayInfo(cityInfos);

          // 转换为标准格式 code:name:type
          const standardFormat = cityInfos.map(info => `${info.code}:${info.name}:${info.type}`);
          setSelectedCities(standardFormat);
        } catch (error) {
          console.error('加载城市信息失败:', error);
          // 如果加载失败，使用原始编码
          const fallbackFormat = value.map(code => `${code}:${code}:1`);
          setSelectedCities(fallbackFormat);
        }
      };

      loadCityInfo();
    } else {
      setSelectedCities([]);
      setCityDisplayInfo([]);
    }
  }, [value]);

  // 渲染已选择的城市标签
  const renderSelectedCities = () => {
    if (!showSelected || selectedCities.length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: 12 }}>
        <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
          已选择业务城市:
        </Text>
        <Space wrap>
          {selectedCities.map((city) => {
            const parts = city.split(':');
            const cityCode = parts[0];
            const cityName = parts[1] || cityCode;
            const cityType = parseInt(parts[2] || '1', 10);

            const tagColor = cityType === 1 ? 'blue' : 'green';
            const typeText = cityType === 1 ? '省' : '市';

            return (
              <Tag
                key={cityCode}
                color={tagColor}
                icon={<EnvironmentOutlined />}
              >
                {cityName} {typeText}
              </Tag>
            );
          })}
        </Space>
      </div>
    );
  };

  return (
    <div style={style}>
      <div>
        <FormCitySelector
          name="businessCities"
          value={selectedCities}
          onChange={handleCityChange}
          placeholder={placeholder}
          showSelected={false} // 我们自己渲染选中的城市
          required={required}
          useBackendData={true}
          disabled={disabled}
        />
      </div>
      {renderSelectedCities()}
    </div>
  );
};

// Form.Item 包装器
export const FormBusinessCitySelector: React.FC<BusinessCitySelectorProps & {
  name: string | number | (string | number)[];
  label?: React.ReactNode;
  rules?: any[];
}> = ({ name, label, rules, required, ...restProps }) => {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules || (required ? [{ required: true, message: '请选择至少一个业务城市' }] : [])}
    >
      <BusinessCitySelector
        required={required}
        {...restProps}
      />
    </Form.Item>
  );
};

export default BusinessCitySelector;
