import React from 'react';
import { Form, Checkbox, Space, Typography } from 'antd';
import { CarOutlined, BankOutlined, GiftOutlined } from '@ant-design/icons';

const { Text } = Typography;

export interface BusinessTypeSelectorProps {
  value?: number[];
  onChange?: (value: number[]) => void;
  disabled?: boolean;
  showSelected?: boolean;
}

/**
 * 业务类型选择器组件
 * 支持多选业务类型：0-车险,1-财险,2-增值服务
 */
const BusinessTypeSelector: React.FC<BusinessTypeSelectorProps> = ({
  value = [],
  onChange,
  disabled = false,
  showSelected = true,
}) => {
  // 业务类型选项配置
  const businessTypeOptions = [
    {
      value: 0,
      label: '车险',
      icon: <CarOutlined style={{ color: '#1890ff' }} />,
      color: '#1890ff',
    },
    {
      value: 1,
      label: '财险',
      icon: <BankOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a',
    },
    {
      value: 2,
      label: '增值服务',
      icon: <GiftOutlined style={{ color: '#fa8c16' }} />,
      color: '#fa8c16',
    },
  ];

  // 处理选择变化
  const handleChange = (checkedValues: any[]) => {
    // 确保返回的是数字数组
    const numericValues = checkedValues.map(val => Number(val)).filter(val => !isNaN(val));
    onChange?.(numericValues);
  };

  // 渲染已选择的业务类型
  const renderSelectedTypes = () => {
    if (!showSelected || value.length === 0) {
      return null;
    }

    const selectedOptions = businessTypeOptions.filter(option => value.includes(option.value));

    return (
      <div style={{ marginTop: 12 }}>
        <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
          已选择业务类型:
        </Text>
        <Space wrap>
          {selectedOptions.map((option) => (
            <div
              key={option.value}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '4px 8px',
                backgroundColor: option.color + '15',
                border: `1px solid ${option.color}`,
                borderRadius: 4,
                fontSize: 12,
              }}
            >
              {option.icon}
              <span style={{ marginLeft: 4, color: option.color, fontWeight: 500 }}>
                {option.label}
              </span>
            </div>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <div>
      <Checkbox.Group
        value={value}
        onChange={handleChange}
        disabled={disabled}
        style={{ width: '100%' }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {businessTypeOptions.map((option) => (
            <Checkbox
              key={option.value}
              value={option.value}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                borderRadius: 6,
                margin: 0,
                marginBottom: 8,
                backgroundColor: value.includes(option.value) ? option.color + '08' : '#fafafa',
                borderColor: value.includes(option.value) ? option.color : '#d9d9d9',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', marginLeft: 8 }}>
                {option.icon}
                <span style={{ marginLeft: 8, fontWeight: 500 }}>
                  {option.label}
                </span>
              </div>
            </Checkbox>
          ))}
        </Space>
      </Checkbox.Group>
      
      {renderSelectedTypes()}
    </div>
  );
};

// Form.Item 包装器
export const FormBusinessTypeSelector: React.FC<BusinessTypeSelectorProps & {
  name: string | number | (string | number)[];
  label?: React.ReactNode;
  rules?: any[];
  required?: boolean;
}> = ({ name, label, rules, required, ...restProps }) => {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules || (required ? [{ required: true, message: '请选择至少一种业务类型' }] : [])}
    >
      <BusinessTypeSelector {...restProps} />
    </Form.Item>
  );
};

export default BusinessTypeSelector;
