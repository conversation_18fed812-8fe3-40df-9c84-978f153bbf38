import React, { useState, useEffect } from 'react';
import { Space, Spin, Empty, Typography } from 'antd';
import { getCityInfosByCodes } from '@/utils/cityUtils';
import CityCodeDisplay from './index';

const { Text } = Typography;

interface BatchCityCodeDisplayProps {
  cityCodes: string[];
  showType?: boolean;
  maxDisplay?: number; // 最大显示数量，超过则显示省略
  style?: React.CSSProperties;
}

/**
 * 批量城市编码显示组件
 * 用于显示多个城市编码对应的城市名称
 */
const BatchCityCodeDisplay: React.FC<BatchCityCodeDisplayProps> = ({
  cityCodes,
  showType = true,
  maxDisplay = 10,
  style
}) => {
  const [cityInfos, setCityInfos] = useState<Array<{ code: string; name: string; type: number }>>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCityInfos = async () => {
      if (!cityCodes || cityCodes.length === 0) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const infos = await getCityInfosByCodes(cityCodes);
        setCityInfos(infos);
      } catch (error) {
        console.error('批量获取城市信息失败:', error);
        setCityInfos([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCityInfos();
  }, [cityCodes]);

  if (loading) {
    return <Spin size="small" tip="加载城市信息..." />;
  }

  if (!cityInfos || cityInfos.length === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无城市数据" />;
  }

  // 显示的城市列表
  const displayCities = cityInfos.slice(0, maxDisplay);
  const remainingCount = cityInfos.length - maxDisplay;

  return (
    <div style={style}>
      <Space size={[8, 8]} wrap>
        {displayCities.map((cityInfo) => (
          <CityCodeDisplay
            key={cityInfo.code}
            cityCode={cityInfo.code}
            showType={showType}
          />
        ))}
        {remainingCount > 0 && (
          <Text type="secondary">
            +{remainingCount} 个城市
          </Text>
        )}
      </Space>
    </div>
  );
};

export default BatchCityCodeDisplay;
