import React, { useState, useEffect } from 'react';
import { Tag, Spin } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import { getCityInfoByCode } from '@/utils/cityUtils';

interface CityCodeDisplayProps {
  cityCode: string;
  showType?: boolean; // 是否显示城市类型标识
  style?: React.CSSProperties;
}

/**
 * 城市编码显示组件
 * 根据城市编码从后端接口获取对应的城市名称并显示
 */
const CityCodeDisplay: React.FC<CityCodeDisplayProps> = ({ 
  cityCode, 
  showType = true,
  style 
}) => {
  const [cityInfo, setCityInfo] = useState<{ name: string; type: number } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCityInfo = async () => {
      if (!cityCode) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const info = await getCityInfoByCode(cityCode);
        setCityInfo(info);
      } catch (error) {
        console.error('获取城市信息失败:', error);
        setCityInfo(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCityInfo();
  }, [cityCode]);

  if (loading) {
    return <Spin size="small" />;
  }

  if (!cityInfo) {
    // 如果找不到城市信息，显示原始编码
    return (
      <Tag color="default" style={style}>
        {cityCode}
      </Tag>
    );
  }

  // 根据城市类型设置标签颜色和类型文本
  let tagColor = 'green';
  let cityType = '';

  if (cityInfo.type === 1) {
    tagColor = 'blue';
    cityType = showType ? ' (省/直辖市)' : '';
  } else if (cityInfo.type === 2) {
    tagColor = 'green';
    cityType = showType ? ' (地级市)' : '';
  } else if (cityInfo.type === 3) {
    tagColor = 'purple';
    cityType = showType ? ' (特别行政区)' : '';
  }

  return (
    <Tag 
      color={tagColor} 
      icon={<EnvironmentOutlined />}
      style={style}
    >
      {cityInfo.name}{cityType}
    </Tag>
  );
};

export default CityCodeDisplay;
