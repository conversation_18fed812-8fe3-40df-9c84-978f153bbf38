import React from 'react';

interface CityNameDisplayProps {
  cityCode: string;
  cityName: string;
  type: number;
}

/**
 * 城市名称显示组件
 * 根据城市编码和类型动态显示城市名称
 * 对于二级城市(type=2)，显示时会拼接其所属的一级城市名称
 * 对于一级城市(type=1)，直接显示原名称
 */
const CityNameDisplay: React.FC<CityNameDisplayProps> = ({ cityCode, cityName, type }) => {
  // 定义直辖市列表
  const directCities = ['北京市', '天津市', '上海市', '重庆市'];
  
  // 省份编码映射表
  const provinceCodeMap: Record<string, string> = {
    '11': '北京市',
    '12': '天津市',
    '13': '河北省',
    '14': '山西省',
    '15': '内蒙古自治区',
    '21': '辽宁省',
    '22': '吉林省',
    '23': '黑龙江省',
    '31': '上海市',
    '32': '江苏省',
    '33': '浙江省',
    '34': '安徽省',
    '35': '福建省',
    '36': '江西省',
    '37': '山东省',
    '41': '河南省',
    '42': '湖北省',
    '43': '湖南省',
    '44': '广东省',
    '45': '广西壮族自治区',
    '46': '海南省',
    '50': '重庆市',
    '51': '四川省',
    '52': '贵州省',
    '53': '云南省',
    '54': '西藏自治区',
    '61': '陕西省',
    '62': '甘肃省',
    '63': '青海省',
    '64': '宁夏回族自治区',
    '65': '新疆维吾尔自治区',
    '71': '台湾省',
    '81': '香港特别行政区',
    '82': '澳门特别行政区'
  };

  // 如果是二级城市，需要拼接省份名称
  if (type === 2 && cityCode) {
    // 获取省份编码（城市编码的前两位）
    const provinceCode = cityCode.substring(0, 2);
    const provinceName = provinceCodeMap[provinceCode];
    
    if (provinceName) {
      // 对于直辖市，直接使用省份名称
      if (directCities.includes(provinceName)) {
        return <span>{provinceName}{cityName}</span>;
      } 
      // 对于普通省份，去掉"省"、"自治区"等后缀
      else {
        let shortProvinceName = provinceName;
        if (provinceName.endsWith('省')) {
          shortProvinceName = provinceName.slice(0, -1);
        } else if (provinceName.endsWith('自治区')) {
          shortProvinceName = provinceName.slice(0, -3);
        } else if (provinceName.endsWith('特别行政区')) {
          shortProvinceName = provinceName.slice(0, -5);
        }
        return <span>{shortProvinceName}{cityName}</span>;
      }
    }
  }
  
  // 对于一级城市或无法识别的城市，直接显示原名称
  return <span>{cityName}</span>;
};

export default CityNameDisplay;
