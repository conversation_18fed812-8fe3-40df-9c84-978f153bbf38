import React, { useState, useEffect } from 'react';
import { TreeSelect, Form, Tag, Space, Typography, Empty, Spin } from 'antd';
import type { TreeSelectProps } from 'antd';
import { EnvironmentOutlined, LoadingOutlined } from '@ant-design/icons';
import { getCityTree } from '@/services/corp/city';
import { convertCodesToStandardFormat, isPureCityCodeFormat } from '@/utils/cityUtils';

const { Text } = Typography;
const { SHOW_PARENT } = TreeSelect;

// 直辖市列表
const directCities = ['北京市', '天津市', '上海市', '重庆市'];

export interface CityItem {
  city: string;
  type: number;
}

export interface CityData {
  title: string;
  value: string;
  key: string;
  children?: CityData[];
}

export interface CitySelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  cityData?: CityData[];
  useBackendData?: boolean;
  showSelected?: boolean;
  placeholder?: string;
  style?: React.CSSProperties;
  autoConvertCodes?: boolean; // 是否自动转换纯编码格式
}

const CitySelector: React.FC<CitySelectorProps> = ({
  value,
  onChange,
  cityData = [],
  useBackendData = false,
  showSelected = true,
  placeholder = '请选择城市',
  style,
  autoConvertCodes = true,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [backendCityData, setBackendCityData] = useState<CityData[]>([]);
  const [convertedValue, setConvertedValue] = useState<string[]>(value || []);

  // 从后端获取城市数据
  useEffect(() => {
    let isMounted = true;

    if (useBackendData) {
      setLoading(true);
      getCityTree()
        .then(response => {
          if (isMounted && response && response.success && response.result) {
            // 转换后端数据格式为组件需要的格式
            const formattedData = convertBackendCityData(response.result);
            setBackendCityData(formattedData);
          }
        })
        .catch(error => {
          if (isMounted) {
            console.error('获取城市数据失败:', error);
          }
        })
        .finally(() => {
          if (isMounted) {
            setLoading(false);
          }
        });
    }

    return () => {
      isMounted = false;
    };
  }, [useBackendData]);

  // 自动转换纯编码格式的值
  useEffect(() => {
    const convertValue = async () => {
      if (!value || !autoConvertCodes) {
        setConvertedValue(value || []);
        return;
      }

      // 检查是否为纯编码格式
      if (isPureCityCodeFormat(value)) {
        try {
          const converted = await convertCodesToStandardFormat(value);
          setConvertedValue(converted);
        } catch (error) {
          console.error('转换城市编码失败:', error);
          setConvertedValue(value);
        }
      } else {
        setConvertedValue(value);
      }
    };

    convertValue();
  }, [value, autoConvertCodes]);

  // 转换后端城市数据为组件需要的格式
  const convertBackendCityData = (data: any[]): CityData[] => {
    return data.map(province => {
      const provinceNode: CityData = {
        title: province.name,
        value: `${province.code}:${province.name}:1`, // 格式: code:name:type
        key: province.code,
      };

      if (province.children && province.children.length > 0) {
        provinceNode.children = province.children.map(city => ({
          title: city.name,
          value: `${city.code}:${city.name}:2`, // 格式: code:name:type
          key: city.code,
        }));
      }

      return provinceNode;
    });
  };
  // 处理选择变化
  const handleChange = (newValue: string[]) => {
    if (onChange) {
      // 处理选中的值，确保省份的type为1
      const processedValue = processSelectedValues(newValue);
      onChange(processedValue);
    }
  };

  // 处理选中的值，确保省份的type为1，并且保留code信息
  const processSelectedValues = (selectedValues: string[]): string[] => {
    return selectedValues.map(item => {
      // 如果值已经包含类型信息
      if (item.includes(':')) {
        const parts = item.split(':');

        // 如果是后端数据格式 (code:name:type)
        if (parts.length === 3) {
          const [code, name, type] = parts;
          // 如果是省份但类型不是1，则修正为1
          if (isProvince(name) && type !== '1') {
            return `${code}:${name}:1`;
          }
          return item;
        }

        // 如果是旧格式 (name:type)
        const [city, type] = parts;
        // 如果是省份但类型不是1，则修正为1
        if (isProvince(city) && type !== '1') {
          return `${city}:1`;
        }
        return item;
      }

      // 如果值不包含类型信息，则根据是否是省份来添加类型
      return isProvince(item) ? `${item}:1` : `${item}:2`;
    });
  };

  // 判断是否是省份
  const isProvince = (cityName: string): boolean => {
    // 检查是否是直辖市
    if (directCities.includes(cityName)) {
      return true;
    }

    // 检查是否是省份（通过名称结尾是否包含"省"或"自治区"等）
    return cityName.endsWith('省') ||
           cityName.endsWith('自治区') ||
           cityName.endsWith('特别行政区');
  };

  // 自定义标签渲染函数
  const tagRender = (props: any) => {
    const { label, value, closable, onClose } = props;

    // 检查value是否存在
    if (!value || typeof value !== 'string') {
      return (
        <Tag
          color="blue"
          closable={closable}
          onClose={onClose}
          style={{ marginRight: 3 }}
        >
          {label || '未知城市'}
        </Tag>
      );
    }

    // 解析value获取城市信息
    const parts = value.split(':');
    let displayName = label;

    if (parts.length === 3) {
      // 后端数据格式: code:name:type
      const [cityCode, cityName, type] = parts;

      // 如果是二级城市，需要拼接省份名称
      if (type === '2' && cityCode) {
        // 省份编码映射表
        const provinceCodeMap: Record<string, string> = {
          '11': '北京市', '12': '天津市', '13': '河北省', '14': '山西省', '15': '内蒙古自治区',
          '21': '辽宁省', '22': '吉林省', '23': '黑龙江省', '31': '上海市', '32': '江苏省',
          '33': '浙江省', '34': '安徽省', '35': '福建省', '36': '江西省', '37': '山东省',
          '41': '河南省', '42': '湖北省', '43': '湖南省', '44': '广东省', '45': '广西壮族自治区',
          '46': '海南省', '50': '重庆市', '51': '四川省', '52': '贵州省', '53': '云南省',
          '54': '西藏自治区', '61': '陕西省', '62': '甘肃省', '63': '青海省', '64': '宁夏回族自治区',
          '65': '新疆维吾尔自治区', '71': '台湾省', '81': '香港特别行政区', '82': '澳门特别行政区'
        };

        // 获取省份编码（城市编码的前两位）
        const provinceCode = cityCode.substring(0, 2);
        const provinceName = provinceCodeMap[provinceCode];

        if (provinceName) {
          // 对于直辖市，直接使用省份名称
          if (directCities.includes(provinceName)) {
            displayName = provinceName;
          }
          // 对于普通省份，去掉"省"、"自治区"等后缀
          else {
            let shortProvinceName = provinceName;
            if (provinceName.endsWith('省')) {
              shortProvinceName = provinceName.slice(0, -1);
            } else if (provinceName.endsWith('自治区')) {
              shortProvinceName = provinceName.slice(0, -3);
            } else if (provinceName.endsWith('特别行政区')) {
              shortProvinceName = provinceName.slice(0, -5);
            }
            displayName = `${shortProvinceName}${cityName}`;
          }
        } else {
          displayName = cityName;
        }
      } else {
        displayName = cityName;
      }
    }

    return (
      <Tag
        color="blue"
        closable={closable}
        onClose={onClose}
        style={{ marginRight: 3 }}
      >
        {displayName}
      </Tag>
    );
  };

  const treeSelectProps: TreeSelectProps<string[]> = {
    treeData: useBackendData ? backendCityData : cityData,
    value: convertedValue,
    onChange: handleChange,
    treeCheckable: true,
    showCheckedStrategy: SHOW_PARENT,
    placeholder,
    style: { width: '100%', ...style },
    treeDefaultExpandAll: false,
    dropdownStyle: { maxHeight: 400, overflow: 'auto' },
    showSearch: true,
    filterTreeNode: (input, treeNode) =>
      (treeNode?.title as string)?.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    maxTagCount: 'responsive',
    tagRender, // 使用自定义标签渲染
    // 使用treeCheckStrictly和labelInValue来获取更详细的选择信息
    treeCheckStrictly: false, // 保持父子节点关联，但我们会在onChange中处理
    disabled: loading,
  };

  // 渲染已选择的城市标签
  const renderSelectedCities = () => {
    if (!convertedValue || convertedValue.length === 0) {
      return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无选择" />;
    }

    return (
      <Space size={[8, 8]} wrap>
        {convertedValue.map((city: string) => {
          const parts = city.split(':');

          // 处理不同格式的值
          let cityCode, cityName, type;

          if (parts.length === 3) {
            // 后端数据格式: code:name:type
            cityCode = parts[0];
            cityName = parts[1];
            type = parts[2];
          } else if (parts.length === 2) {
            // 旧格式: name:type
            cityName = parts[0];
            cityCode = parts[0]; // 旧格式中没有单独的code，使用name作为cityCode
            type = parts[1];
          } else {
            // 未知格式，直接使用
            cityName = parts[0];
            cityCode = parts[0]; // 未知格式中没有单独的code，使用name作为cityCode
            type = '1'; // 默认类型为1
          }

          let tagColor = 'green';
          let cityType = '(地级市)';

          if (type === '1') {
            tagColor = 'blue';
            cityType = '(省/直辖市)';
          } else if (type === '3') {
            tagColor = 'purple';
            cityType = '(特别行政区)';
          }

          // 省份编码映射表
          const provinceCodeMap: Record<string, string> = {
            '11': '北京市',
            '12': '天津市',
            '13': '河北省',
            '14': '山西省',
            '15': '内蒙古自治区',
            '21': '辽宁省',
            '22': '吉林省',
            '23': '黑龙江省',
            '31': '上海市',
            '32': '江苏省',
            '33': '浙江省',
            '34': '安徽省',
            '35': '福建省',
            '36': '江西省',
            '37': '山东省',
            '41': '河南省',
            '42': '湖北省',
            '43': '湖南省',
            '44': '广东省',
            '45': '广西壮族自治区',
            '46': '海南省',
            '50': '重庆市',
            '51': '四川省',
            '52': '贵州省',
            '53': '云南省',
            '54': '西藏自治区',
            '61': '陕西省',
            '62': '甘肃省',
            '63': '青海省',
            '64': '宁夏回族自治区',
            '65': '新疆维吾尔自治区',
            '71': '台湾省',
            '81': '香港特别行政区',
            '82': '澳门特别行政区'
          };

          // 显示城市名称（对于二级城市，显示时拼接省份名称）
          let displayName = cityName;

          // 如果是二级城市，需要拼接省份名称
          if (type === '2' && cityCode) {
            // 获取省份编码（城市编码的前两位）
            const provinceCode = cityCode.substring(0, 2);
            const provinceName = provinceCodeMap[provinceCode];

            if (provinceName) {
              // 对于直辖市，直接使用省份名称
              if (directCities.includes(provinceName)) {
                displayName = `${provinceName}${cityName}`;
              }
              // 对于普通省份，去掉"省"、"自治区"等后缀
              else {
                let shortProvinceName = provinceName;
                if (provinceName.endsWith('省')) {
                  shortProvinceName = provinceName.slice(0, -1);
                } else if (provinceName.endsWith('自治区')) {
                  shortProvinceName = provinceName.slice(0, -3);
                } else if (provinceName.endsWith('特别行政区')) {
                  shortProvinceName = provinceName.slice(0, -5);
                }
                displayName = `${shortProvinceName}${cityName}`;
              }
            }
          }

          return (
            <Tag
              key={city}
              color={tagColor}
              icon={<EnvironmentOutlined />}
            >
              {displayName} {cityType}
            </Tag>
          );
        })}
      </Space>
    );
  };

  return (
    <div>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} tip="加载城市数据..." />
        </div>
      ) : (
        <TreeSelect<string[]> {...treeSelectProps} />
      )}

      {showSelected && convertedValue && convertedValue.length > 0 && (
        <div style={{ marginTop: 12 }}>
          <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
            已选择城市:
          </Text>
          {renderSelectedCities()}
        </div>
      )}
    </div>
  );
};

// Form.Item 包装器
export const FormCitySelector: React.FC<CitySelectorProps & {
  name: string | number | (string | number)[];
  label?: React.ReactNode;
  rules?: any[];
  required?: boolean;
}> = ({ name, label, rules, required, useBackendData = true, autoConvertCodes = true, ...restProps }) => {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules || (required ? [{ required: true, message: '请选择至少一个城市' }] : [])}
    >
      <CitySelector
        useBackendData={useBackendData}
        autoConvertCodes={autoConvertCodes}
        {...restProps}
      />
    </Form.Item>
  );
};

export default CitySelector;
