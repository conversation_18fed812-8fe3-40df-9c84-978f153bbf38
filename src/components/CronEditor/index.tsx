import React, { useState, useEffect } from 'react';
import { Input, Button, Modal, Tabs, Radio, InputNumber, Checkbox, Row, Col, Space, Typography } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { TabPane } = Tabs;

interface CronEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

// 简化版的Cron表达式编辑器
const CronEditor: React.FC<CronEditorProps> = ({
  value = '* * * * * ?',
  onChange,
  placeholder = '请输入cron表达式',
  disabled = false,
}) => {
  const [cronValue, setCronValue] = useState<string>(value);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [nextFireTimes, setNextFireTimes] = useState<string[]>([]);

  useEffect(() => {
    setCronValue(value);
  }, [value]);

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setCronValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  // 打开配置弹窗
  const openConfigModal = () => {
    if (!disabled) {
      setModalVisible(true);
      // 这里可以添加计算下次执行时间的逻辑
      setNextFireTimes([
        '下次执行时间计算中...',
      ]);
    }
  };

  // 关闭配置弹窗
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 确认配置
  const handleOk = () => {
    setModalVisible(false);
    if (onChange) {
      onChange(cronValue);
    }
  };

  return (
    <>
      <Input
        value={cronValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        disabled={disabled}
        addonAfter={
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={openConfigModal}
            disabled={disabled}
            style={{ margin: -7 }}
          >
            选择
          </Button>
        }
      />

      <Modal
        title="Cron表达式配置"
        open={modalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
      >
        <Tabs defaultActiveKey="second">
          <TabPane tab="秒" key="second">
            <div style={{ padding: '16px 0' }}>
              <Radio.Group defaultValue="every">
                <Space direction="vertical">
                  <Radio value="every">每秒</Radio>
                  <Radio value="range">
                    区间
                    <InputNumber min={0} max={59} defaultValue={0} style={{ margin: '0 8px' }} />
                    -
                    <InputNumber min={0} max={59} defaultValue={59} style={{ margin: '0 8px' }} />
                  </Radio>
                  <Radio value="loop">
                    循环
                    从第
                    <InputNumber min={0} max={59} defaultValue={0} style={{ margin: '0 8px' }} />
                    秒开始，每
                    <InputNumber min={1} max={59} defaultValue={1} style={{ margin: '0 8px' }} />
                    秒执行一次
                  </Radio>
                  <Radio value="specify">
                    指定
                    <div style={{ marginTop: 8 }}>
                      <Checkbox.Group>
                        <Row>
                          {Array.from({ length: 60 }).map((_, i) => (
                            <Col span={4} key={i}>
                              <Checkbox value={i}>{i}</Checkbox>
                            </Col>
                          ))}
                        </Row>
                      </Checkbox.Group>
                    </div>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>
          </TabPane>
          <TabPane tab="分" key="minute">
            <div style={{ padding: '16px 0' }}>
              <Text>分钟配置（简化版）</Text>
            </div>
          </TabPane>
          <TabPane tab="时" key="hour">
            <div style={{ padding: '16px 0' }}>
              <Text>小时配置（简化版）</Text>
            </div>
          </TabPane>
          <TabPane tab="日" key="day">
            <div style={{ padding: '16px 0' }}>
              <Text>日期配置（简化版）</Text>
            </div>
          </TabPane>
          <TabPane tab="月" key="month">
            <div style={{ padding: '16px 0' }}>
              <Text>月份配置（简化版）</Text>
            </div>
          </TabPane>
          <TabPane tab="周" key="week">
            <div style={{ padding: '16px 0' }}>
              <Text>星期配置（简化版）</Text>
            </div>
          </TabPane>
        </Tabs>

        <div style={{ marginTop: 16, borderTop: '1px solid #f0f0f0', paddingTop: 16 }}>
          <Text strong>表达式：{cronValue}</Text>
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">最近10次执行时间：</Text>
            <div style={{ marginTop: 8, maxHeight: 150, overflow: 'auto' }}>
              {nextFireTimes.map((time, index) => (
                <div key={index}>{time}</div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CronEditor;
