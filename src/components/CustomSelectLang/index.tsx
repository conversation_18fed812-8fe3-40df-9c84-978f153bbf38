import { GlobalOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd';
import { setLocale, getLocale } from '@umijs/max';
import React from 'react';

export type SiderTheme = 'light' | 'dark';

export const CustomSelectLang = () => {
  const selectedLang = getLocale();

  const changeLang = ({ key }: { key: string }) => {
    setLocale(key);
  };

  const items = [
    {
      key: 'zh-CN',
      label: '简体中文',
    },
    {
      key: 'en-US',
      label: 'English',
    },
  ];

  return (
    <Dropdown
      menu={{
        items,
        selectedKeys: [selectedLang],
        onClick: changeLang
      }}
      placement="bottomRight"
    >
      <div style={{ cursor: 'pointer', padding: '8px', backgroundColor: 'rgba(255, 255, 255, 0.6)', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <GlobalOutlined style={{ fontSize: '18px', color: '#1890ff' }} />
      </div>
    </Dropdown>
  );
};

export default CustomSelectLang;
