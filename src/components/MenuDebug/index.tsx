import React, { useEffect, useState } from 'react';
import { Button, Drawer, Typography, Space } from 'antd';
import { getVisibleMenuKeys } from '@/services/menuFilter';
import { getCurrentCompany } from '@/config/company';

const { Title, Paragraph, Text } = Typography;

/**
 * 菜单调试组件
 * 用于显示当前菜单状态和可见菜单项
 */
const MenuDebug: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [company, setCompany] = useState('');
  const [visibleMenus, setVisibleMenus] = useState<string[]>([]);

  useEffect(() => {
    // 获取当前公司和可见菜单
    const currentCompany = getCurrentCompany();
    const menuKeys = getVisibleMenuKeys();
    
    setCompany(currentCompany);
    setVisibleMenus(menuKeys);
  }, []);

  const showDrawer = () => {
    setVisible(true);
  };

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <Button 
        type="primary" 
        onClick={showDrawer}
        style={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000 }}
      >
        菜单调试
      </Button>
      <Drawer
        title="菜单调试信息"
        placement="right"
        onClose={onClose}
        open={visible}
        width={400}
      >
        <Typography>
          <Title level={4}>当前公司</Title>
          <Paragraph>
            <Text strong>{company}</Text>
          </Paragraph>
          
          <Title level={4}>可见菜单项 ({visibleMenus.length})</Title>
          <Space direction="vertical">
            {visibleMenus.map((menu, index) => (
              <Text key={index} code>{menu}</Text>
            ))}
          </Space>
        </Typography>
      </Drawer>
    </>
  );
};

export default MenuDebug;
