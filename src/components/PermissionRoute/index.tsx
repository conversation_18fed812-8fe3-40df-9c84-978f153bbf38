/**
 * 权限路由组件
 * 根据菜单权限控制路由访问
 */

import React from 'react';
import { Route, Redirect } from 'react-router-dom';
import { isMenuVisible } from '@/services/menuFilter';

interface PermissionRouteProps {
  path: string;
  component: React.ComponentType<any>;
  menuKey: string;
  exact?: boolean;
  [key: string]: any;
}

const PermissionRoute: React.FC<PermissionRouteProps> = ({
  component: Component,
  menuKey,
  ...rest
}) => (
  <Route
    {...rest}
    render={props =>
      isMenuVisible(menuKey) ? (
        <Component {...props} />
      ) : (
        <Redirect to="/unauthorized" />
      )
    }
  />
);

export default PermissionRoute;
