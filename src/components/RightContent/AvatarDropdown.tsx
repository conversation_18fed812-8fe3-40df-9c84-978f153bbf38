import { logout, clearLoginCredentials } from '@/services/auth';
import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Spin, message } from 'antd';
import type { MenuProps } from 'antd';
import { createStyles } from 'antd-style';
import { stringify } from 'querystring';
import React, { useEffect } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.name}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

// 强制跳转到登录页面的函数
const forceRedirectToLogin = () => {
  console.log('强制跳转到登录页面');
  // 清除所有可能的用户数据
  localStorage.removeItem('token');
  localStorage.removeItem('loginUsername');
  sessionStorage.clear();

  // 直接设置窗口位置
  window.location.href = '/user/login';
};

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  // 添加一个效果钩子，在组件挂载时检查登录状态
  useEffect(() => {
    // 检查是否有token
    const token = localStorage.getItem('token');
    if (!token && window.location.pathname !== '/user/login') {
      console.log('检测到没有token，但不在登录页面，尝试重定向');
      // 如果没有token但不在登录页面，重定向到登录页面
      setTimeout(() => {
        forceRedirectToLogin();
      }, 100);
    }
  }, []);

  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    try {
      // 尝试调用后端登出接口
      const response = await logout();
      console.log('登出响应:', response);

      // 无论后端返回成功或失败，都执行前端登出流程
      if (!response.success) {
        console.warn('后端登出失败，错误信息:', response.message);
      }
    } catch (error) {
      // 捕获并记录错误，但继续执行登出流程
      console.error('登出请求发生错误:', error);
    } finally {
      // 无论后端响应如何，都清除前端状态并跳转到登录页
      // 清除保存的登录凭证
      clearLoginCredentials();
      // 清除token
      localStorage.removeItem('token');
      // 清除用户名
      localStorage.removeItem('loginUsername');
      // 清除其他可能的缓存数据
      sessionStorage.clear();
      // 清除所有与用户相关的localStorage项
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('user') || key.includes('User') || key.includes('tenant') || key.includes('Tenant') || key.includes('auth') || key.includes('Auth'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // 直接强制跳转到登录页面，不使用history.replace
      console.log('开始重定向到登录页面...');

      // 显示消息提示用户正在退出
      message.loading('正在退出登录...', 1, () => {
        // 调用强制跳转函数
        forceRedirectToLogin();
      });

      // 为了防止可能的重定向问题，添加一个延时回调
      setTimeout(() => {
        console.log('重定向超时检查，如果还在当前页面，尝试再次跳转');
        if (window.location.pathname !== '/user/login') {
          forceRedirectToLogin();
        }
      }, 1000);
    }
  };
  const { styles } = useStyles();

  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick: MenuProps['onClick'] = (event) => {
    const { key } = event;
    if (key === 'logout') {
      // 直接调用登出函数，不等待状态更新
      console.log('点击了退出登录按钮');

      // 先清除全局状态
      try {
        flushSync(() => {
          setInitialState((s) => ({
            ...s,
            currentUser: undefined,
            userPermission: undefined,
          }));
        });
      } catch (error) {
        console.error('清除全局状态时出错:', error);
      }

      // 立即调用登出函数
      loginOut();
      return;
    }
    history.push(`/account/${key}`);
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    console.log('初始状态为空，显示加载中...');
    return loading;
  }

  const { currentUser } = initialState;

  // 如果没有用户信息，尝试从 localStorage 获取用户名
  if (!currentUser || !currentUser.name) {
    const username = localStorage.getItem('loginUsername');
    console.log('当前用户信息不完整，从 localStorage 获取的用户名:', username);

    if (username) {
      // 如果有用户名，更新全局状态
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: {
            ...s?.currentUser,
            name: username,
            avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
          },
        }));
      });

      // 返回下拉菜单，而不是加载中状态
      return (
        <HeaderDropdown
          menu={{
            selectedKeys: [],
            onClick: onMenuClick,
            items: menuItems,
          }}
        >
          {children}
        </HeaderDropdown>
      );
    }

    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
          {
            key: 'center',
            icon: <UserOutlined />,
            label: '个人中心',
          },
          {
            key: 'settings',
            icon: <SettingOutlined />,
            label: '个人设置',
          },
          {
            type: 'divider' as const,
          },
        ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
