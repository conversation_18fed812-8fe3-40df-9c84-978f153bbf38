import { QuestionCircleOutlined } from '@ant-design/icons';
import React from 'react';
import CustomSelectLang from '@/components/CustomSelectLang';

export type SiderTheme = 'light' | 'dark';

export const SelectLang = () => {
  return (
    <div style={{ display: 'flex', alignItems: 'center', padding: 4 }}>
      <CustomSelectLang />
    </div>
  );
};

export const Question = () => {
  return (
    <div
      style={{
        display: 'flex',
        height: 26,
      }}
      onClick={() => {
        window.open('https://pro.ant.design/docs/getting-started');
      }}
    >
      <QuestionCircleOutlined />
    </div>
  );
};
