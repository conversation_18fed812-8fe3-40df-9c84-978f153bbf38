import React, { useState, useEffect } from 'react';
import { Select, Spin } from 'antd';
import { request } from '@/utils/request';

export interface SysTenantVO {
  id: number;
  name: string;
}

export interface TenantSelectProps {
  value?: number | number[];
  onChange?: (value: number | number[]) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  mode?: 'multiple' | 'tags';
  allowClear?: boolean;
  disabled?: boolean;
  loading?: boolean;
  showSearch?: boolean;
  filterOption?: boolean | ((inputValue: string, option: any) => boolean);
}

/**
 * 通用的租户选择组件
 * 用于选择所属公司
 */
const TenantSelect: React.FC<TenantSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择所属公司',
  style = { width: '100%' },
  mode,
  allowClear = true,
  disabled = false,
  loading: propLoading = false,
  showSearch = true,
  filterOption = true,
}) => {
  const [tenantList, setTenantList] = useState<SysTenantVO[]>([]);
  const [loading, setLoading] = useState<boolean>(propLoading);

  // 获取租户列表
  const fetchTenantList = async () => {
    try {
      setLoading(true);
      const response = await request('/sys/tenant/getTenantList', {
        method: 'GET',
      });

      if (response && response.success) {
        setTenantList(response.result || []);
      } else {
        console.error('获取租户列表失败:', response?.message || '未知错误');
      }
    } catch (error) {
      console.error('获取租户列表异常:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取租户列表
  useEffect(() => {
    fetchTenantList();
  }, []);

  // 处理选择变化
  const handleChange = (selectedValue: number | number[]) => {
    if (onChange) {
      onChange(selectedValue);
    }
  };

  // 默认的过滤选项方法
  const defaultFilterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      style={style}
      mode={mode}
      allowClear={allowClear}
      disabled={disabled}
      loading={loading}
      showSearch={showSearch}
      filterOption={filterOption === true ? defaultFilterOption : filterOption}
      notFoundContent={loading ? <Spin size="small" /> : null}
      options={tenantList.map(tenant => ({
        label: `${tenant.name}-${tenant.id}`,
        value: tenant.id,
      }))}
    />
  );
};

export default TenantSelect;
