import React, { useState, useEffect, useMemo } from 'react';
import { Transfer, Input, Spin, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { getUserList } from '@/services/system/user';

interface UserSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: React.CSSProperties;
  listStyle?: React.CSSProperties;
  titles?: [string, string];
  disabled?: boolean;
}

interface UserItem {
  id: string;
  username: string;
  realname: string;
  wechatQrcode?: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  value = [],
  onChange,
  style,
  listStyle = { width: 300, height: 300 },
  titles = ['可选用户', '已选用户'],
  disabled = false,
}) => {
  const [users, setUsers] = useState<UserItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [inputValue, setInputValue] = useState(''); // 输入框的值
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
  });

  // 防抖搜索
  const debouncedSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (keyword: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        console.log('UserSelector 防抖搜索触发，关键字:', keyword);
        setSearchKeyword(keyword);
        setPagination(prev => ({ ...prev, current: 1 }));
      }, 300);
    };
  }, []);

  // 获取用户列表
  const fetchUsers = async (keyword = '', pageNo = 1, pageSize = 50) => {
    try {
      setLoading(true);
      console.log('UserSelector 请求参数:', { keyword: keyword.trim(), pageNo, pageSize });

      const response = await getUserList({
        keyword: keyword.trim(),
        pageNo,
        pageSize,
      });

      console.log('UserSelector 接口响应:', response);

      if (response && response.success) {
        let newUsers = [];

        // 处理不同的响应格式
        if (response.result) {
          if (response.result.records) {
            // 新的分页格式
            newUsers = response.result.records;
            setPagination(prev => ({
              ...prev,
              current: pageNo,
              total: response.result.total || 0,
            }));
          } else if (Array.isArray(response.result)) {
            // 旧的数组格式
            newUsers = response.result;
            setPagination(prev => ({
              ...prev,
              current: pageNo,
              total: response.result.length,
            }));
          }
        }

        console.log('UserSelector 处理后的用户数据:', newUsers);

        // 如果是第一页，替换数据；否则追加数据
        if (pageNo === 1) {
          setUsers(newUsers);
        } else {
          setUsers(prev => [...prev, ...newUsers]);
        }
      } else {
        console.error('UserSelector 接口返回失败:', response);
        message.error(response?.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('UserSelector 获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchUsers();
  }, []);

  // 搜索关键字变化时重新加载
  useEffect(() => {
    console.log('UserSelector searchKeyword 变化:', searchKeyword);
    fetchUsers(searchKeyword, 1);
  }, [searchKeyword]);

  // 加载更多数据
  const loadMore = () => {
    if (loading || pagination.current * pagination.pageSize >= pagination.total) {
      return;
    }
    fetchUsers(searchKeyword, pagination.current + 1, pagination.pageSize);
  };

  // 转换数据格式
  const dataSource = users.map(user => ({
    key: user.id,
    title: `${user.realname} (${user.username})`,
    realname: user.realname,
    username: user.username,
  }));

  console.log('UserSelector dataSource:', dataSource);
  console.log('UserSelector value:', value);

  return (
    <div style={style}>
      <div style={{ marginBottom: '8px' }}>
        <Input
          placeholder="搜索用户姓名或账号（后端搜索）"
          prefix={<SearchOutlined />}
          value={inputValue}
          onChange={(e) => {
            const value = e.target.value;
            setInputValue(value);
            debouncedSearch(value);
          }}
          allowClear
          onClear={() => {
            console.log('UserSelector 清空搜索框');
            setInputValue('');
            setSearchKeyword('');
          }}
          suffix={loading && inputValue ? <Spin size="small" /> : null}
        />
        {searchKeyword && (
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            搜索关键字: "{searchKeyword}" | 找到 {users.length} 个用户
          </div>
        )}
      </div>
      <Spin spinning={loading}>
        <Transfer
          dataSource={dataSource}
          targetKeys={value}
          onChange={onChange}
          render={item => item.title}
          listStyle={listStyle}
          titles={titles}
          disabled={disabled}
          showSearch={false} // 禁用内置搜索，使用我们的后端搜索
          onScroll={(direction, e) => {
            // 监听左侧列表滚动，实现无限滚动
            if (direction === 'left') {
              const { target } = e;
              if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
                loadMore();
              }
            }
          }}
          footer={(props) => {
            if (props.direction === 'left' && pagination.current * pagination.pageSize < pagination.total) {
              return (
                <div style={{ textAlign: 'center', padding: '8px' }}>
                  <span style={{ color: '#666', fontSize: '12px' }}>
                    滚动加载更多 ({users.length}/{pagination.total})
                  </span>
                </div>
              );
            }
            return null;
          }}
        />
      </Spin>
    </div>
  );
};

export default UserSelector;
