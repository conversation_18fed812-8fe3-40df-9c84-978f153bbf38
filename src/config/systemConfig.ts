/**
 * 系统全局配置
 */

// 定义公司配置类型
type CompanyConfig = {
  id: string;
  logo: string;
  name: string;
  fullName: string;
  systemName: string;
  systemShortName: string;
  copyright: string;
  icp: string;
};

// 定义公司配置集合类型
type CompanyConfigs = {
  [key: string]: CompanyConfig;
};

// 公司配置信息
const companyConfigs: CompanyConfigs = {
  taiyi: {
    id: 'TaiYi',
    logo: 'taiyi_logo.png',
    name: '太一企业',
    fullName: '福州太一市企业管理有限公司',
    systemName: '太一企业管理系统',
    systemShortName: '太一企业',
    copyright: '©2025 福州太一市企业管理有限公司',
    icp: '闽ICP备2024066292号'
  },
  xudong: {
    id: 'XuDong',
    logo: 'xudong_logo.png',
    name: '旭动企业',
    fullName: '浩旭汽车服务有限公司',
    systemName: '旭动企业管理系统',
    systemShortName: '旭动企业',
    copyright: '©2025 浩旭汽车服务有限公司',
    icp: '鲁ICP备2025154233号'
  }
};

// 获取当前使用的公司配置
const getCurrentCompany = () => {
  // 从公司配置文件中获取当前公司标识
  let companyKey = 'taiyi'; // 默认为太一

  try {
    // 尝试从 company.ts 文件中导入公司标识
    const { CURRENT_COMPANY } = require('./company');
    if (CURRENT_COMPANY && (CURRENT_COMPANY === 'taiyi' || CURRENT_COMPANY === 'xudong')) {
      companyKey = CURRENT_COMPANY;
      console.log(`从 company.ts 获取公司标识: ${companyKey}`);
    }
  } catch (error) {
    console.warn('无法从 company.ts 获取公司标识，使用默认值: taiyi');
  }

  // 确保公司标识有效
  if (!companyConfigs[companyKey]) {
    console.warn(`无效的公司标识: ${companyKey}, 使用默认值: taiyi`);
    companyKey = 'taiyi';
  }

  return companyConfigs[companyKey];
};

// 当前公司配置
const currentCompany = getCurrentCompany();

// 系统配置
const systemConfig = {
  // 系统名称
  systemName: currentCompany.systemName,

  // 系统简称（用于浏览器标签页标题）
  systemShortName: currentCompany.systemShortName,

  // 系统版本
  systemVersion: 'v1.0.0',

  // 公司名称
  companyName: currentCompany.fullName,

  // 备案号
  icp: currentCompany.icp,

  // 版权年份
  copyrightYear: '2025',

  // Logo URL（如果需要自定义Logo）
  logoUrl: `/resource/img/${currentCompany.logo}`,

  // 系统描述
  systemDescription: `${currentCompany.systemName}是一个综合性的多租户管理平台，专为保险和增值服务行业设计。`,

  // 公司ID
  companyId: currentCompany.id,

  // 版权信息
  copyright: currentCompany.copyright,
};

export { companyConfigs };
export default systemConfig;
