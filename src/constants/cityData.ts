import type { CityData } from '@/components/CitySelector';
import { getProvinceTreeData } from '@/utils/geo';

// 获取省市树形数据
const provinceTreeData = getProvinceTreeData();

// 直辖市列表
const directCities = ['北京市', '天津市', '上海市', '重庆市'];

// 将省市树形数据转换为CitySelector组件需要的格式
const convertToCitySelectorFormat = (treeData: any[]): CityData[] => {
  return treeData.map(node => {
    // 判断是否为直辖市
    const isDirect = directCities.includes(node.title);

    // 如果是直辖市，直接返回不带children的节点
    if (isDirect) {
      return {
        title: node.title,
        value: `${node.title}:1`, // 1表示直辖市
        key: node.key,
      };
    }

    // 如果是普通省份
    const result: CityData = {
      title: node.title,
      value: `${node.title}:1`, // 1表示省份
      key: node.key,
    };

    // 添加子城市
    if (node.children && node.children.length > 0) {
      result.children = node.children.map((city: any) => ({
        title: city.title,
        value: `${city.title}:2`, // 2表示地级市
        key: `${city.title}:2`,
      }));
    }

    return result;
  });
};

// 城市数据
export const cityData: CityData[] = convertToCitySelectorFormat(provinceTreeData);

/* 原始数据，保留作为参考
export const cityDataOld: CityData[] = [
  {
    title: '北京市',
    value: '北京市',
    key: '北京市',
    children: [
      {
        title: '北京市',
        value: '北京市:1',
        key: '北京市:1',
      },
    ],
  },
  {
    title: '天津市',
    value: '天津市',
    key: '天津市',
    children: [
      {
        title: '天津市',
        value: '天津市:1',
        key: '天津市:1',
      },
    ],
  },
  {
    title: '河北省',
    value: '河北省',
    key: '河北省',
    children: [
      {
        title: '石家庄市',
        value: '石家庄市:2',
        key: '石家庄市:2',
      },
      {
        title: '唐山市',
        value: '唐山市:2',
        key: '唐山市:2',
      },
      {
        title: '秦皇岛市',
        value: '秦皇岛市:2',
        key: '秦皇岛市:2',
      },
      {
        title: '邯郸市',
        value: '邯郸市:2',
        key: '邯郸市:2',
      },
      {
        title: '邢台市',
        value: '邢台市:2',
        key: '邢台市:2',
      },
    ],
  },
  {
    title: '山西省',
    value: '山西省',
    key: '山西省',
    children: [
      {
        title: '太原市',
        value: '太原市:2',
        key: '太原市:2',
      },
      {
        title: '大同市',
        value: '大同市:2',
        key: '大同市:2',
      },
      {
        title: '阳泉市',
        value: '阳泉市:2',
        key: '阳泉市:2',
      },
    ],
  },
  {
    title: '内蒙古自治区',
    value: '内蒙古自治区',
    key: '内蒙古自治区',
    children: [
      {
        title: '呼和浩特市',
        value: '呼和浩特市:2',
        key: '呼和浩特市:2',
      },
      {
        title: '包头市',
        value: '包头市:2',
        key: '包头市:2',
      },
    ],
  },
  {
    title: '辽宁省',
    value: '辽宁省',
    key: '辽宁省',
    children: [
      {
        title: '沈阳市',
        value: '沈阳市:2',
        key: '沈阳市:2',
      },
      {
        title: '大连市',
        value: '大连市:2',
        key: '大连市:2',
      },
    ],
  },
  {
    title: '吉林省',
    value: '吉林省',
    key: '吉林省',
    children: [
      {
        title: '长春市',
        value: '长春市:2',
        key: '长春市:2',
      },
      {
        title: '吉林市',
        value: '吉林市:2',
        key: '吉林市:2',
      },
    ],
  },
  {
    title: '黑龙江省',
    value: '黑龙江省',
    key: '黑龙江省',
    children: [
      {
        title: '哈尔滨市',
        value: '哈尔滨市:2',
        key: '哈尔滨市:2',
      },
      {
        title: '齐齐哈尔市',
        value: '齐齐哈尔市:2',
        key: '齐齐哈尔市:2',
      },
    ],
  },
  {
    title: '上海市',
    value: '上海市',
    key: '上海市',
    children: [
      {
        title: '上海市',
        value: '上海市:1',
        key: '上海市:1',
      },
    ],
  },
  {
    title: '江苏省',
    value: '江苏省',
    key: '江苏省',
    children: [
      {
        title: '南京市',
        value: '南京市:2',
        key: '南京市:2',
      },
      {
        title: '无锡市',
        value: '无锡市:2',
        key: '无锡市:2',
      },
      {
        title: '徐州市',
        value: '徐州市:2',
        key: '徐州市:2',
      },
      {
        title: '苏州市',
        value: '苏州市:2',
        key: '苏州市:2',
      },
    ],
  },
  {
    title: '浙江省',
    value: '浙江省',
    key: '浙江省',
    children: [
      {
        title: '杭州市',
        value: '杭州市:2',
        key: '杭州市:2',
      },
      {
        title: '宁波市',
        value: '宁波市:2',
        key: '宁波市:2',
      },
      {
        title: '温州市',
        value: '温州市:2',
        key: '温州市:2',
      },
    ],
  },
  {
    title: '安徽省',
    value: '安徽省',
    key: '安徽省',
    children: [
      {
        title: '合肥市',
        value: '合肥市:2',
        key: '合肥市:2',
      },
      {
        title: '芜湖市',
        value: '芜湖市:2',
        key: '芜湖市:2',
      },
    ],
  },
  {
    title: '福建省',
    value: '福建省',
    key: '福建省',
    children: [
      {
        title: '福州市',
        value: '福州市:2',
        key: '福州市:2',
      },
      {
        title: '厦门市',
        value: '厦门市:2',
        key: '厦门市:2',
      },
    ],
  },
  {
    title: '江西省',
    value: '江西省',
    key: '江西省',
    children: [
      {
        title: '南昌市',
        value: '南昌市:2',
        key: '南昌市:2',
      },
      {
        title: '景德镇市',
        value: '景德镇市:2',
        key: '景德镇市:2',
      },
    ],
  },
  {
    title: '山东省',
    value: '山东省',
    key: '山东省',
    children: [
      {
        title: '济南市',
        value: '济南市:2',
        key: '济南市:2',
      },
      {
        title: '青岛市',
        value: '青岛市:2',
        key: '青岛市:2',
      },
    ],
  },
  {
    title: '河南省',
    value: '河南省',
    key: '河南省',
    children: [
      {
        title: '郑州市',
        value: '郑州市:2',
        key: '郑州市:2',
      },
      {
        title: '开封市',
        value: '开封市:2',
        key: '开封市:2',
      },
    ],
  },
  {
    title: '湖北省',
    value: '湖北省',
    key: '湖北省',
    children: [
      {
        title: '武汉市',
        value: '武汉市:2',
        key: '武汉市:2',
      },
      {
        title: '宜昌市',
        value: '宜昌市:2',
        key: '宜昌市:2',
      },
    ],
  },
  {
    title: '湖南省',
    value: '湖南省',
    key: '湖南省',
    children: [
      {
        title: '长沙市',
        value: '长沙市:2',
        key: '长沙市:2',
      },
      {
        title: '株洲市',
        value: '株洲市:2',
        key: '株洲市:2',
      },
    ],
  },
  {
    title: '广东省',
    value: '广东省',
    key: '广东省',
    children: [
      {
        title: '广州市',
        value: '广州市:2',
        key: '广州市:2',
      },
      {
        title: '深圳市',
        value: '深圳市:2',
        key: '深圳市:2',
      },
      {
        title: '珠海市',
        value: '珠海市:2',
        key: '珠海市:2',
      },
      {
        title: '汕头市',
        value: '汕头市:2',
        key: '汕头市:2',
      },
      {
        title: '佛山市',
        value: '佛山市:2',
        key: '佛山市:2',
      },
    ],
  },
  {
    title: '广西壮族自治区',
    value: '广西壮族自治区',
    key: '广西壮族自治区',
    children: [
      {
        title: '南宁市',
        value: '南宁市:2',
        key: '南宁市:2',
      },
      {
        title: '柳州市',
        value: '柳州市:2',
        key: '柳州市:2',
      },
    ],
  },
  {
    title: '海南省',
    value: '海南省',
    key: '海南省',
    children: [
      {
        title: '海口市',
        value: '海口市:2',
        key: '海口市:2',
      },
      {
        title: '三亚市',
        value: '三亚市:2',
        key: '三亚市:2',
      },
    ],
  },
  {
    title: '重庆市',
    value: '重庆市',
    key: '重庆市',
    children: [
      {
        title: '重庆市',
        value: '重庆市:1',
        key: '重庆市:1',
      },
    ],
  },
  {
    title: '四川省',
    value: '四川省',
    key: '四川省',
    children: [
      {
        title: '成都市',
        value: '成都市:2',
        key: '成都市:2',
      },
      {
        title: '自贡市',
        value: '自贡市:2',
        key: '自贡市:2',
      },
    ],
  },
  {
    title: '贵州省',
    value: '贵州省',
    key: '贵州省',
    children: [
      {
        title: '贵阳市',
        value: '贵阳市:2',
        key: '贵阳市:2',
      },
      {
        title: '遵义市',
        value: '遵义市:2',
        key: '遵义市:2',
      },
    ],
  },
  {
    title: '云南省',
    value: '云南省',
    key: '云南省',
    children: [
      {
        title: '昆明市',
        value: '昆明市:2',
        key: '昆明市:2',
      },
      {
        title: '曲靖市',
        value: '曲靖市:2',
        key: '曲靖市:2',
      },
    ],
  },
  {
    title: '西藏自治区',
    value: '西藏自治区',
    key: '西藏自治区',
    children: [
      {
        title: '拉萨市',
        value: '拉萨市:2',
        key: '拉萨市:2',
      },
    ],
  },
  {
    title: '陕西省',
    value: '陕西省',
    key: '陕西省',
    children: [
      {
        title: '西安市',
        value: '西安市:2',
        key: '西安市:2',
      },
      {
        title: '铜川市',
        value: '铜川市:2',
        key: '铜川市:2',
      },
    ],
  },
  {
    title: '甘肃省',
    value: '甘肃省',
    key: '甘肃省',
    children: [
      {
        title: '兰州市',
        value: '兰州市:2',
        key: '兰州市:2',
      },
      {
        title: '嘉峪关市',
        value: '嘉峪关市:2',
        key: '嘉峪关市:2',
      },
    ],
  },
  {
    title: '青海省',
    value: '青海省',
    key: '青海省',
    children: [
      {
        title: '西宁市',
        value: '西宁市:2',
        key: '西宁市:2',
      },
    ],
  },
  {
    title: '宁夏回族自治区',
    value: '宁夏回族自治区',
    key: '宁夏回族自治区',
    children: [
      {
        title: '银川市',
        value: '银川市:2',
        key: '银川市:2',
      },
      {
        title: '石嘴山市',
        value: '石嘴山市:2',
        key: '石嘴山市:2',
      },
    ],
  },
  {
    title: '新疆维吾尔自治区',
    value: '新疆维吾尔自治区',
    key: '新疆维吾尔自治区',
    children: [
      {
        title: '乌鲁木齐市',
        value: '乌鲁木齐市:2',
        key: '乌鲁木齐市:2',
      },
      {
        title: '克拉玛依市',
        value: '克拉玛依市:2',
        key: '克拉玛依市:2',
      },
    ],
  },
  {
    title: '台湾省',
    value: '台湾省',
    key: '台湾省',
    children: [
      {
        title: '台北市',
        value: '台北市:2',
        key: '台北市:2',
      },
      {
        title: '高雄市',
        value: '高雄市:2',
        key: '高雄市:2',
      },
    ],
  },
  {
    title: '香港特别行政区',
    value: '香港特别行政区',
    key: '香港特别行政区',
    children: [
      {
        title: '香港',
        value: '香港:3',
        key: '香港:3',
      },
    ],
  },
  {
    title: '澳门特别行政区',
    value: '澳门特别行政区',
    key: '澳门特别行政区',
    children: [
      {
        title: '澳门',
        value: '澳门:3',
        key: '澳门:3',
      },
    ],
  },
];
*/
