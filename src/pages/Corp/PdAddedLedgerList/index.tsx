import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Table, Button, Space, message, Popconfirm, DatePicker, Input, Form, Row, Col, Select, Tag, Upload } from 'antd';
import { PlusOutlined, ExportOutlined, ImportOutlined, DeleteOutlined, EditOutlined, EyeOutlined, InboxOutlined, UploadOutlined } from '@ant-design/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import type { FilterValue } from 'antd/es/table/interface';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import { useAccess, Access } from 'umi';
import { request, uploadFile } from '@/utils/request';
import TenantSelect from '@/components/TenantSelect';
import PdAddedLedgerModal from './components/PdAddedLedgerModal';
// 导入弹框组件，已注释
// import PdLedgerImportModal from '../PdLedgerList/components/PdLedgerImportModal';

const { RangePicker } = DatePicker;

const PdAddedLedgerList: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [showFooter, setShowFooter] = useState<boolean>(true);
  // 注释掉弹框状态
  // const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  // 添加文件上传状态
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [searchForm] = Form.useForm();

  // 权限控制
  const access = useAccess();

  // 获取列表数据
  const fetchData = async (params: any = {}) => {
    try {
      setLoading(true);
      const { current, pageSize, ...restParams } = params;

      // 处理日期范围
      if (restParams.dateRange && restParams.dateRange.length === 2) {
        restParams.startDate = restParams.dateRange[0];
        restParams.endDate = restParams.dateRange[1];
        delete restParams.dateRange;
      }

      // 处理多选的所属公司
      if (restParams.tenantList && Array.isArray(restParams.tenantList) && restParams.tenantList.length > 0) {
        // 保持原样，后端会处理数组参数
        console.log('多选的所属公司:', restParams.tenantList);
      }

      const response = await request(`/corp/pdAddedLedger/list/${current}/${pageSize}`, {
        method: 'POST',
        data: restParams,
      });

      if (response && response.success) {
        setDataSource(response.result.records || []);
        setPagination({
          ...pagination,
          current,
          pageSize,
          total: response.result.total || 0,
        });
      } else {
        message.error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载数据
  useEffect(() => {
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  }, []);

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: any,
  ) => {
    fetchData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters,
      ...searchForm.getFieldsValue(),
      orderBy: sorter.field && sorter.order
        ? `${sorter.field} ${sorter.order === 'ascend' ? 'asc' : 'desc'}`
        : undefined,
    });
  };

  // 搜索表单提交
  const onFinish = (values: any) => {
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
      ...values,
    });
  };

  // 重置搜索表单
  const onReset = () => {
    searchForm.resetFields();
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  // 新增按钮点击
  const handleAdd = () => {
    setCurrentRecord(null);
    setIsUpdate(false);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 编辑按钮点击
  const handleEdit = (record: any) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 详情按钮点击
  const handleDetail = (record: any) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(false);
    setModalVisible(true);
  };

  // 删除按钮点击
  const handleDelete = async (id: string) => {
    try {
      const response = await request('/corp/pdAddedLedger/delete', {
        method: 'DELETE',
        params: { id },
      });

      if (response && response.success) {
        message.success('删除成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await request('/corp/pdAddedLedger/deleteBatch', {
        method: 'DELETE',
        data: { ids: selectedRowKeys },
      });

      if (response && response.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  // 导出
  const handleExport = () => {
    // 使用Blob下载文件
    const token = localStorage.getItem('token') || '';
    const headers = new Headers();
    headers.append('X-Access-Token', token);
    headers.append('Content-Type', 'application/json');

    const formValues = searchForm.getFieldsValue();

    fetch('/corp/pdAddedLedger/exportXls', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(formValues),
    })
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '增值服务台账.xls';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch(error => {
        console.error('导出失败:', error);
        message.error('导出失败');
      });
  };

  // 导入 - 注释掉原来的弹框方式
  /*
  const handleImport = () => {
    setImportModalVisible(true);
  };

  // 导入成功回调
  const handleImportSuccess = async (data: any) => {
    setImportModalVisible(false);

    // 打印收到的数据
    console.log('收到的导入数据:', data);
    console.log('文件对象:', data.file);

    // 构建FormData
    const formData = new FormData();
    if (data.file) {
      formData.append('file', data.file);
      console.log('添加文件到FormData成功');
    } else {
      console.error('没有文件对象!');
      message.error('没有文件对象!');
      return;
    }

    // 添加其他参数
    if (data.startTime) formData.append('startTime', data.startTime);
    if (data.endTime) formData.append('endTime', data.endTime);
    if (data.importType) formData.append('importType', String(data.importType));
    if (data.provinces) formData.append('provinces', data.provinces);
    if (data.provincesWithLevel) formData.append('provincesWithLevel', data.provincesWithLevel);
    if (data.fieldData) formData.append('fieldData', data.fieldData);

    // 检查FormData内容
    console.log('FormData内容:');
    for (const pair of formData.entries()) {
      console.log(pair[0] + ': ' + (pair[0] === 'file' ? '文件对象' : pair[1]));
    }

    // 显示加载中提示
    const loadingMessage = message.loading('正在导入数据，请稍候...', 0);

    try {
      // 使用统一的上传函数
      const result = await uploadFile('/corp/pdAddedLedger/importExcel', formData);

      // 关闭加载提示
      loadingMessage();

      console.log('响应结果:', result);

      if (result.success) {
        message.success('导入成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(result.message || '导入失败');
      }
    } catch (error) {
      // 关闭加载提示
      loadingMessage();
      console.error('导入失败:', error);
      message.error('导入失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };
  */

  // 新的直接文件上传方式
  // 处理文件上传前检查
  const beforeUpload = (file: RcFile) => {
    const isExcel = file.type === 'application/vnd.ms-excel' ||
                   file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   /\.(xlsx|xls)$/i.test(file.name);

    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于10MB!');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  // 处理上传变化
  const handleUploadChange: UploadProps['onChange'] = (info) => {
    if (info.file.status === 'uploading') {
      setUploading(true);
      setFileList([info.file]);
      return;
    }

    if (info.file.status === 'done') {
      setUploading(false);
      setFileList([info.file]);

      if (info.file.response && info.file.response.success) {
        message.success('导入成功');
        // 刷新数据
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(info.file.response?.message || '导入失败');
      }
    }

    if (info.file.status === 'error') {
      setUploading(false);
      message.error('导入失败');
    }
  };

  // 模态框关闭后刷新
  const handleSuccess = () => {
    setModalVisible(false);
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm.getFieldsValue(),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      sorter: true,
      render: (text: string) => text ? text.substring(0, 10) : '',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
    },
    {
      title: '选择服务',
      dataIndex: 'userItem',
      key: 'userItem',
      sorter: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      sorter: true,
    },
    {
      title: '聊天记录',
      dataIndex: 'isVied',
      key: 'isVied',
      sorter: true,
      render: (text: any) => {
        // 确保处理字符串和数字类型
        const status = String(text);
        if (status === '0') return <Tag color="default">未生成</Tag>;
        if (status === '1') return <Tag color="processing">生成中</Tag>;
        if (status === '2') return <Tag color="success">已生成</Tag>;
        return <Tag color="default">未知</Tag>;
      },
    },
    {
      title: '主体名称',
      dataIndex: 'tenantName',
      key: 'tenantName',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="small">
          <Access accessible={access.hasPermission('corp:pd_added_ledger:edit')}>
            <Button type="link" size="small" onClick={() => handleEdit(record)}>
              编辑
            </Button>
          </Access>
          <Access accessible={access.hasPermission('corp:pd_added_ledger:delete')}>
            <Popconfirm
              title="确定要删除这条记录吗?"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger>
                删除
              </Button>
            </Popconfirm>
          </Access>
          <Button type="link" size="small" onClick={() => handleDetail(record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer title="增值服务台账">
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          name="searchForm"
          layout="inline"
          onFinish={onFinish}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16} style={{ width: '100%' }}>
            <Col span={8}>
              <Form.Item name="tenantList" label="所属公司">
                <TenantSelect mode="multiple" placeholder="请选择所属公司" allowClear />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="name" label="姓名">
                <Input placeholder="请输入姓名" allowClear />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="chatStatus" label="生成聊天记录">
                <Select
                  placeholder="请选择"
                  allowClear
                  style={{ width: '100%' }}
                  options={[
                    { label: '生成中', value: 1 },
                    { label: '未生成', value: 0 },
                    { label: '已生成', value: 2 },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16} style={{ width: '100%', marginTop: 8 }}>
            <Col span={8}>
              <Form.Item name="dateRange" label="日期范围">
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right' }}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button onClick={onReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Access accessible={access.hasPermission('corp:pd_added_ledger:add')}>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增
              </Button>
            </Access>
            <Access accessible={access.hasPermission('corp:pd_added_ledger:exportXls')}>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
            </Access>
            <Access accessible={access.hasPermission('corp:pd_added_ledger:importExcel')}>
              <Upload
                name="file"
                action="/corp/pdAddedLedger/importExcel"
                headers={{
                  'X-Access-Token': localStorage.getItem('token') || '',
                }}
                beforeUpload={beforeUpload}
                onChange={handleUploadChange}
                showUploadList={false}
                accept=".xlsx,.xls"
              >
                <Button icon={<ImportOutlined />} loading={uploading}>
                  导入
                </Button>
              </Upload>
            </Access>
            {selectedRowKeys.length > 0 && (
              <Access accessible={access.hasPermission('corp:pd_added_ledger:deleteBatch')}>
                <Popconfirm
                  title="确定要删除选中的记录吗?"
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    批量删除
                  </Button>
                </Popconfirm>
              </Access>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <div>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            pagination={pagination}
            onChange={handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
            }}
          />
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <span>总数量: {pagination.total} 条</span>
          </div>
        </div>
      </Card>

      {/* 表单弹窗 */}
      <PdAddedLedgerModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleSuccess}
        record={currentRecord}
        isUpdate={isUpdate}
        showFooter={showFooter}
      />

      {/* 导入模态框 - 已注释
      <PdLedgerImportModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={handleImportSuccess}
      /> */}
    </PageContainer>
  );
};

export default PdAddedLedgerList;
