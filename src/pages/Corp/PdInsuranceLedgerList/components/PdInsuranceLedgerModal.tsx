import React, { useEffect } from 'react';
import { Modal, Form, Input, DatePicker, Radio, message } from 'antd';
import moment from 'moment';
import { request } from '@/utils/request';

interface PdInsuranceLedgerModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdInsuranceLedgerModal: React.FC<PdInsuranceLedgerModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();
  
  // 当弹窗打开或记录变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      
      if (isUpdate && record) {
        // 处理日期字段
        const formData = { ...record };
        if (formData.orderDate) {
          formData.orderDate = moment(formData.orderDate);
        }
        
        form.setFieldsValue(formData);
      }
    }
  }, [visible, record, isUpdate, form]);
  
  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理日期格式
      if (values.orderDate) {
        values.orderDate = values.orderDate.format('YYYY-MM-DD');
      }
      
      const url = isUpdate ? '/corp/pdInsuranceLedger/edit' : '/corp/pdInsuranceLedger/add';
      const response = await request(url, {
        method: 'POST',
        data: values,
      });
      
      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    }
  };
  
  // 表单布局
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  
  return (
    <Modal
      title={!isUpdate ? '新增' : showFooter ? '编辑' : '详情'}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={800}
      destroyOnClose
      maskClosable={false}
      okButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
      cancelButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
    >
      <Form
        form={form}
        {...formItemLayout}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>
        
        <Form.Item
          name="orderDate"
          label="日期"
          rules={[{ required: true, message: '请选择日期' }]}
        >
          <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>
        
        <Form.Item
          name="name"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>
        
        <Form.Item
          name="userItem"
          label="选择服务"
          rules={[{ required: true, message: '请输入选择的服务' }]}
        >
          <Input placeholder="请输入选择的服务" />
        </Form.Item>
        
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1\d{10}$/, message: '请输入正确的手机号' }
          ]}
        >
          <Input placeholder="请输入手机号" disabled />
        </Form.Item>
        
        <Form.Item
          name="isVied"
          label="是否生成聊天记录"
          initialValue={0}
        >
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdInsuranceLedgerModal;
