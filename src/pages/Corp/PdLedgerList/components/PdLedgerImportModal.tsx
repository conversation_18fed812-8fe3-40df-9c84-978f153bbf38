import React, { useState, useEffect } from 'react';
import { Modal, Form, DatePicker, Input, message, InputNumber, Radio, Space, Tooltip } from 'antd';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import TenantSelect from '@/components/TenantSelect';
import { InfoCircleOutlined } from '@ant-design/icons';

interface PdLedgerImportModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (data: any) => void;
}

const { RangePicker } = DatePicker;

const PdLedgerImportModal: React.FC<PdLedgerImportModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [inputMode, setInputMode] = useState<'count' | 'ratio'>('ratio');
  const [calculatedValue, setCalculatedValue] = useState<number | null>(null);

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      setCalculatedValue(null);
      setInputMode('ratio');
    }
  }, [visible, form]);

  // 计算比例或台账数
  const calculateValue = () => {
    const clickNum = form.getFieldValue('clickNum');
    const recordNum = form.getFieldValue('recordNum');

    if (!clickNum || !recordNum) {
      setCalculatedValue(null);
      return;
    }

    if (inputMode === 'count') {
      // 用户输入的是台账数，计算比例
      const ratio = Math.min(100, Math.round((recordNum / clickNum) * 100));
      setCalculatedValue(ratio);
    } else {
      // 用户输入的是比例，计算台账数
      const count = Math.round((recordNum / 100) * clickNum);
      setCalculatedValue(count);
    }
  };

  // 当点击数或台账数/比例变化时重新计算
  useEffect(() => {
    calculateValue();
  }, [form.getFieldValue('clickNum'), form.getFieldValue('recordNum'), inputMode]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();

      // 处理日期范围
      let startDate = '';
      let endDate = '';
      if (values.dateRange && values.dateRange.length === 2) {
        startDate = values.dateRange[0].format('YYYY-MM-DD');
        endDate = values.dateRange[1].format('YYYY-MM-DD');
      }

      // 处理城市代码
      const cityCodes = values.provinces ? values.provinces.map((item: string) => {
        const [city] = item.split(':');
        return city;
      }) : [];

      // 计算最终的比例值
      let finalRatio = values.recordNum;
      if (inputMode === 'count') {
        // 如果用户输入的是台账数，转换为比例
        finalRatio = Math.min(100, Math.round((values.recordNum / values.clickNum) * 100));
      }

      // 构建提交数据
      const submitData = {
        startDate,
        endDate,
        recordNum: finalRatio.toString(), // 后端接收的是比例值，1表示1%
        clickNum: values.clickNum,
        tenantId: values.tenantIds,
        cityCodes
      };

      console.log('提交的数据:', submitData);

      // 调用父组件的成功回调
      onSuccess(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败，请检查输入');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title="系统补全配置"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      okButtonProps={{ loading: submitting }}
      width={700}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item
          name="dateRange"
          label="开始/结束日期"
          rules={[{ required: true, message: '请选择日期范围' }]}
        >
          <RangePicker style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="clickNum"
          label="点击数"
          rules={[{ required: true, message: '请输入点击数' }]}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={1}
            placeholder="请输入点击数"
            onChange={() => calculateValue()}
          />
        </Form.Item>

        <Form.Item label="输入方式">
          <Radio.Group
            value={inputMode}
            onChange={(e) => {
              setInputMode(e.target.value);
              form.setFieldsValue({ recordNum: undefined });
              setCalculatedValue(null);
            }}
          >
            <Radio value="ratio">按比例</Radio>
            <Radio value="count">按台账数</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="recordNum"
          label={inputMode === 'ratio' ? "台账占比(%)" : "台账数量"}
          rules={[{ required: true, message: inputMode === 'ratio' ? '请输入台账占比' : '请输入台账数量' }]}
          tooltip={inputMode === 'ratio'
            ? "台账数占点击数的比例，取值范围1-100，例如：80表示80%的点击生成台账"
            : "生成的台账数量，不能超过点击数"}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={1}
            max={inputMode === 'ratio' ? 100 : form.getFieldValue('clickNum')}
            precision={0}
            placeholder={inputMode === 'ratio' ? "请输入台账占比(1-100%)" : "请输入台账数量"}
            onChange={() => calculateValue()}
            addonAfter={inputMode === 'ratio' ? "%" : "条"}
          />
        </Form.Item>

        {calculatedValue !== null && (
          <Form.Item label="计算结果" style={{ marginBottom: 0 }}>
            <div style={{ padding: '8px 12px', background: '#f5f5f5', borderRadius: '4px' }}>
              {inputMode === 'ratio'
                ? `预计生成台账数量: ${calculatedValue} 条`
                : `台账占比: ${calculatedValue}%`}
            </div>
          </Form.Item>
        )}

        <Form.Item
          name="tenantIds"
          label="选择租户"
          rules={[{ required: true, message: '请选择租户' }]}
        >
          <TenantSelect
            mode="multiple"
            placeholder="请选择租户(可多选)"
            allowClear
          />
        </Form.Item>

        <Form.Item
          name="provinces"
          label="选择省份/城市"
          rules={[{ required: true, message: '请选择省份/城市' }]}
        >
          <FormCitySelector
            name="provinces"
            cityData={cityData}
            placeholder="请选择省份或城市(可多选)"
            showSelected={false}
            required={false}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdLedgerImportModal;
