import React, { useEffect } from 'react';
import { Modal, Form, Input, DatePicker, Radio, message } from 'antd';
import moment from 'moment';
import { addPdLedger, editPdLedger } from '@/services/corp/pdLedger';

interface PdLedgerModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdLedgerModal: React.FC<PdLedgerModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();
  
  // 当弹窗打开或记录变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      
      if (isUpdate && record) {
        // 处理日期字段
        const formData = { ...record };
        if (formData.signDate) {
          formData.signDate = moment(formData.signDate);
        }
        
        form.setFieldsValue(formData);
      }
    }
  }, [visible, record, isUpdate, form]);
  
  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理日期格式
      if (values.signDate) {
        values.signDate = values.signDate.format('YYYY-MM-DD');
      }
      
      const submitFunc = isUpdate ? editPdLedger : addPdLedger;
      const response = await submitFunc(values);
      
      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    }
  };
  
  // 表单布局
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  
  return (
    <Modal
      title={!isUpdate ? '新增' : showFooter ? '编辑' : '详情'}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={800}
      destroyOnClose
      maskClosable={false}
      okButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
      cancelButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
    >
      <Form
        form={form}
        {...formItemLayout}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>
        
        <Form.Item
          name="signDate"
          label="签单日期"
          rules={[{ required: true, message: '请选择签单日期' }]}
        >
          <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>
        
        <Form.Item
          name="policyholder"
          label="投保人"
          rules={[{ required: true, message: '请输入投保人' }]}
        >
          <Input placeholder="请输入投保人" />
        </Form.Item>
        
        <Form.Item
          name="insured"
          label="被保险人"
          rules={[{ required: true, message: '请输入被保险人' }]}
        >
          <Input placeholder="请输入被保险人" />
        </Form.Item>
        
        <Form.Item
          name="phoneNumber"
          label="手机号"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1\d{10}$/, message: '请输入正确的手机号' }
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>
        
        <Form.Item
          name="licensePlate"
          label="车牌号"
          rules={[{ required: true, message: '请输入车牌号' }]}
        >
          <Input placeholder="请输入车牌号" />
        </Form.Item>
        
        <Form.Item
          name="brandModel"
          label="车型"
          rules={[{ required: true, message: '请输入车型' }]}
        >
          <Input placeholder="请输入车型" />
        </Form.Item>
        
        <Form.Item
          name="vin"
          label="车架号"
          rules={[{ required: true, message: '请输入车架号' }]}
        >
          <Input placeholder="请输入车架号" />
        </Form.Item>
        
        <Form.Item
          name="insuranceName"
          label="保险名称"
          rules={[{ required: true, message: '请输入保险名称' }]}
        >
          <Input placeholder="请输入保险名称" />
        </Form.Item>
        
        <Form.Item
          name="chatStatus"
          label="是否生成聊天记录"
          initialValue={0}
        >
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdLedgerModal;
