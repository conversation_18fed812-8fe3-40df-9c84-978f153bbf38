import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Modal, Form, DatePicker, message } from 'antd';
import { rangeDeletePdLedger } from '@/services/corp/pdLedger';
import TenantSelect from '@/components/TenantSelect';

interface PdLedgerRangeDeleteModalProps {
  onSuccess: () => void;
}

const { RangePicker } = DatePicker;

const PdLedgerRangeDeleteModal = forwardRef<any, PdLedgerRangeDeleteModalProps>(({ onSuccess }, ref) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState<boolean>(false);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    register: (show: boolean) => {
      setVisible(show);
      if (!show) {
        form.resetFields();
      }
    }
  }));

  // 取消
  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };

  // 提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 处理日期范围和租户ID列表
      const data = {
        startDate: values.dateRange[0].format('YYYY-MM-DD'),
        endDate: values.dateRange[1].format('YYYY-MM-DD'),
        tenantIdList: values.tenantIdList.map(id => id.toString()),
      };

      // 调用删除接口
      const response = await rangeDeletePdLedger(data);

      if (response && response.success) {
        message.success('范围删除成功');
        handleCancel();
        onSuccess();
      } else {
        message.error(response.message || '范围删除失败');
      }
    } catch (error) {
      console.error('范围删除失败:', error);
    }
  };



  return (
    <Modal
      title="范围删除"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      width={700}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item
          name="dateRange"
          label="日期范围"
          rules={[{ required: true, message: '请选择日期范围' }]}
        >
          <RangePicker style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tenantIdList"
          label="所属公司"
          rules={[{ required: true, message: '请选择所属公司' }]}
        >
          <TenantSelect
            placeholder="请选择所属公司"
            allowClear
            showSearch
            mode="multiple"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default PdLedgerRangeDeleteModal;
