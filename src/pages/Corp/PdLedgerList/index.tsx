import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Table, Button, Space, message, Popconfirm, DatePicker, Input, Form, Row, Col, Select, Tag } from 'antd';
import { PlusOutlined, ExportOutlined, ImportOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import type { FilterValue } from 'antd/es/table/interface';
// 权限控制已移除
import { fetchPdLedgerList, deletePdLedger, batchDeletePdLedger, generateChatRecord, rangeDeletePdLedger, systemCompletion } from '@/services/corp/pdLedger';
import { uploadFile } from '@/utils/request';
import PdLedgerModal from './components/PdLedgerModal';
import PdLedgerImportModal from './components/PdLedgerImportModal';
import PdLedgerRangeDeleteModal from './components/PdLedgerRangeDeleteModal';
import TenantSelect from '@/components/TenantSelect';

const { RangePicker } = DatePicker;

// 定义车险台账数据类型
interface PdLedgerItem {
  id: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  insuranceName?: string; // 保险名称
  phoneNumber?: string; // 手机号
  signDate?: string; // 签单日期
  signDateTime?: string; // 签单时间
  policyholder?: string; // 投保人
  insured?: string; // 被保险人
  licensePlate?: string; // 车牌号
  brandModel?: string; // 车型
  vin?: string; // 车架号
  isDelete?: number;
  chatStatus?: number; // 聊天记录状态
  tenantId?: string;
  tenantName?: string; // 租户名称
  hasChatUser?: string;
  [key: string]: any; // 其他可能的字段
}

const PdLedgerList: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<PdLedgerItem[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [showFooter, setShowFooter] = useState<boolean>(true);
  const [searchForm] = Form.useForm();
  const rangeDeleteModalRef = useRef<any>(null);

  // 权限控制已移除

  // 获取列表数据
  const fetchData = async (params: any = {}) => {
    try {
      setLoading(true);
      const { current, pageSize, ...restParams } = params;

      // 处理日期范围
      if (restParams.dateRange && restParams.dateRange.length === 2) {
        // 使用与旧系统一致的参数名称
        const startDate = restParams.dateRange[0].format('YYYY-MM-DD');
        const endDate = restParams.dateRange[1].format('YYYY-MM-DD');
        restParams.startDate = startDate;
        restParams.endDate = endDate;
        delete restParams.dateRange;
      }

      // 确保 chatStatus 是字符串类型
      if (restParams.chatStatus !== undefined) {
        restParams.chatStatus = String(restParams.chatStatus);
      }

      // 处理多选的所属公司
      if (restParams.tenantList && Array.isArray(restParams.tenantList) && restParams.tenantList.length > 0) {
        // 保持原样，后端会处理数组参数
        console.log('多选的所属公司:', restParams.tenantList);
      }

      const response = await fetchPdLedgerList({
        current: current || pagination.current,
        pageSize: pageSize || pagination.pageSize,
        ...restParams,
      });

      if (response && response.success) {
        setDataSource(response.result?.records || []);
        setPagination({
          ...pagination,
          current: current || pagination.current,
          pageSize: pageSize || pagination.pageSize,
          total: response.result?.total || 0,
        });
      } else {
        message.error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载数据
  useEffect(() => {
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  }, []);

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: any,
  ) => {
    fetchData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters,
      ...searchForm.getFieldsValue(),
      orderBy: sorter.field && sorter.order
        ? `${sorter.field} ${sorter.order === 'ascend' ? 'asc' : 'desc'}`
        : undefined,
    });
  };

  // 搜索表单提交
  const onFinish = (values: any) => {
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
      ...values,
    });
  };

  // 重置搜索表单
  const onReset = () => {
    searchForm.resetFields();
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  // 新增按钮点击
  const handleAdd = () => {
    setCurrentRecord(null);
    setIsUpdate(false);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 编辑按钮点击
  const handleEdit = (record: PdLedgerItem) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 详情按钮点击
  const handleDetail = (record: PdLedgerItem) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(false);
    setModalVisible(true);
  };

  // 删除按钮点击
  const handleDelete = async (id: string) => {
    try {
      const response = await deletePdLedger(id);

      if (response && response.success) {
        message.success('删除成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await batchDeletePdLedger(selectedRowKeys as string[]);

      if (response && response.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  // 导出
  const handleExport = () => {
    // 使用Blob下载文件
    const token = localStorage.getItem('token') || '';
    const headers = new Headers();
    headers.append('X-Access-Token', token);
    headers.append('Content-Type', 'application/json');

    const formValues = searchForm.getFieldsValue();

    fetch('/corp/pdLedger/exportXls', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(formValues),
    })
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '车险台账.xls';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch(error => {
        console.error('导出失败:', error);
        message.error('导出失败');
      });
  };

  // 系统补全
  const handleImport = () => {
    setImportModalVisible(true);
  };

  // 导入成功回调
  const handleImportSuccess = async (data: any) => {
    setImportModalVisible(false);

    // 打印收到的数据
    console.log('收到的系统补全数据:', data);

    // 显示加载中提示
    const loadingMessage = message.loading('正在创建系统补全任务，请稍候...', 0);

    try {
      // 调用系统补全接口
      const result = await systemCompletion(data);

      // 关闭加载提示
      loadingMessage();

      console.log('响应结果:', result);

      if (result.success) {
        message.success(result.message || '系统补全任务已创建');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(result.message || '创建系统补全任务失败');
      }
    } catch (error) {
      // 关闭加载提示
      loadingMessage();
      console.error('创建系统补全任务失败:', error);
      message.error('创建系统补全任务失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 范围删除
  const handleRangeDelete = () => {
    if (rangeDeleteModalRef.current) {
      rangeDeleteModalRef.current.register(true);
    }
  };

  // 生成聊天记录
  const handleGenerateChatRecord = async () => {
    try {
      const response = await generateChatRecord();

      if (response && response.success) {
        message.success('生成聊天记录成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '生成聊天记录失败');
      }
    } catch (error) {
      console.error('生成聊天记录失败:', error);
      message.error('生成聊天记录失败');
    }
  };

  // 模态框关闭后刷新
  const handleSuccess = () => {
    setModalVisible(false);
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm.getFieldsValue(),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '签单日期',
      dataIndex: 'signDate',
      key: 'signDate',
      sorter: true,
      render: (text: string) => text || '',
    },
    {
      title: '投保人',
      dataIndex: 'policyholder',
      key: 'policyholder',
      sorter: true,
    },
    {
      title: '被保险人',
      dataIndex: 'insured',
      key: 'insured',
      sorter: true,
    },
    {
      title: '手机号',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      sorter: true,
      render: (text: string) => text || '',
    },
    {
      title: '车牌号',
      dataIndex: 'licensePlate',
      key: 'licensePlate',
      sorter: true,
    },
    {
      title: '车型',
      dataIndex: 'brandModel',
      key: 'brandModel',
      sorter: true,
    },
    {
      title: '保险名称',
      dataIndex: 'insuranceName',
      key: 'insuranceName',
      sorter: true,
      ellipsis: true,
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      key: 'vin',
      sorter: true,
      ellipsis: true,
    },
    {
      title: '聊天记录',
      dataIndex: 'chatStatus',
      key: 'chatStatus',
      sorter: true,
      render: (text: any) => {
        // 确保处理字符串和数字类型
        const status = String(text);
        if (status === '0') return <Tag color="default">未生成</Tag>;
        if (status === '1') return <Tag color="processing">生成中</Tag>;
        if (status === '2') return <Tag color="success">已生成</Tag>;
        return <Tag color="default">未知</Tag>;
      },
    },
    {
      title: '主体名称',
      dataIndex: 'tenantName',
      key: 'tenantName',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PdLedgerItem) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            <EditOutlined /> 编辑
          </Button>
          <Popconfirm
            title="确定删除此记录吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger>
              <DeleteOutlined /> 删除
            </Button>
          </Popconfirm>
          <Button type="link" size="small" onClick={() => handleDetail(record)}>
            <EyeOutlined /> 详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          name="search"
          layout="inline"
          onFinish={onFinish}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16} style={{ width: '100%' }}>
            <Col span={8}>
              <Form.Item name="tenantList" label="所属公司">
                <TenantSelect mode="multiple" placeholder="请选择所属公司" allowClear />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="chatStatus" label="生成聊天记录">
                <Select placeholder="请选择" allowClear>
                  <Select.Option value="1">生成中</Select.Option>
                  <Select.Option value="0">未生成</Select.Option>
                  <Select.Option value="2">已生成</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="dateRange" label="日期范围">
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16} style={{ width: '100%', marginTop: 8 }}>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button onClick={onReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
            <Button icon={<ImportOutlined />} onClick={handleImport}>
              系统补全
            </Button>
            {selectedRowKeys.length > 0 && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
              >
                批量删除
              </Button>
            )}
            <Button onClick={handleRangeDelete}>
              范围删除
            </Button>
            <Button onClick={handleGenerateChatRecord}>
              生成聊天记录
            </Button>
          </Space>
        </div>

        {/* 表格 */}
        <div>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            pagination={pagination}
            onChange={handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: (keys) => setSelectedRowKeys(keys),
            }}
            scroll={{ x: 1300 }}
          />
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <span>总数量: {pagination.total} 条</span>
          </div>
        </div>
      </Card>

      {/* 新增/编辑/详情模态框 */}
      <PdLedgerModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleSuccess}
        record={currentRecord}
        isUpdate={isUpdate}
        showFooter={showFooter}
      />

      {/* 导入模态框 */}
      <PdLedgerImportModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={handleImportSuccess}
      />

      {/* 范围删除模态框 */}
      <PdLedgerRangeDeleteModal
        ref={rangeDeleteModalRef}
        onSuccess={() => fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        })}
      />
    </PageContainer>
  );
};

export default PdLedgerList;
