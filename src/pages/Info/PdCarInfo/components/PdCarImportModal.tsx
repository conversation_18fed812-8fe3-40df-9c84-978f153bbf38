import React, { useState } from 'react';
import { Modal, Form, DatePicker, Select, Upload, Button, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { uploadImportFile } from '@/services/info/pdCarInfo';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface PdCarImportModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const PdCarImportModal: React.FC<PdCarImportModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 重置表单
  const resetForm = () => {
    form.resetFields();
    setFileList([]);
  };

  // 关闭弹窗
  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  // 文件上传前的验证
  const beforeUpload = (file: RcFile) => {
    const isExcel = 
      file.type === 'application/vnd.ms-excel' || 
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return false;
    }
    
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于10MB!');
      return false;
    }
    
    setFileList([file]);
    return false; // 阻止自动上传
  };

  // 移除文件
  const handleRemove = () => {
    setFileList([]);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (fileList.length === 0 && values.importType === 1) {
        message.error('请选择要导入的文件');
        return;
      }
      
      setLoading(true);
      
      // 处理日期范围
      const dateRange = values.dateRange || [];
      const startTime = dateRange[0]?.format('YYYY-MM-DD');
      const endTime = dateRange[1]?.format('YYYY-MM-DD');
      
      // 上传文件
      const file = fileList[0]?.originFileObj;
      const response = await uploadImportFile(file, {
        startTime,
        endTime,
        importType: values.importType,
      });
      
      if (response.success) {
        message.success(response.message || '导入成功');
        handleCancel();
        onSuccess();
      } else {
        message.error(response.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="导入配置"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={700}
      destroyOnClose
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
      >
        <Form.Item
          name="dateRange"
          label="开始/结束日期"
        >
          <RangePicker format="YYYY-MM-DD" />
        </Form.Item>
        
        <Form.Item
          name="importType"
          label="导入方式"
          rules={[{ required: true, message: '请选择导入方式' }]}
          initialValue={1}
        >
          <Select placeholder="请选择导入方式">
            <Option value={1}>取表格</Option>
            <Option value={2}>随机</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name="file"
          label="选择文件"
        >
          <div style={{ padding: '8px 0' }}>
            <p style={{ color: '#999', marginBottom: '8px' }}>支持 .xls, .xlsx 格式</p>
            <Upload
              fileList={fileList}
              beforeUpload={beforeUpload}
              onRemove={handleRemove}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />} type="primary">
                选择文件
              </Button>
            </Upload>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdCarImportModal;
