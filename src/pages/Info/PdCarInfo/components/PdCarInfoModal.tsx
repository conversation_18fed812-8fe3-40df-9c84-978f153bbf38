import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { PdCarInfoItem } from '../data';
import { addPdCarInfo, editPdCarInfo } from '@/services/info/pdCarInfo';
import { formFieldsConfig } from '../data';
import { isTenantIdZero } from '@/utils/tenant';

interface PdCarInfoModalProps {
  visible: boolean;
  title: string;
  record?: PdCarInfoItem;
  onCancel: () => void;
  onSuccess: () => void;
  showFooter?: boolean;
}

const PdCarInfoModal: React.FC<PdCarInfoModalProps> = ({
  visible,
  title,
  record,
  onCancel,
  onSuccess,
  showFooter = true,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  // 表单初始化
  useEffect(() => {
    if (visible) {
      form.resetFields();
      
      if (record) {
        // 编辑模式，设置表单值
        form.setFieldsValue({
          ...record,
        });
      }
      
      // 如果不显示底部按钮，则禁用表单
      setDisabled(!showFooter);
    }
  }, [visible, record, form, showFooter]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const isUpdate = !!record;
      const submitData = {
        ...values,
        id: record?.id,
      };
      
      const response = isUpdate 
        ? await editPdCarInfo(submitData)
        : await addPdCarInfo(submitData);
      
      if (response.success) {
        message.success(isUpdate ? '编辑成功' : '新增成功');
        onSuccess();
      } else {
        message.error(response.message || (isUpdate ? '编辑失败' : '新增失败'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染表单项
  const renderFormItems = () => {
    return formFieldsConfig.map((field) => {
      if (field.hidden) return null;
      
      switch (field.type) {
        case 'input':
          return (
            <Form.Item
              key={field.name}
              name={field.name}
              label={field.label}
              rules={field.rules}
            >
              <Input placeholder={`请输入${field.label}`} disabled={disabled} />
            </Form.Item>
          );
        case 'select':
          return (
            <Form.Item
              key={field.name}
              name={field.name}
              label={field.label}
              rules={field.rules}
            >
              <Select
                placeholder={`请选择${field.label}`}
                disabled={disabled}
                showSearch
                allowClear
              >
                {/* 这里可以添加选项 */}
              </Select>
            </Form.Item>
          );
        default:
          return null;
      }
    });
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnClose
      okButtonProps={{ style: { display: showFooter ? 'inline-block' : 'none' } }}
      cancelButtonProps={{ style: { display: showFooter ? 'inline-block' : 'none' } }}
    >
      <Form
        form={form}
        layout="vertical"
        disabled={disabled}
      >
        {renderFormItems()}
        
        {/* 隐藏字段 */}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdCarInfoModal;
