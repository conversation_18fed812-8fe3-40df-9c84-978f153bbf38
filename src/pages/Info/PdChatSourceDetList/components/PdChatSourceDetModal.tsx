import React, { useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, message } from 'antd';
import { addPdChatSourceDet, editPdChatSourceDet } from '@/services/info/pdChatSourceDet';

interface PdChatSourceDetModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdChatSourceDetModal: React.FC<PdChatSourceDetModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();
  
  // 当弹窗打开或记录变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      
      if (isUpdate && record) {
        form.setFieldsValue(record);
      }
    }
  }, [visible, record, isUpdate, form]);
  
  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const submitFunc = isUpdate ? editPdChatSourceDet : addPdChatSourceDet;
      const response = await submitFunc(values);
      
      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    }
  };
  
  // 表单布局
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  
  return (
    <Modal
      title={!isUpdate ? '新增' : showFooter ? '编辑' : '详情'}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={800}
      destroyOnClose
      maskClosable={false}
      okButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
      cancelButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
    >
      <Form
        form={form}
        {...formItemLayout}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>
        
        <Form.Item
          name="message"
          label="发送内容"
          rules={[{ required: true, message: '请输入发送内容' }]}
        >
          <Input.TextArea rows={4} placeholder="请输入发送内容" />
        </Form.Item>
        
        <Form.Item
          name="sendType"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
        >
          <Select
            placeholder="请选择类型"
            options={[
              { label: '用户', value: 0 },
              { label: '客服', value: 1 },
            ]}
          />
        </Form.Item>
        
        <Form.Item
          name="num"
          label="对应回合"
          rules={[{ required: true, message: '请输入对应回合' }]}
        >
          <InputNumber style={{ width: '100%' }} placeholder="请输入对应回合" min={1} />
        </Form.Item>
        
        
      </Form>
    </Modal>
  );
};

export default PdChatSourceDetModal;
