import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, Table, Button, Space, message, Popconfirm, Form, Row, Col, Select, Input } from 'antd';
import { PlusOutlined, ExportOutlined, ImportOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import type { FilterValue } from 'antd/es/table/interface';
import { fetchPdChatSourceDetList, deletePdChatSourceDet, batchDeletePdChatSourceDet } from '@/services/info/pdChatSourceDet';
import PdChatSourceDetModal from './components/PdChatSourceDetModal';

interface PdChatSourceDetItem {
  id: string;
  message?: string;
  sendType?: number;
  num?: number;
  pid?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  [key: string]: any;
}

const PdChatSourceDetList: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<PdChatSourceDetItem[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [showFooter, setShowFooter] = useState<boolean>(true);
  const [searchForm] = Form.useForm();

  // 获取数据
  const fetchData = async (params: any = {}) => {
    setLoading(true);
    try {
      const response = await fetchPdChatSourceDetList({
        current: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });

      if (response && response.success) {
        setDataSource(response.result.records || []);
        setPagination({
          ...pagination,
          current: response.result.current,
          pageSize: response.result.size,
          total: response.result.total,
        });
      } else {
        message.error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载数据
  useEffect(() => {
    fetchData();
  }, []);

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: any
  ) => {
    fetchData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...searchForm.getFieldsValue(),
      sortField: sorter.field,
      sortOrder: sorter.order,
    });
  };

  // 搜索表单提交
  const onFinish = (values: any) => {
    fetchData({
      ...values,
      current: 1,
    });
  };

  // 重置搜索表单
  const onReset = () => {
    searchForm.resetFields();
    fetchData({
      current: 1,
    });
  };

  // 新增
  const handleAdd = () => {
    setCurrentRecord(null);
    setIsUpdate(false);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 编辑
  const handleEdit = (record: PdChatSourceDetItem) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 查看详情
  const handleDetail = (record: PdChatSourceDetItem) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(false);
    setModalVisible(true);
  };

  // 删除
  const handleDelete = async (id: string) => {
    try {
      const response = await deletePdChatSourceDet(id);
      if (response && response.success) {
        message.success('删除成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await batchDeletePdChatSourceDet(selectedRowKeys as string[]);
      if (response && response.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  // 导出
  const handleExport = () => {
    const token = localStorage.getItem('token') || '';
    const url = `/info/pdChatSourceDet/exportXls?token=${token}`;
    window.open(url);
  };

  // 操作成功回调
  const handleSuccess = () => {
    setModalVisible(false);
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm.getFieldsValue(),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '创建日期',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
    },
    {
      title: '发送内容',
      dataIndex: 'message',
      key: 'message',
      sorter: true,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'sendType',
      key: 'sendType',
      sorter: true,
      render: (text: number) => text === 0 ? '用户' : '客服',
    },
    {
      title: '对应回合',
      dataIndex: 'num',
      key: 'num',
      sorter: true,
    },

    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PdChatSourceDetItem) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            <EditOutlined /> 编辑
          </Button>
          <Popconfirm
            title="确定删除此记录吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger>
              <DeleteOutlined /> 删除
            </Button>
          </Popconfirm>
          <Button type="link" size="small" onClick={() => handleDetail(record)}>
            <EyeOutlined /> 详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer title="聊天源详情管理">
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          name="searchForm"
          layout="inline"
          onFinish={onFinish}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16} style={{ width: '100%' }}>
            <Col span={8}>
              <Form.Item name="sendType" label="类型">
                <Select
                  placeholder="请选择类型"
                  allowClear
                  options={[
                    { label: '用户', value: 0 },
                    { label: '客服', value: 1 },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right' }}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button onClick={onReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
            {selectedRowKeys.length > 0 && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
              >
                批量删除
              </Button>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <div>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            pagination={pagination}
            onChange={handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: (keys) => setSelectedRowKeys(keys),
            }}
            scroll={{ x: 1200 }}
          />
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <span>总数量: {pagination.total} 条</span>
          </div>
        </div>
      </Card>

      {/* 新增/编辑/详情模态框 */}
      <PdChatSourceDetModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleSuccess}
        record={currentRecord}
        isUpdate={isUpdate}
        showFooter={showFooter}
      />
    </PageContainer>
  );
};

export default PdChatSourceDetList;
