import React, { useEffect } from 'react';
import { Modal, Form, Select, Radio, message } from 'antd';
import moment from 'moment';
import { addPdChatSource, editPdChatSource } from '@/services/info/pdChatSource';

interface PdChatSourceModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdChatSourceModal: React.FC<PdChatSourceModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();

  // 当弹窗打开或记录变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (isUpdate && record) {
        form.setFieldsValue(record);
      }
    }
  }, [visible, record, isUpdate, form]);

  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      const submitFunc = isUpdate ? editPdChatSource : addPdChatSource;
      const response = await submitFunc(values);

      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    }
  };

  // 表单布局
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  return (
    <Modal
      title={!isUpdate ? '新增' : showFooter ? '编辑' : '详情'}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={800}
      destroyOnClose
      maskClosable={false}
      okButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
      cancelButtonProps={{ style: { display: showFooter ? 'inline' : 'none' } }}
    >
      <Form
        form={form}
        {...formItemLayout}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <input />
        </Form.Item>

        <Form.Item
          name="chatType"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
        >
          <Select
            placeholder="请选择类型"
            options={[
              { label: '车险', value: 0 },
              { label: '财险', value: 1 },
              { label: '增值服务', value: 2 },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="isUse"
          label="使用状态"
          initialValue={1}
          rules={[{ required: true, message: '请选择使用状态' }]}
        >
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PdChatSourceModal;
