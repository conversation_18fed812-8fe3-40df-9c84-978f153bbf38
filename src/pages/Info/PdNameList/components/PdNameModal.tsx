import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, List, Typography, message, Badge, Switch } from 'antd';
import { RightOutlined, DeleteOutlined } from '@ant-design/icons';
import { addPdScene, editPdScene } from '@/services/info/pdScene';
import './PdNameModal.less';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface PdNameModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdNameModal: React.FC<PdNameModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();
  const [inputText, setInputText] = useState<string>('');
  const [nameList, setNameList] = useState<string[]>([]);
  const [converting, setConverting] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 当弹窗打开或记录变化时，初始化数据
  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (isUpdate && record) {
        // 编辑模式：直接设置表单值
        form.setFieldsValue({
          ...record,
          isUse: record.isUse === '1', // 转换为布尔值用于Switch组件
        });
      } else if (!isUpdate) {
        // 新增模式：重置状态
        setInputText('');
        setNameList([]);
        form.setFieldsValue({
          sourceType: 2,
          isUse: true,
        });
      }
    }
  }, [visible, record, isUpdate, form]);

  // 转换姓名（仅用于新增模式）
  const handleConvert = () => {
    const text = inputText.trim();
    if (!text) {
      message.warning('请输入姓名');
      return;
    }

    setConverting(true);

    try {
      // 只使用英文逗号分割姓名
      const names = text.split(',').filter(name => name.trim());

      // 去重
      const uniqueNames = [...new Set(names)];

      // 更新姓名列表
      setNameList(uniqueNames);
      message.success(`成功转换 ${uniqueNames.length} 个姓名`);
    } catch (error) {
      console.error('转换姓名出错:', error);
      message.error('转换姓名出错，请检查输入格式');
    } finally {
      setConverting(false);
    }
  };

  // 清空输入（仅用于新增模式）
  const handleClear = () => {
    setInputText('');
    setNameList([]);
  };

  // 删除单个姓名（仅用于新增模式）
  const handleDeleteName = (index: number) => {
    const newList = [...nameList];
    newList.splice(index, 1);
    setNameList(newList);
  };

  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 构建提交的数据
      let submitData;

      if (isUpdate) {
        // 编辑模式：直接提交表单值
        submitData = {
          ...values,
          id: record.id,
          sourceType: 2,  // 2表示姓名库
          isUse: values.isUse ? '1' : '0', // 将布尔值转换为字符串
        };
      } else {
        // 新增模式：需要先转换姓名
        if (nameList.length === 0) {
          message.warning('请先转换姓名');
          setSubmitting(false);
          return;
        }

        submitData = {
          ...values,
          sceneName: nameList.join(','),  // 姓名字符串，逗号分隔
          nameList: nameList,  // 姓名数组
          sourceType: 2,  // 2表示姓名库
          isUse: values.isUse ? '1' : '0', // 将布尔值转换为字符串
        };
      }

      const submitFunc = isUpdate ? editPdScene : addPdScene;
      const response = await submitFunc(submitData);

      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染编辑模式的表单
  const renderEditForm = () => {
    return (
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sourceType"
          initialValue={2}
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sceneName"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <TextArea
            placeholder="请输入姓名"
            autoSize={{ minRows: 4, maxRows: 8 }}
          />
        </Form.Item>

        <Form.Item
          name="isUse"
          label="是否使用"
          valuePropName="checked"
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>
      </Form>
    );
  };

  // 渲染新增模式的表单
  const renderAddForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sourceType"
          initialValue={2}
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="isUse"
          label="是否使用"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>

        <div className="name-editor">
          <div className="name-input-panel">
            <div className="panel-header">
              <Title level={5}>
                输入姓名
                <Badge count="英文逗号分隔" style={{ backgroundColor: '#1890ff', marginLeft: 8 }} />
              </Title>
              <Text type="secondary">已输入: {inputText.length} 个字符</Text>
            </div>
            <TextArea
              className="name-textarea"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请输入姓名，多个姓名用英文逗号(,)分隔，例如：张三,李四"
            />
          </div>

          <div className="name-actions">
            <Button
              type="primary"
              icon={<RightOutlined />}
              onClick={handleConvert}
              loading={converting}
            >
              转换
            </Button>
            <Button onClick={handleClear}>清空</Button>
          </div>

          <div className="name-result-panel">
            <div className="panel-header">
              <Title level={5}>
                处理后的姓名
                <Badge count={`共 ${nameList.length} 个`} style={{ backgroundColor: '#52c41a', marginLeft: 8 }} />
              </Title>
            </div>
            <div className="name-list-container">
              {nameList.length > 0 ? (
                <List
                  size="small"
                  bordered
                  dataSource={nameList}
                  style={{ height: '100%', overflow: 'auto' }}
                  renderItem={(item, index) => (
                    <List.Item
                      actions={[
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteName(index)}
                        >
                          删除
                        </Button>
                      ]}
                    >
                      <Text>{index + 1}. {item}</Text>
                    </List.Item>
                  )}
                />
              ) : (
                <div className="empty-list">
                  <Text type="secondary">暂无数据，请先转换姓名</Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </Form>
    );
  };

  return (
    <Modal
      title={!isUpdate ? '新增姓名库' : showFooter ? '编辑姓名库' : '姓名库详情'}
      open={visible}
      onCancel={onCancel}
      width={isUpdate ? 600 : 1000}
      destroyOnClose
      maskClosable={false}
      bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }} // 设置最大高度和滚动
      footer={showFooter ? [
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitting} onClick={handleOk}>
          确认
        </Button>,
      ] : [
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      {isUpdate ? renderEditForm() : renderAddForm()}
    </Modal>
  );
};

export default PdNameModal;
