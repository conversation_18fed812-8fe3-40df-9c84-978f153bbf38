.scene-editor {
  display: flex;
  gap: 16px;
  height: 500px; /* 设置固定高度 */
}

.scene-input-panel, .scene-result-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h5 {
    margin: 0;
  }
}

.scene-textarea {
  flex: 1;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  height: calc(100% - 40px); /* 减去标题的高度 */
  overflow-y: auto;
}

.scene-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.scene-list-container {
  flex: 1;
  height: calc(100% - 40px); /* 减去标题的高度 */
  overflow: hidden; /* 防止双重滚动条 */
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  /* 列表内部滚动 */
  .ant-list {
    height: 100%;
    overflow-y: auto;
  }
}

.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}

// 响应式布局
@media (max-width: 768px) {
  .scene-editor {
    flex-direction: column;
  }

  .scene-actions {
    flex-direction: row;
    margin: 16px 0;
  }
}
