import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, List, Typography, Divider, message, Badge, Switch } from 'antd';
import { RightOutlined, DeleteOutlined } from '@ant-design/icons';
import { addPdScene, editPdScene } from '@/services/info/pdScene';
import './PdSceneModal.less';

const { TextArea } = Input;
const { Text, Title } = Typography;
const { Option } = Select;

interface PdSceneModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: any;
  isUpdate: boolean;
  showFooter: boolean;
}

const PdSceneModal: React.FC<PdSceneModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  isUpdate,
  showFooter,
}) => {
  const [form] = Form.useForm();
  const [inputText, setInputText] = useState<string>('');
  const [sceneList, setSceneList] = useState<string[]>([]);
  const [converting, setConverting] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [serverType, setServerType] = useState<string>('0');

  // 当弹窗打开或记录变化时，初始化数据
  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (isUpdate && record) {
        // 编辑模式：直接设置表单值
        form.setFieldsValue({
          ...record,
          isUse: record.isUse === '1', // 转换为布尔值用于Switch组件
        });
        setServerType(record.serverType || '0');
      } else if (!isUpdate) {
        // 新增模式：重置状态
        setInputText('');
        setSceneList([]);
        setServerType('0');
        form.setFieldsValue({
          sourceType: 1,
          serverType: '0',
          isUse: true,
        });
      }
    }
  }, [visible, record, isUpdate, form]);

  // 处理业务类型变化
  const handleServerTypeChange = (value: string) => {
    setServerType(value);
  };

  // 转换场景（仅用于新增模式）
  const handleConvert = () => {
    const text = inputText.trim();
    if (!text) {
      message.warning('请输入场景');
      return;
    }

    setConverting(true);

    try {
      // 只使用英文逗号分割场景
      const scenes = text.split(',').filter(scene => scene.trim());

      // 去重
      const uniqueScenes = [...new Set(scenes)];

      // 更新场景列表
      setSceneList(uniqueScenes);
      message.success(`成功转换 ${uniqueScenes.length} 个场景`);
    } catch (error) {
      console.error('转换场景出错:', error);
      message.error('转换场景出错，请检查输入格式');
    } finally {
      setConverting(false);
    }
  };

  // 清空输入（仅用于新增模式）
  const handleClear = () => {
    setInputText('');
    setSceneList([]);
  };

  // 删除单个场景（仅用于新增模式）
  const handleDeleteScene = (index: number) => {
    const newList = [...sceneList];
    newList.splice(index, 1);
    setSceneList(newList);
  };

  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 构建提交的数据
      let submitData;

      if (isUpdate) {
        // 编辑模式：直接提交表单值
        submitData = {
          ...values,
          id: record.id,
          sourceType: 1,  // 1表示场景库
          isUse: values.isUse ? '1' : '0', // 将布尔值转换为字符串
        };
      } else {
        // 新增模式：需要先转换场景
        if (sceneList.length === 0) {
          message.warning('请先转换场景');
          setSubmitting(false);
          return;
        }

        submitData = {
          ...values,
          sceneName: sceneList.join(','),  // 场景字符串，逗号分隔
          sceneList: sceneList,  // 场景数组
          sourceType: 1,  // 1表示场景库
          isUse: values.isUse ? '1' : '0', // 将布尔值转换为字符串
        };
      }

      const submitFunc = isUpdate ? editPdScene : addPdScene;
      const response = await submitFunc(submitData);

      if (response && response.success) {
        message.success(`${isUpdate ? '编辑' : '新增'}成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${isUpdate ? '编辑' : '新增'}失败:`, error);
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染编辑模式的表单
  const renderEditForm = () => {
    return (
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sourceType"
          initialValue={1}
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="serverType"
          label="业务类型"
          rules={[{ required: true, message: '请选择业务类型' }]}
        >
          <Select placeholder="请选择业务类型" onChange={handleServerTypeChange}>
            <Option value="0">车险</Option>
            <Option value="1">财险</Option>
            <Option value="2">增值服务</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="sceneName"
          label="场景名称"
          rules={[{ required: true, message: '请输入场景名称' }]}
        >
          <TextArea
            placeholder="请输入场景名称"
            autoSize={{ minRows: 4, maxRows: 8 }}
          />
        </Form.Item>

        <Form.Item
          name="isUse"
          label="是否使用"
          valuePropName="checked"
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>
      </Form>
    );
  };

  // 渲染新增模式的表单
  const renderAddForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        disabled={!showFooter}
      >
        <Form.Item
          name="id"
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sourceType"
          initialValue={1}
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="serverType"
          label="业务类型"
          rules={[{ required: true, message: '请选择业务类型' }]}
          initialValue={serverType}
        >
          <Select
            placeholder="请选择业务类型"
            onChange={handleServerTypeChange}
          >
            <Option value="0">车险</Option>
            <Option value="1">财险</Option>
            <Option value="2">增值服务</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="isUse"
          label="是否使用"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>

        <div className="scene-editor">
          <div className="scene-input-panel">
            <div className="panel-header">
              <Title level={5}>
                输入场景
                <Badge count="英文逗号分隔" style={{ backgroundColor: '#1890ff', marginLeft: 8 }} />
              </Title>
              <Text type="secondary">已输入: {inputText.length} 个字符</Text>
            </div>
            <TextArea
              className="scene-textarea"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请输入场景，多个场景用英文逗号(,)分隔，例如：我的车泡水了,你好划痕险怎么赔"
              autoSize={{ minRows: 10, maxRows: 20 }}
            />
          </div>

          <div className="scene-actions">
            <Button
              type="primary"
              icon={<RightOutlined />}
              onClick={handleConvert}
              loading={converting}
            >
              转换
            </Button>
            <Button onClick={handleClear}>清空</Button>
          </div>

          <div className="scene-result-panel">
            <div className="panel-header">
              <Title level={5}>
                处理后的场景
                <Badge count={`共 ${sceneList.length} 个`} style={{ backgroundColor: '#52c41a', marginLeft: 8 }} />
              </Title>
            </div>
            <div className="scene-list-container">
              {sceneList.length > 0 ? (
                <List
                  size="small"
                  bordered
                  dataSource={sceneList}
                  style={{ height: '100%', overflow: 'auto' }}
                  renderItem={(item, index) => (
                    <List.Item
                      actions={[
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteScene(index)}
                        >
                          删除
                        </Button>
                      ]}
                    >
                      <Text>{index + 1}. {item}</Text>
                    </List.Item>
                  )}
                />
              ) : (
                <div className="empty-list">
                  <Text type="secondary">暂无数据，请先转换场景</Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </Form>
    );
  };

  return (
    <Modal
      title={!isUpdate ? '新增场景库' : showFooter ? '编辑场景库' : '场景库详情'}
      open={visible}
      onCancel={onCancel}
      width={isUpdate ? 600 : 1000}
      destroyOnClose
      maskClosable={false}
      bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }} // 设置最大高度和滚动
      footer={showFooter ? [
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitting} onClick={handleOk}>
          确认
        </Button>,
      ] : [
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      {isUpdate ? renderEditForm() : renderAddForm()}
    </Modal>
  );
};

export default PdSceneModal;
