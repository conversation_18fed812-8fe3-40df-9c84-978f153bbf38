import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Table, Button, Space, message, Row, Col, Statistic, Divider, Progress, Tabs, Tag } from 'antd';
import { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, CarOutlined, BankOutlined, GiftOutlined } from '@ant-design/icons';
import { getQueueStats, getQueueStatsByType, triggerTaskConsumer } from '@/services/info/chatSourceTask';
import './index.less';

// 创建静态图标
const staticLoadingIcon = <LoadingOutlined spin={false} style={{ color: '#1890ff' }} />;
const staticSyncIcon = <SyncOutlined spin={false} style={{ color: '#faad14' }} />;

const { TabPane } = Tabs;

// 定义队列统计信息类型
interface QueueStatsItem {
  linkType: number;
  pendingCount: number;
  processingCount: number;
  successCount: number;
  failedCount: number;
  totalCount: number;
  successRate: number;
}

interface QueueStatsData {
  typeStats: QueueStatsItem[];
  totalStats: QueueStatsItem;
}

const PdTaskCenter: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [triggering, setTriggering] = useState<Record<number, boolean>>({});
  const [statsData, setStatsData] = useState<QueueStatsData | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');

  // 获取队列统计信息
  const fetchQueueStats = async () => {
    try {
      setRefreshing(true);
      const response = await getQueueStats();
      if (response && response.success) {
        setStatsData(response.result);
      } else {
        message.error(response.message || '获取队列统计信息失败');
      }
    } catch (error) {
      console.error('获取队列统计信息失败:', error);
      message.error('获取队列统计信息失败');
    } finally {
      setRefreshing(false);
    }
  };

  // 获取指定类型的队列统计信息
  const fetchQueueStatsByType = async (linkType: number) => {
    try {
      setLoading(true);
      const response = await getQueueStatsByType(linkType);
      if (response && response.success) {
        // 更新特定类型的统计信息
        if (statsData) {
          const updatedTypeStats = [...statsData.typeStats];
          const index = updatedTypeStats.findIndex(item => item.linkType === linkType);
          if (index !== -1) {
            updatedTypeStats[index] = response.result;
            setStatsData({
              ...statsData,
              typeStats: updatedTypeStats
            });
          }
        }
      } else {
        message.error(response.message || `获取类型 ${linkType} 的队列统计信息失败`);
      }
    } catch (error) {
      console.error(`获取类型 ${linkType} 的队列统计信息失败:`, error);
      message.error(`获取类型 ${linkType} 的队列统计信息失败`);
    } finally {
      setLoading(false);
    }
  };

  // 手动触发任务消费者
  const handleTriggerTaskConsumer = async (linkType: number) => {
    try {
      setTriggering({ ...triggering, [linkType]: true });
      const response = await triggerTaskConsumer(linkType);
      if (response && response.success) {
        message.success(`成功触发类型 ${getLinkTypeName(linkType)} 的任务消费者`);
        // 触发成功后刷新统计信息
        fetchQueueStatsByType(linkType);
      } else {
        message.error(response.message || `触发类型 ${getLinkTypeName(linkType)} 的任务消费者失败`);
      }
    } catch (error) {
      console.error(`触发类型 ${getLinkTypeName(linkType)} 的任务消费者失败:`, error);
      message.error(`触发类型 ${getLinkTypeName(linkType)} 的任务消费者失败`);
    } finally {
      setTriggering({ ...triggering, [linkType]: false });
    }
  };

  // 首次加载数据
  useEffect(() => {
    fetchQueueStats();
    // 设置定时刷新（每30秒刷新一次）
    const timer = setInterval(() => {
      fetchQueueStats();
    }, 30000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  // 获取链接类型名称
  const getLinkTypeName = (linkType: number): string => {
    switch (linkType) {
      case 0:
        return '车险';
      case 1:
        return '财险';
      case 2:
        return '增值服务';
      default:
        return `未知类型(${linkType})`;
    }
  };

  // 获取链接类型图标
  const getLinkTypeIcon = (linkType: number) => {
    switch (linkType) {
      case 0:
        return <CarOutlined />;
      case 1:
        return <BankOutlined />;
      case 2:
        return <GiftOutlined />;
      default:
        return null;
    }
  };

  // 获取状态图标
  const getStatusIcon = (type: 'pending' | 'processing' | 'success' | 'failed') => {
    switch (type) {
      case 'pending':
        return null; // 待处理不显示图标
      case 'processing':
        return staticSyncIcon;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  // 获取状态标签
  const getStatusTag = (count: number, type: 'pending' | 'processing' | 'success' | 'failed') => {
    if (count === 0) return null;

    const icon = getStatusIcon(type);
    let color;

    switch (type) {
      case 'pending':
        color = 'blue';
        break;
      case 'processing':
        color = 'orange';
        break;
      case 'success':
        color = 'green';
        break;
      case 'failed':
        color = 'red';
        break;
      default:
        color = 'default';
    }

    return (
      <Space>
        {icon}
        <Tag color={color}>{count}</Tag>
      </Space>
    );
  };

  // 渲染统计卡片
  const renderStatsCard = (stats: QueueStatsItem, isTotal: boolean = false) => {
    const title = isTotal ? '聊天源任务' : getLinkTypeName(stats.linkType);
    const icon = isTotal ? null : getLinkTypeIcon(stats.linkType);

    return (
      <Card
        title={
          <Space direction="vertical" size={0}>
            <Space>
              {icon}
              <span>{title}</span>
            </Space>
            {isTotal && <div style={{ fontSize: '12px', color: '#999' }}>(基于场景库生成系统配置中对应类型的聊天源数量)</div>}
          </Space>
        }
        bordered={false}
        style={{ marginBottom: 16 }}
        extra={
          !isTotal && (
            <Button
              type="primary"
              icon={staticSyncIcon}
              loading={triggering[stats.linkType]}
              onClick={() => handleTriggerTaskConsumer(stats.linkType)}
            >
              触发处理
            </Button>
          )
        }
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="待处理"
              value={stats.pendingCount}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="处理中"
              value={stats.processingCount}
              valueStyle={{ color: '#faad14' }}
              prefix={staticSyncIcon}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功"
              value={stats.successCount}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="失败"
              value={stats.failedCount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<CloseCircleOutlined />}
            />
          </Col>
        </Row>
        <Divider />
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="总任务数"
              value={stats.totalCount}
            />
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 8 }}>成功率</div>
            <Progress
              percent={stats.successRate}
              status={stats.successRate < 100 ? "active" : "success"}
              format={(percent) => `${percent?.toFixed(2)}%`}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染表格
  const renderTable = () => {
    const columns = [
      {
        title: '类型',
        dataIndex: 'linkType',
        key: 'linkType',
        render: (text: number) => (
          <Space>
            {getLinkTypeIcon(text)}
            {getLinkTypeName(text)}
          </Space>
        ),
      },
      {
        title: '待处理',
        dataIndex: 'pendingCount',
        key: 'pendingCount',
        render: (text: number) => getStatusTag(text, 'pending'),
      },
      {
        title: '处理中',
        dataIndex: 'processingCount',
        key: 'processingCount',
        render: (text: number) => getStatusTag(text, 'processing'),
      },
      {
        title: '成功',
        dataIndex: 'successCount',
        key: 'successCount',
        render: (text: number) => getStatusTag(text, 'success'),
      },
      {
        title: '失败',
        dataIndex: 'failedCount',
        key: 'failedCount',
        render: (text: number) => getStatusTag(text, 'failed'),
      },
      {
        title: '总任务数',
        dataIndex: 'totalCount',
        key: 'totalCount',
      },
      {
        title: '成功率',
        dataIndex: 'successRate',
        key: 'successRate',
        render: (text: number) => (
          <Progress
            percent={text}
            status={text < 100 ? "active" : "success"}
            format={(percent) => `${percent?.toFixed(2)}%`}
            size="small"
            style={{ width: 120 }}
          />
        ),
      },
      {
        title: '操作',
        key: 'action',
        render: (_: any, record: QueueStatsItem) => (
          <Button
            type="primary"
            size="small"
            icon={staticSyncIcon}
            loading={triggering[record.linkType]}
            onClick={() => handleTriggerTaskConsumer(record.linkType)}
          >
            触发处理
          </Button>
        ),
      },
    ];

    return (
      <Table
        rowKey="linkType"
        loading={loading || refreshing}
        dataSource={statsData?.typeStats || []}
        columns={columns}
        pagination={false}
        footer={() => (
          <div style={{ fontWeight: 'bold' }}>
            总计: {statsData?.totalStats.totalCount || 0} 条任务
          </div>
        )}
      />
    );
  };

  return (
    <PageContainer
      title="任务中心"
      extra={[
        <Button
          key="refresh"
          type="primary"
          icon={staticSyncIcon}
          loading={refreshing}
          onClick={fetchQueueStats}
        >
          刷新数据
        </Button>,
      ]}
    >
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="all">
            {statsData && (
              <>
                {renderStatsCard(statsData.totalStats, true)}
                <Row gutter={16}>
                  {statsData.typeStats.map((stats) => (
                    <Col span={8} key={stats.linkType}>
                      {renderStatsCard(stats)}
                    </Col>
                  ))}
                </Row>
              </>
            )}
          </TabPane>
          <TabPane tab="详细数据" key="details">
            {renderTable()}
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default PdTaskCenter;
