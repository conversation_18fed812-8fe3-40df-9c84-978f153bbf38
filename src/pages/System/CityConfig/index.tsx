import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Row,
  Col,
  Tooltip,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SyncOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  searchCityConfigs,
  addCityConfig,
  editCityConfig,
  deleteCityConfig,
  getCityConfigById,
  getParentCities,
  syncCityData,
  type CityConfig,
  type CityConfigSearchDTO,
  type ParentCity,
} from '@/services/system/cityConfig';

const { Option } = Select;

const CityConfig: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<CityConfig[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<CityConfig | null>(null);
  const [parentCities, setParentCities] = useState<ParentCity[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<CityConfigSearchDTO>({
    pageNo: 1,
    pageSize: 10,
  });
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 示例数据
  const exampleData = {
    cityCode: '224200',
    regionCityName: '东台市',
    platePrefix: '苏J',
    allCityName: '江苏省东台市',
    topCityName: '江苏省',
    parentCityCode: '320000', // 江苏省的编码
  };

  // 获取城市配置列表
  const fetchCityConfigs = async (params?: CityConfigSearchDTO) => {
    setLoading(true);
    try {
      const searchData = params || searchParams;
      const response = await searchCityConfigs(searchData);
      if (response.success) {
        setDataSource(response.result?.records || []);
        setPagination({
          current: response.result?.current || 1,
          pageSize: response.result?.size || 10,
          total: response.result?.total || 0,
        });
      } else {
        message.error('获取城市配置失败');
      }
    } catch (error) {
      message.error('获取城市配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索
  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const newSearchParams = {
      ...searchParams,
      ...values,
      pageNo: 1,
    };
    setSearchParams(newSearchParams);
    fetchCityConfigs(newSearchParams);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    const newSearchParams = {
      pageNo: 1,
      pageSize: 10,
    };
    setSearchParams(newSearchParams);
    fetchCityConfigs(newSearchParams);
  };

  // 获取上级城市列表
  const fetchParentCities = async () => {
    try {
      const response = await getParentCities();
      if (response.success) {
        setParentCities(response.result || []);
      } else {
        message.error('获取上级城市列表失败');
      }
    } catch (error) {
      message.error('获取上级城市列表失败');
    }
  };

  useEffect(() => {
    fetchCityConfigs();
    fetchParentCities();
  }, []);

  // 表格列定义
  const columns: ColumnsType<CityConfig> = [
    {
      title: '城市编码',
      dataIndex: 'cityCode',
      key: 'cityCode',
      width: 120,
    },
    {
      title: '完整城市名称',
      dataIndex: 'allCityName',
      key: 'allCityName',
      width: 200,
    },
    {
      title: '顶级城市名称',
      dataIndex: 'topCityName',
      key: 'topCityName',
      width: 150,
    },
    {
      title: '车牌前缀',
      dataIndex: 'platePrefix',
      key: 'platePrefix',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 新增
  const handleAdd = () => {
    setEditingRecord(null);
    setModalVisible(true);
    form.resetFields();
    // 如果上级城市数据为空，重新获取
    if (parentCities.length === 0) {
      fetchParentCities();
    }
  };

  // 填充示例数据
  const fillExampleData = () => {
    form.setFieldsValue(exampleData);
    message.success('已填充示例数据');
  };

  // 编辑
  const handleEdit = async (record: CityConfig) => {
    try {
      const response = await getCityConfigById(record.id);
      if (response.success) {
        setEditingRecord(response.result);
        setModalVisible(true);
        form.setFieldsValue(response.result);
      } else {
        message.error('获取城市配置详情失败');
      }
    } catch (error) {
      message.error('获取城市配置详情失败');
    }
  };

  // 删除
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteCityConfig(id);
      if (response.success) {
        message.success('删除成功');
        fetchCityConfigs();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 同步城市数据
  const handleSync = async () => {
    setLoading(true);
    try {
      const response = await syncCityData();
      if (response.success) {
        message.success('同步成功');
        fetchCityConfigs();
      } else {
        message.error(response.message || '同步失败');
      }
    } catch (error) {
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const data = editingRecord ? { ...values, id: editingRecord.id } : values;

      const response = editingRecord
        ? await editCityConfig(data)
        : await addCityConfig(data);

      if (response.success) {
        message.success(editingRecord ? '编辑成功' : '添加成功');
        setModalVisible(false);
        fetchCityConfigs();
      } else {
        message.error(response.message || (editingRecord ? '编辑失败' : '添加失败'));
      }
    } catch (error) {
      message.error(editingRecord ? '编辑失败' : '添加失败');
    }
  };

  return (
    <PageContainer title="城市配置">
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="inline"
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="cityCode" label="城市编码">
            <Input placeholder="请输入城市编码" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="allCityName" label="完整城市名称">
            <Input placeholder="请输入完整城市名称" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="topCityName" label="顶级城市名称">
            <Input placeholder="请输入顶级城市名称" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="platePrefix" label="车牌前缀">
            <Input placeholder="请输入车牌前缀" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增城市
            </Button>
          </Col>
          <Col>
            <Button
              icon={<SyncOutlined />}
              onClick={handleSync}
              loading={loading}
            >
              同步城市数据
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey="id"
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              const newSearchParams = {
                ...searchParams,
                pageNo: page,
                pageSize: pageSize || 10,
              };
              setSearchParams(newSearchParams);
              fetchCityConfigs(newSearchParams);
            },
          }}
        />

        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <span>{editingRecord ? '编辑城市配置' : '新增城市配置'}</span>
              {!editingRecord && (
                <Tooltip
                  title="点击查看填写示例"
                  placement="top"
                >
                  <Button
                    type="link"
                    icon={<InfoCircleOutlined />}
                    size="small"
                    onClick={fillExampleData}
                    style={{ padding: 0, height: 'auto' }}
                  >
                    示例
                  </Button>
                </Tooltip>
              )}
            </div>
          }
          open={modalVisible}
          onOk={handleSave}
          onCancel={() => setModalVisible(false)}
          width={600}
        >

          {!editingRecord && (
            <div style={{
              marginBottom: 16,
              padding: 12,
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: 6,
              fontSize: 12,
              color: '#52c41a'
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>💡 填写说明：</div>
              <div>• 城市编码：行政区划代码，如 224200</div>
              <div>• 地区表城市名称：保存到地区表的名称，如 东台市</div>
              <div>• 车牌前缀：该地区的车牌前缀，如 苏J</div>
              <div>• 完整城市名称：包含省份的完整名称，如 江苏省东台市</div>
              <div>• 顶级城市名称：所属省份名称，如 江苏省</div>
              <div>• 上级城市：选择所属的上级行政区域</div>
            </div>
          )}

          <Form
            form={form}
            layout="vertical"
            initialValues={{}}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="城市编码"
                  name="cityCode"
                  rules={[{ required: true, message: '请输入城市编码' }]}
                >
                  <Input placeholder="请输入行政区划代码，如：224200" disabled={!!editingRecord} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="地区表城市名称"
                  name="regionCityName"
                  rules={[{ required: true, message: '请输入地区表城市名称' }]}
                >
                  <Input placeholder="保存到地区表的名称，如：东台市" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="车牌前缀"
                  name="platePrefix"
                  rules={[{ required: true, message: '请输入车牌前缀' }]}
                >
                  <Input placeholder="该地区的车牌前缀，如：苏J" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="完整城市名称"
                  name="allCityName"
                  rules={[{ required: true, message: '请输入完整城市名称' }]}
                >
                  <Input placeholder="包含省份的完整名称，如：江苏省东台市" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="顶级城市名称"
                  name="topCityName"
                  rules={[{ required: true, message: '请输入顶级城市名称' }]}
                >
                  <Input placeholder="所属省份名称，如：江苏省" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="上级城市"
                  name="parentCityCode"
                  rules={[{ required: !editingRecord, message: '请选择上级城市' }]}
                >
                  <Select
                    placeholder="请选择上级城市"
                    showSearch
                    allowClear
                    disabled={!!editingRecord} // 编辑时禁用上级城市选择
                    filterOption={(input, option) => {
                      const cityName = option?.children?.toString() || '';
                      const cityCode = option?.value?.toString() || '';
                      return cityName.toLowerCase().includes(input.toLowerCase()) ||
                             cityCode.toLowerCase().includes(input.toLowerCase());
                    }}
                  >
                    {parentCities.map((city) => (
                      <Option key={city.code} value={city.code}>
                        {city.name} ({city.code})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              {editingRecord && (
                <Col span={12}>
                  <Form.Item label="当前上级城市">
                    <Input
                      value={editingRecord.parentCityName || '无'}
                      disabled
                      placeholder="当前上级城市"
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Form>
        </Modal>
      </Card>
    </PageContainer>
  );
};

export default CityConfig;
