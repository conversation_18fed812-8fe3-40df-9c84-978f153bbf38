import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import {
  Card,
  Table,
  Button,
  Statistic,
  Row,
  Col,
  Progress,
  Tag,
  message,
  Divider,
  Space,
  Typography,
} from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { getClickPreBatchProgress, ConfigInfo, ProgressData } from '@/services/clickPreProgress';

const { Title, Text } = Typography;

const ClickPreProgress: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // 获取进度数据
  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const response = await getClickPreBatchProgress();

      if (response && response.success) {
        setProgressData(response.result);
      } else {
        message.error(response?.message || '获取进度数据失败');
      }
    } catch (error) {
      console.error('获取进度数据失败:', error);
      message.error('获取进度数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换自动刷新
  const toggleAutoRefresh = () => {
    if (autoRefresh) {
      // 停止自动刷新
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
      setAutoRefresh(false);
      message.info('已停止自动刷新');
    } else {
      // 开始自动刷新
      const interval = setInterval(() => {
        fetchProgressData();
      }, 5000); // 每5秒刷新一次
      setRefreshInterval(interval);
      setAutoRefresh(true);
      message.info('已开启自动刷新（每5秒）');
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchProgressData();

    // 组件卸载时清理定时器
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case '待开始':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      case '处理中':
        return <PlayCircleOutlined style={{ color: '#faad14' }} />;
      case '已完成':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    switch (status) {
      case '待开始':
        return <Tag color="blue">{status}</Tag>;
      case '处理中':
        return <Tag color="orange">{status}</Tag>;
      case '已完成':
        return <Tag color="green">{status}</Tag>;
      default:
        return <Tag color="red">{status}</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '租户ID',
      dataIndex: 'tenantId',
      key: 'tenantId',
      width: 100,
    },
    {
      title: '租户名称',
      dataIndex: 'tenantName',
      key: 'tenantName',
      width: 150,
    },
    {
      title: '平均点击数',
      dataIndex: 'avgClicks',
      key: 'avgClicks',
      width: 120,
      render: (value: number) => value?.toLocaleString() || 0,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 180,
      render: (value: string) => value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      render: (value: string) => value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '处理时长',
      key: 'duration',
      width: 120,
      render: (record: ConfigInfo) => {
        if (record.startTime && record.endTime) {
          const duration = moment(record.endTime).diff(moment(record.startTime), 'seconds');
          return `${duration}秒`;
        }
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          {getStatusTag(status)}
        </Space>
      ),
    },
  ];

  // 计算进度百分比
  const getProgressPercent = () => {
    if (!progressData || progressData.totalTasks === 0) return 0;
    return Math.round((progressData.completedTasks / progressData.totalTasks) * 100);
  };

  return (
    <PageContainer
      title="点击数预生成进度监控"
      extra={[
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={fetchProgressData}
        >
          手动刷新
        </Button>,
        <Button
          key="autoRefresh"
          type={autoRefresh ? 'default' : 'primary'}
          onClick={toggleAutoRefresh}
        >
          {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
        </Button>,
      ]}
    >
      {progressData && (
        <>
          {/* 总体进度卡片 */}
          <Card title="总体进度" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="总任务数"
                  value={progressData.totalTasks}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="已完成"
                  value={progressData.completedTasks}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="剩余任务"
                  value={progressData.remainingTasks}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>处理状态: </Text>
                  {getStatusTag(progressData.status)}
                </div>
                <Progress
                  percent={getProgressPercent()}
                  status={progressData.status === '处理失败' ? 'exception' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </Col>
            </Row>
          </Card>

          {/* 详细配置列表 */}
          <Card title="配置处理详情">
            <Table
              columns={columns}
              dataSource={progressData.configList}
              rowKey={(record) => `${record.tenantId}-${record.status}`}
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </>
      )}
    </PageContainer>
  );
};

export default ClickPreProgress;
