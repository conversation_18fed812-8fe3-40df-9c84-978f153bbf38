import React from 'react';
import { Collapse, Row, Col } from 'antd';
import MetricInput from './MetricInput';
import type { MetricGroup as MetricGroupType } from '../data';

const { Panel } = Collapse;

interface MetricGroupProps {
  group: MetricGroupType;
  disabled?: boolean;
}

/**
 * 指标分组组件
 * 使用折叠面板展示一组相关的指标，采用两列布局使界面更紧凑
 */
const MetricGroup: React.FC<MetricGroupProps> = ({ group, disabled }) => {
  const { title, key, metrics } = group;

  return (
    <Collapse
      defaultActiveKey={[key]}
      style={{ marginBottom: 12 }}
      bordered={false}
      className="metric-group-collapse"
    >
      <Panel header={<b>{title}</b>} key={key}>
        <Row gutter={[16, 8]}>
          {metrics.map((metric) => (
            <Col span={12} key={metric.startKey}>
              <MetricInput
                metric={metric}
                disabled={disabled}
              />
            </Col>
          ))}
        </Row>
      </Panel>
    </Collapse>
  );
};

export default MetricGroup;
