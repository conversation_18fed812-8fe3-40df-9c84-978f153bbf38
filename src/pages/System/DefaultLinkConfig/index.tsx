import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, Form, Button, message, Spin, Alert, Tabs, Typography, Space, Tooltip } from 'antd';
import { SaveOutlined, ThunderboltOutlined, CarOutlined, BankOutlined, GiftOutlined } from '@ant-design/icons';
import { queryDefaultLinkConfigById, addDefaultLinkConfig, editDefaultLinkConfig } from '@/services/system/defaultLinkConfig';
import { generateCarInsuranceRules, normalizeRules } from './utils/randomGenerator';
import MetricGroup from './components/MetricGroup';
import type { MetricGroup as MetricGroupType } from './data';
import './index.less';

const { Text } = Typography;
const { TabPane } = Tabs;

// 链接类型配置
const linkTypes = [
  { key: '2', name: '车险', icon: <CarOutlined />, deployType: 2 },
  { key: '3', name: '财险', icon: <BankOutlined />, deployType: 3 },
  { key: '4', name: '增值服务', icon: <GiftOutlined />, deployType: 4 },
];

// 定义指标分组
const metricGroups: MetricGroupType[] = [
  {
    title: '流量指标',
    key: 'traffic',
    metrics: [
      {
        label: '独立查询数',
        startKey: 'uniqueQueryStart',
        endKey: 'uniqueQueryEnd',
        min: 0,
        max: 10000,
        step: 10,
      },
    ],
  },
  {
    title: '转化指标',
    key: 'conversion',
    metrics: [
      {
        label: '转化率',
        startKey: 'conversionRateStart',
        endKey: 'conversionRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '表单提交数',
        startKey: 'formSubmissionsStart',
        endKey: 'formSubmissionsEnd',
        min: 0,
        max: 10000,
        step: 10,
      },
      {
        label: 'CTR (点击率)',
        startKey: 'ctrStart',
        endKey: 'ctrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '首屏CTR',
        startKey: 'firstScreenCtrStart',
        endKey: 'firstScreenCtrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: 'TOP3 PV-CTR',
        startKey: 'top3PvCtrStart',
        endKey: 'top3PvCtrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
  {
    title: '用户行为指标',
    key: 'behavior',
    metrics: [
      {
        label: '跳出率',
        startKey: 'bounceRateStart',
        endKey: 'bounceRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '平均停留时长',
        startKey: 'avgStayTimeStart',
        endKey: 'avgStayTimeEnd',
        unit: '秒',
        min: 0,
        max: 3600,
        step: 1,
      },
      {
        label: '返回率',
        startKey: 'returnRateStart',
        endKey: 'returnRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容完成率',
        startKey: 'completionRateStart',
        endKey: 'completionRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容跳出率',
        startKey: 'contentJumpRateStart',
        endKey: 'contentJumpRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容返回率',
        startKey: 'contentReturnRateStart',
        endKey: 'contentReturnRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
  {
    title: '交互指标',
    key: 'interaction',
    metrics: [
      {
        label: '点击深度',
        startKey: 'clickDepthStart',
        endKey: 'clickDepthEnd',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '点击间隔时间',
        startKey: 'clickIntervalTimeStart',
        endKey: 'clickIntervalTimeEnd',
        unit: '秒',
        min: 0,
        max: 3600,
        step: 0.1,
        precision: 1,
      },
      {
        label: 'Engagement得分',
        startKey: 'engagementScoreStart',
        endKey: 'engagementScoreEnd',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '翻页率',
        startKey: 'pageStartNum',
        endKey: 'pageEndNum',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
];

const DefaultLinkConfig: React.FC = () => {
  // 创建三个表单实例，分别对应三种链接类型
  const [carForm] = Form.useForm();
  const [propertyForm] = Form.useForm();
  const [valueAddedForm] = Form.useForm();

  const [activeKey, setActiveKey] = useState('2'); // 默认选中车险
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [configIds, setConfigIds] = useState<Record<string, string>>({});

  // 获取当前表单实例
  const getCurrentForm = () => {
    switch (activeKey) {
      case '2': return carForm;
      case '3': return propertyForm;
      case '4': return valueAddedForm;
      default: return carForm;
    }
  };

  // 获取当前配置类型
  const getCurrentDeployType = () => {
    return linkTypes.find(type => type.key === activeKey)?.deployType || 2;
  };

  // 随机生成车险链接规则
  const handleGenerateCarRules = () => {
    if (activeKey !== '2') {
      message.warning('请先切换到车险标签页');
      return;
    }

    // 生成随机规则
    const randomRules = generateCarInsuranceRules();

    // 确保结束值大于起始值
    const normalizedRules = normalizeRules(randomRules);

    // 设置表单值
    carForm.setFieldsValue(normalizedRules);

    message.success('已随机生成车险链接规则');
  };

  // 获取配置数据
  const fetchConfig = async (deployType: number) => {
    setLoading(true);
    try {
      // 这里假设系统中每种类型只有一条配置记录，ID为对应的deployType
      // 实际使用时可能需要先获取列表，然后获取第一条记录的ID
      const response = await queryDefaultLinkConfigById(deployType.toString(), deployType);

      if (response && response.success && response.result) {
        const { id, deployJson } = response.result;

        // 更新配置ID
        setConfigIds(prev => ({
          ...prev,
          [deployType]: id,
        }));

        // 解析JSON字符串为对象
        if (deployJson) {
          try {
            const configData = JSON.parse(deployJson);

            // 根据deployType选择对应的表单
            const form = deployType === 2 ? carForm :
                        deployType === 3 ? propertyForm : valueAddedForm;

            form.setFieldsValue(configData);
          } catch (error) {
            console.error('解析配置JSON失败:', error);
            message.error('解析配置信息失败');
          }
        }
      }
    } catch (error) {
      console.error('获取配置信息失败:', error);
      message.error('获取配置信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载时获取所有类型的配置
  useEffect(() => {
    // 获取车险配置
    fetchConfig(2);
    // 获取财险配置
    fetchConfig(3);
    // 获取增值服务配置
    fetchConfig(4);
  }, []);

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await getCurrentForm().validateFields();
      const deployType = getCurrentDeployType();

      setSubmitting(true);

      // 将表单值转换为JSON字符串
      const deployJson = JSON.stringify(values);

      const params = {
        id: configIds[deployType],
        deployJson,
        deployType,
      };

      // 根据是否有ID决定是新增还是编辑
      const submitFunc = configIds[deployType] ? editDefaultLinkConfig : addDefaultLinkConfig;
      const response = await submitFunc(params);

      if (response && response.success) {
        message.success('默认链接配置保存成功');

        // 如果是新增，保存后获取新的ID
        if (!configIds[deployType] && response.result) {
          setConfigIds(prev => ({
            ...prev,
            [deployType]: response.result,
          }));
        }
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    return (
      <Spin spinning={loading}>
        {metricGroups.map(group => (
          <MetricGroup
            key={group.key}
            group={group}
            disabled={loading || submitting}
          />
        ))}
      </Spin>
    );
  };

  return (
    <PageContainer title="默认链接配置">
      <Card>
        <Alert
          message="默认链接配置"
          description="配置不同链接类型的默认生成规则参数，包括流量指标、转化指标、用户行为指标和交互指标等。每个指标包含起始值和结束值，用于设置指标的有效范围。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Tabs
            activeKey={activeKey}
            onChange={setActiveKey}
            tabPosition="top"
            type="card"
            style={{ marginBottom: 0, flex: 1 }}
            styles={{
              nav: {
                marginBottom: 12,
              },
              tab: {
                padding: '8px 16px',
              },
            }}
          >
            {linkTypes.map(type => (
              <TabPane
                tab={
                  <span>
                    {type.icon}
                    <span style={{ marginLeft: 8 }}>{type.name}</span>
                  </span>
                }
                key={type.key}
              >
                <Form
                  form={type.key === '2' ? carForm : type.key === '3' ? propertyForm : valueAddedForm}
                  layout="vertical"
                  initialValues={{}}
                >
                  {renderFormContent()}
                </Form>
              </TabPane>
            ))}
          </Tabs>
        </div>

        {activeKey === '2' && (
          <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
            <Tooltip title="随机生成车险链接规则">
              <Button
                type="primary"
                icon={<ThunderboltOutlined />}
                onClick={handleGenerateCarRules}
                disabled={loading || submitting}
              >
                随机生成车险规则
              </Button>
            </Tooltip>
          </div>
        )}

        <div style={{ marginTop: 24, display: 'flex', justifyContent: 'center' }}>
          <Space>
            <Button
              onClick={() => getCurrentForm().resetFields()}
              disabled={loading || submitting}
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={submitting}
              disabled={loading}
            >
              保存配置
            </Button>
          </Space>
        </div>
      </Card>
    </PageContainer>
  );
};

export default DefaultLinkConfig;
