import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Row, Col, Typography, Divider, Space, Alert, Button, message } from 'antd';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import { Form } from 'antd';
import JSONPretty from 'react-json-pretty';
import 'react-json-pretty/themes/monikai.css';
import { CopyOutlined, SwapOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const FormatConverter: React.FC = () => {
  const [form] = Form.useForm();
  const [formattedData, setFormattedData] = useState<string>('');
  const [jsonObject, setJsonObject] = useState<any[]>([]);

  // 监听表单值变化
  const handleValuesChange = (changedValues: any) => {
    if (changedValues.cityList) {
      const cityList = changedValues.cityList;
      console.log('选择的城市列表:', cityList);

      // 转换城市数据格式
      const formattedCities = cityList.map((item: string) => {
        // 处理可能的异常情况
        if (!item || typeof item !== 'string') {
          console.log('无效的城市项:', item);
          return { city: String(item), type: 1 };
        }

        // 分割城市名和类型
        const parts = item.split(':');
        const city = parts[0];
        let type = 1; // 默认类型为1

        // 如果有类型部分，尝试解析
        if (parts.length > 1 && parts[1]) {
          const parsedType = parseInt(parts[1], 10);
          if (!isNaN(parsedType)) {
            type = parsedType;
          }
        }

        // 判断是否是省份，如果是，则确保type为1
        const isProvince = isProvinceCity(city);
        if (isProvince) {
          type = 1;
        }

        console.log(`处理城市: ${city}, 类型: ${type}, 是否是省份: ${isProvince}`);
        return { city, type };
      });

      // 更新格式化后的数据和JSON对象
      setJsonObject(formattedCities);
      setFormattedData(JSON.stringify(formattedCities, null, 2));
    }
  };

  // 判断是否是省份
  const isProvinceCity = (cityName: string): boolean => {
    // 直辖市列表
    const directCities = ['北京市', '天津市', '上海市', '重庆市'];

    // 检查是否是直辖市
    if (directCities.includes(cityName)) {
      return true;
    }

    // 检查是否是省份（通过名称结尾是否包含"省"或"自治区"等）
    return cityName.endsWith('省') ||
           cityName.endsWith('自治区') ||
           cityName.endsWith('特别行政区');
  };

  // 复制JSON数据到剪贴板
  const handleCopyJson = () => {
    if (formattedData) {
      navigator.clipboard.writeText(formattedData)
        .then(() => {
          message.success('JSON数据已复制到剪贴板');
        })
        .catch(() => {
          message.error('复制失败，请手动复制');
        });
    } else {
      message.warning('没有可复制的数据');
    }
  };

  return (
    <PageContainer title="格式转换">
      <Card>
        <Alert
          message="格式转换工具"
          description="本工具可以帮助您将城市选择转换为标准的JSON格式，方便在系统中使用。选择城市后，右侧会自动生成格式化的JSON数据。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={24}>
          <Col span={12}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <SwapOutlined style={{ fontSize: 20, color: '#1890ff', marginRight: 8 }} />
              <Title level={4} style={{ margin: 0 }}>城市选择</Title>
            </div>
            <Paragraph>
              请在下方选择城市，右侧将显示转换后的格式化数据。
            </Paragraph>

            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
            >
              <FormCitySelector
                name="cityList"
                cityData={cityData}
                placeholder="请选择城市"
                showSelected={true}
                required={false}
              />
            </Form>
          </Col>

          <Col span={12}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <CopyOutlined style={{ fontSize: 20, color: '#1890ff', marginRight: 8 }} />
                <Title level={4} style={{ margin: 0 }}>格式化数据</Title>
              </div>

              <Button
                type="primary"
                icon={<CopyOutlined />}
                onClick={handleCopyJson}
                disabled={!formattedData}
              >
                复制JSON
              </Button>
            </div>

            <Paragraph>
              下方显示的是格式化后的JSON数据，可以直接复制使用。
            </Paragraph>

            <div style={{
              border: '1px solid #d9d9d9',
              borderRadius: '2px',
              padding: '16px',
              backgroundColor: '#f5f5f5',
              minHeight: '300px',
              maxHeight: '500px',
              overflow: 'auto'
            }}>
              {formattedData ? (
                <JSONPretty id="json-pretty" data={formattedData}></JSONPretty>
              ) : (
                <div style={{ color: '#999', padding: '10px', textAlign: 'center', marginTop: '100px' }}>
                  请在左侧选择城市，这里将显示格式化后的数据。
                </div>
              )}
            </div>

            {formattedData && (
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">
                  示例格式: [{'\u007B'}"city": "上海市", "type": 1{'\u007D'}, {'\u007B'}"city": "北京市", "type": 1{'\u007D'}, {'\u007B'}"city": "石家庄市", "type": 2{'\u007D'}, {'\u007B'}"city": "山西省", "type": 1{'\u007D'}]
                </Text>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">
                    已选择 <Text strong>{jsonObject.length}</Text> 个城市
                  </Text>
                </div>
              </div>
            )}
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default FormatConverter;
