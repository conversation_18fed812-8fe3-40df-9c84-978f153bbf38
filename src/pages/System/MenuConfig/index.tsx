import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Row, Col, Tree, Button, message, Spin, Modal } from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { request } from '@/utils/request';
import type { CheckInfo } from 'rc-tree/lib/interface';

// 菜单配置页面
const MenuConfig: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [menuTree, setMenuTree] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  // 获取可见菜单配置
  const fetchVisibleMenus = async () => {
    try {
      setLoading(true);
      const response = await request('/sys/menu/getVisibleMenus', {
        method: 'GET',
      });

      if (response && response.success) {
        setCheckedKeys(response.result || []);
        setExpandedKeys(response.result || []);
      } else {
        message.error(response?.message || '获取菜单配置失败');
      }
    } catch (error) {
      console.error('获取菜单配置失败:', error);
      message.error('获取菜单配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存菜单可见性配置
  const saveMenuVisibility = async () => {
    try {
      setLoading(true);

      // 打印当前选中的菜单键，便于调试
      console.log('保存的菜单键:', checkedKeys);

      const response = await request('/sys/menu/saveMenuVisibility', {
        method: 'POST',
        data: {
          menuKeys: checkedKeys,
        },
      });

      if (response && response.success) {
        message.success('保存成功');

        // 提示用户刷新页面
        Modal.success({
          title: '保存成功',
          content: '菜单配置已保存，请重新登录以应用新的菜单配置',
          okText: '确定',
        });

        // 刷新菜单树和可见菜单配置
        fetchMenuTreeAndVisibleMenus();
      } else {
        message.error(response?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取菜单树和可见菜单配置
  const fetchMenuTreeAndVisibleMenus = async () => {
    try {
      setLoading(true);
      const response = await request('/sys/menu/getMenuTreeAndVisibleMenus', {
        method: 'GET',
      });

      if (response && response.success) {
        // 设置菜单树
        setMenuTree(response.result?.menuTree || []);
        // 设置选中的菜单键
        setCheckedKeys(response.result?.visibleMenuKeys || []);
        // 设置展开的菜单键
        setExpandedKeys(response.result?.visibleMenuKeys || []);
      } else {
        message.error(response?.message || '获取菜单配置失败');
      }
    } catch (error) {
      console.error('获取菜单配置失败:', error);
      message.error('获取菜单配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchMenuTreeAndVisibleMenus();
  };

  // 递归查找节点的所有子节点key
  const findAllChildrenKeys = (treeData: any[], key: string): string[] => {
    const childrenKeys: string[] = [];

    const traverse = (nodes: any[], targetKey: string) => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          // 找到目标节点，收集其所有子节点的key
          const collectChildrenKeys = (children: any[]) => {
            if (!children || !children.length) return;

            for (const child of children) {
              childrenKeys.push(child.key);
              if (child.children && child.children.length > 0) {
                collectChildrenKeys(child.children);
              }
            }
          };

          if (node.children && node.children.length > 0) {
            collectChildrenKeys(node.children);
          }
          return true;
        }

        if (node.children && node.children.length > 0) {
          if (traverse(node.children, targetKey)) {
            return true;
          }
        }
      }
      return false;
    };

    traverse(treeData, key);
    return childrenKeys;
  };

  // 递归查找节点的所有父节点key
  const findParentKey = (treeData: any[], key: string): string | null => {
    const findParent = (nodes: any[], targetKey: string, parent: string | null = null): string | null => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return parent;
        }

        if (node.children && node.children.length > 0) {
          const found = findParent(node.children, targetKey, node.key);
          if (found !== null) {
            return found;
          }
        }
      }
      return null;
    };

    return findParent(treeData, key);
  };

  // 检查是否所有子节点都被选中
  const areAllChildrenChecked = (treeData: any[], parentKey: string, checkedKeysSet: Set<string>): boolean => {
    const childrenKeys = findAllChildrenKeys(treeData, parentKey);
    if (childrenKeys.length === 0) return false;

    return childrenKeys.every(key => checkedKeysSet.has(key));
  };

  // 处理菜单树选择变化
  const handleCheck = (checked: any, info: CheckInfo) => {
    console.log('选中的菜单键:', checked);
    console.log('勾选信息:', info);

    // 获取当前操作的节点key
    const targetKey = info.node.key as string;
    // 判断是选中还是取消选中
    const isChecked = info.checked;

    // 创建一个新的选中keys集合
    const newCheckedKeys = new Set(checked);

    if (!isChecked) {
      // 如果是取消选中，则同时取消其所有子节点的选中状态
      const childrenKeys = findAllChildrenKeys(menuTree, targetKey);
      childrenKeys.forEach(key => {
        newCheckedKeys.delete(key);
      });
    } else {
      // 如果是选中，检查其父节点的所有子节点是否都被选中
      const parentKey = findParentKey(menuTree, targetKey);
      if (parentKey && areAllChildrenChecked(menuTree, parentKey, newCheckedKeys)) {
        // 如果父节点的所有子节点都被选中，则也选中父节点
        newCheckedKeys.add(parentKey);
      }
    }

    // 更新选中状态
    setCheckedKeys(Array.from(newCheckedKeys));
  };

  // 处理展开/收起节点
  const handleExpand = (expanded: string[]) => {
    setExpandedKeys(expanded);
  };

  // 初始化加载
  useEffect(() => {
    fetchMenuTreeAndVisibleMenus();
  }, []);

  return (
    <PageContainer title="菜单权限配置">
      <Card>
        <Spin spinning={loading}>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              >
                刷新
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={saveMenuVisibility}
              >
                保存配置
              </Button>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Card
                title="菜单配置"
                size="small"
                style={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}
              >
                {menuTree.length > 0 ? (
                  <Tree
                    checkable
                    checkedKeys={checkedKeys}
                    expandedKeys={expandedKeys}
                    onCheck={handleCheck}
                    onExpand={handleExpand}
                    treeData={menuTree}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    暂无菜单数据
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </Spin>
      </Card>
    </PageContainer>
  );
};

export default MenuConfig;
