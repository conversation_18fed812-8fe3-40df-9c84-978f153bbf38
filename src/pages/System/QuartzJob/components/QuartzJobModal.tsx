import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Radio, message } from 'antd';
import { QuartzJobItem, QUARTZ_STATUS } from '../data';
import { addQuartzJob, editQuartzJob, getQuartzJobById } from '@/services/system/quartzJob';
import CronEditor from '@/components/CronEditor';

const { TextArea } = Input;
const { Option } = Select;

interface QuartzJobModalProps {
  visible: boolean;
  title: string;
  record?: QuartzJobItem;
  onCancel: () => void;
  onSuccess: () => void;
}

const QuartzJobModal: React.FC<QuartzJobModalProps> = ({
  visible,
  title,
  record,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [parameterType, setParameterType] = useState<'string' | 'json'>('string');

  // 表单初始化
  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (record) {
        // 编辑模式，获取详细信息
        const fetchDetail = async () => {
          try {
            const response = await getQuartzJobById(record.id);
            if (response.success) {
              const result = response.result;
              // 判断参数类型
              let paramType: 'string' | 'json' = 'string';
              try {
                const param = result.parameter;
                if (param) {
                  JSON.parse(param);
                  paramType = 'json';
                }
              } catch (e) {
                paramType = 'string';
              }

              setParameterType(paramType);
              form.setFieldsValue({
                ...result,
                paramterType: paramType,
              });
            } else {
              message.error(response.message || '获取任务详情失败');
            }
          } catch (error) {
            message.error('获取任务详情失败');
          }
        };

        fetchDetail();
      } else {
        // 新增模式，设置默认值
        form.setFieldsValue({
          status: QUARTZ_STATUS.NORMAL,
          cronExpression: '* * * * * ?',
          paramterType: 'string',
        });
        setParameterType('string');
      }
    }
  }, [visible, record, form]);

  // 处理参数类型变化
  const handleParameterTypeChange = (value: 'string' | 'json') => {
    setParameterType(value);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 处理参数
      if (values.paramterType === 'json' && values.parameter) {
        try {
          // 如果是JSON类型，尝试解析并格式化
          const jsonObj = JSON.parse(values.parameter);
          values.parameter = JSON.stringify(jsonObj);
        } catch (e) {
          message.error('参数JSON格式不正确');
          setLoading(false);
          return;
        }
      }

      if (record) {
        // 编辑模式
        const response = await editQuartzJob({
          ...values,
          id: record.id,
        });
        if (response.success) {
          message.success('编辑成功');
          onSuccess();
        } else {
          message.error(response.message || '编辑失败');
        }
      } else {
        // 新增模式
        const response = await addQuartzJob(values);
        if (response.success) {
          message.success('新增成功');
          onSuccess();
        } else {
          message.error(response.message || '新增失败');
        }
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: QUARTZ_STATUS.NORMAL,
          paramterType: 'string',
        }}
      >
        <Form.Item
          name="jobClassName"
          label="任务类名"
          rules={[{ required: true, message: '请输入任务类名' }]}
        >
          <Input placeholder="请输入任务类名" />
        </Form.Item>

        <Form.Item
          name="cronExpression"
          label="Cron表达式"
          rules={[{ required: true, message: '请输入Cron表达式' }]}
        >
          <CronEditor placeholder="请输入Cron表达式" />
        </Form.Item>

        <Form.Item name="paramterType" label="参数类型">
          <Select onChange={handleParameterTypeChange}>
            <Option value="string">字符串</Option>
            <Option value="json">JSON对象</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="parameter"
          label="参数"
        >
          {parameterType === 'json' ? (
            <TextArea
              rows={4}
              placeholder="请输入JSON格式参数，例如：{'{'}'key':'value'{'}'}"
            />
          ) : (
            <TextArea
              rows={4}
              placeholder="请输入参数"
            />
          )}
        </Form.Item>

        <Form.Item
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
        >
          <Radio.Group>
            <Radio value={QUARTZ_STATUS.NORMAL}>正常</Radio>
            <Radio value={QUARTZ_STATUS.PAUSE}>暂停</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item name="description" label="描述">
          <TextArea rows={3} placeholder="请输入描述" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuartzJobModal;
