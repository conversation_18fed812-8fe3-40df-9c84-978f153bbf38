import type { ProColumns } from '@ant-design/pro-components';

// 定时任务状态
export const QUARTZ_STATUS = {
  NORMAL: 0,  // 正常
  PAUSE: -1,  // 暂停
};

// 定时任务数据模型
export interface QuartzJobItem {
  id: string;
  jobClassName: string;
  cronExpression: string;
  parameter?: string;
  description?: string;
  status: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  paramterType?: 'string' | 'json';
}

// 定时任务表格列配置
export const quartzColumns: ProColumns<QuartzJobItem>[] = [
  {
    title: '任务类名',
    dataIndex: 'jobClassName',
    width: 250,
    ellipsis: true,
  },
  {
    title: 'Cron表达式',
    dataIndex: 'cronExpression',
    width: 150,
  },
  {
    title: '参数',
    dataIndex: 'parameter',
    width: 150,
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    valueEnum: {
      [QUARTZ_STATUS.NORMAL]: { text: '正常', status: 'Success' },
      [QUARTZ_STATUS.PAUSE]: { text: '暂停', status: 'Error' },
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    search: false,
  },
  {
    title: '操作',
    valueType: 'option',
    width: 180,
    fixed: 'right',
  },
];

// 定时任务搜索表单配置
export const searchFormConfig = {
  labelWidth: 100,
  columns: [
    {
      title: '任务类名',
      dataIndex: 'jobClassName',
      valueType: 'input',
      formItemProps: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        [QUARTZ_STATUS.NORMAL]: { text: '正常' },
        [QUARTZ_STATUS.PAUSE]: { text: '暂停' },
      },
      formItemProps: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
    },
  ],
};
