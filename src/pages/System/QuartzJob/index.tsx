import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Tag } from 'antd';
import { PlusOutlined, ExportOutlined, ImportOutlined, DeleteOutlined } from '@ant-design/icons';
import { quartzColumns, QuartzJobItem, QUARTZ_STATUS } from './data';
import {
  getQuartzJobList,
  deleteQuartzJob,
  batchDeleteQuartzJob,
  pauseQuartzJob,
  resumeQuartzJob,
  executeQuartzJob,
  getExportUrl,
  getImportUrl,
} from '@/services/system/quartzJob';
import QuartzJobModal from './components/QuartzJobModal';

const QuartzJobList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<QuartzJobItem | undefined>(undefined);
  const [modalTitle, setModalTitle] = useState<string>('新增任务');

  // 刷新表格
  const refreshTable = () => {
    if (actionRef.current) {
      actionRef.current.reload();
    }
  };

  // 打开新增弹窗
  const handleAdd = () => {
    setCurrentRecord(undefined);
    setModalTitle('新增任务');
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (record: QuartzJobItem) => {
    setCurrentRecord(record);
    setModalTitle('编辑任务');
    setModalVisible(true);
  };

  // 删除任务
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteQuartzJob(id);
      if (response.success) {
        message.success('删除成功');
        refreshTable();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 批量删除任务
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await batchDeleteQuartzJob(selectedRowKeys);
      if (response.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        refreshTable();
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  // 暂停任务
  const handlePause = async (id: string) => {
    try {
      const response = await pauseQuartzJob(id);
      if (response.success) {
        message.success('暂停成功');
        refreshTable();
      } else {
        message.error(response.message || '暂停失败');
      }
    } catch (error) {
      message.error('暂停失败');
    }
  };

  // 恢复任务
  const handleResume = async (id: string) => {
    try {
      const response = await resumeQuartzJob(id);
      if (response.success) {
        message.success('恢复成功');
        refreshTable();
      } else {
        message.error(response.message || '恢复失败');
      }
    } catch (error) {
      message.error('恢复失败');
    }
  };

  // 立即执行任务
  const handleExecute = async (id: string) => {
    try {
      const response = await executeQuartzJob(id);
      if (response.success) {
        message.success('执行成功');
        refreshTable();
      } else {
        message.error(response.message || '执行失败');
      }
    } catch (error) {
      message.error('执行失败');
    }
  };

  // 导出任务
  const handleExport = () => {
    const url = getExportUrl();
    window.open(url);
  };

  // 导入任务
  const handleImport = () => {
    const url = getImportUrl();
    message.info('导入功能待实现');
  };

  // 表格列配置
  const columns: ProColumns<QuartzJobItem>[] = [
    ...quartzColumns,
    {
      title: '操作',
      valueType: 'option',
      width: 180,
      fixed: 'right',
      render: (_, record) => [
        // 根据状态显示不同的操作按钮
        record.status === QUARTZ_STATUS.NORMAL ? (
          <Popconfirm
            key="pause"
            title="确定要暂停该任务吗?"
            onConfirm={() => handlePause(record.id)}
          >
            <a>暂停</a>
          </Popconfirm>
        ) : (
          <Popconfirm
            key="resume"
            title="确定要恢复该任务吗?"
            onConfirm={() => handleResume(record.id)}
          >
            <a>恢复</a>
          </Popconfirm>
        ),
        <Popconfirm
          key="execute"
          title="确定要立即执行该任务吗?"
          onConfirm={() => handleExecute(record.id)}
        >
          <a>执行</a>
        </Popconfirm>,
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <Popconfirm
          key="delete"
          title="确定要删除该任务吗?"
          onConfirm={() => handleDelete(record.id)}
        >
          <a>删除</a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<QuartzJobItem>
        headerTitle="定时任务列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button key="add" type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增
          </Button>,
          <Button key="export" icon={<ExportOutlined />} onClick={handleExport}>
            导出
          </Button>,
          <Button key="import" icon={<ImportOutlined />} onClick={handleImport}>
            导入
          </Button>,
          selectedRowKeys.length > 0 && (
            <Popconfirm
              key="batchDelete"
              title="确定要删除选中的记录吗?"
              onConfirm={handleBatchDelete}
            >
              <Button danger icon={<DeleteOutlined />}>
                批量删除
              </Button>
            </Popconfirm>
          ),
        ]}
        request={async (params) => {
          const { current, pageSize, ...restParams } = params;
          const response = await getQuartzJobList({
            pageNo: current,
            pageSize,
            ...restParams,
          });

          // 处理后端返回的数据结构
          const result = response.result || {};

          return {
            data: result.records || [],
            success: true,
            total: result.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as string[]),
        }}
        pagination={{
          pageSize: 10,
        }}
      />

      <QuartzJobModal
        visible={modalVisible}
        title={modalTitle}
        record={currentRecord}
        onCancel={() => setModalVisible(false)}
        onSuccess={() => {
          setModalVisible(false);
          refreshTable();
        }}
      />
    </PageContainer>
  );
};

export default QuartzJobList;
