# 地图配置功能说明

## 概述
地图配置功能用于管理总部地区与业务地区的对应关系，为地图展示和业务分配提供数据支持。配置基于一级节点（省份/直辖市），不涉及二级城市。

## 功能特性

### 1. 总部地区管理
- **唯一总部**: 系统只支持配置一个总部地区
- **省份级别**: 从一级节点（省份/直辖市）中选择总部地区
- **灵活切换**: 支持更换总部地区

### 2. 业务地区配置
- **多地区支持**: 为总部配置多个业务地区
- **一级节点**: 所有配置都基于省份/直辖市级别
- **批量操作**: 使用Transfer组件支持批量选择和移除
- **智能过滤**: 总部地区不会出现在业务地区选择列表中

### 3. 数据管理
- **结构化存储**: 后端返回包含地区名称的结构化数据
- **原始格式保存**: 保存时使用城市编码的简洁格式
- **实时同步**: 前端操作实时更新两种数据格式

## 数据结构

### 原始存储格式（用于保存）
```json
{
  "headquarterCode": "110000",
  "businessRegions": ["310000", "440000", "320000"]
}
```

### 结构化VO格式（用于展示）
```json
{
  "headquarterCode": "110000",
  "headquarterName": "北京市",
  "businessRegions": [
    {
      "regionCode": "310000",
      "regionName": "上海市"
    },
    {
      "regionCode": "440000",
      "regionName": "广东省"
    },
    {
      "regionCode": "320000",
      "regionName": "江苏省"
    }
  ]
}
```

## 后端实现

### 1. 新增DTO类
- **RegionConfigDTO**: 地区配置主DTO
- **HeadquarterRegionItem**: 总部地区配置项
- **BusinessCityItem**: 业务城市配置项

### 2. 服务层功能
- **getRegionConfig()**: 获取结构化地区配置
- **自动查询地区信息**: 根据编码查询地区名称
- **数据转换**: 原始JSON转换为结构化VO

### 3. 控制器接口
- **GET /wechat/sysDeployConfig/getRegionConfig**: 获取地区配置
- **GET /wechat/eaRegion/getProvinces**: 获取省份列表

## 前端实现

### 1. 用户界面
- **总部选择器**: 下拉选择未配置的省份
- **配置卡片**: 每个总部一个配置卡片
- **Transfer组件**: 城市选择和配置
- **操作按钮**: 添加、删除、保存功能

### 2. 交互设计
- **智能过滤**: 已配置的省份自动从选择列表中移除
- **实时统计**: 显示每个总部已配置的业务城市数量
- **搜索功能**: 支持按城市名称搜索
- **确认操作**: 删除总部配置需要确认

### 3. 数据处理
- **双格式维护**: 同时维护原始格式和VO格式
- **实时同步**: 操作时同步更新两种格式
- **错误处理**: 完善的异常处理和用户提示

## 使用场景

### 1. 地图展示
- 在地图上标注总部位置
- 显示总部覆盖的业务城市范围
- 提供地理位置的可视化展示

### 2. 业务分配
- 根据客户所在城市自动分配到对应总部
- 为业务人员提供地区归属信息
- 支持区域化的业务管理

### 3. 数据分析
- 按地区统计业务数据
- 分析不同总部的业务覆盖情况
- 为业务决策提供地理维度的数据支持

## 操作指南

### 1. 添加总部配置
1. 在下拉框中选择总部地区（省份）
2. 系统自动创建该总部的配置卡片
3. 在Transfer组件中选择业务城市
4. 点击保存按钮保存配置

### 2. 修改业务城市
1. 在对应总部的Transfer组件中操作
2. 从左侧选择城市移动到右侧
3. 或从右侧移除不需要的城市
4. 点击保存按钮保存修改

### 3. 删除总部配置
1. 点击总部卡片右上角的删除按钮
2. 确认删除操作
3. 系统自动移除该总部的所有配置

## 注意事项
1. 配置保存后立即生效，请谨慎操作
2. 删除总部配置会同时删除其下所有业务城市配置
3. 建议定期检查和更新配置，确保业务覆盖的准确性
4. 配置变更可能影响现有的业务分配逻辑
