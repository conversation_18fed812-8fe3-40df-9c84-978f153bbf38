# 系统配置页面功能说明

## 概述
系统配置页面已经升级为支持多个配置类型的页签式界面，包含基础配置和默认保司人员配置两个主要功能模块。

## 功能特性

### 1. 基础配置 (deployType = 1)
- **姓名脱敏**: 支持将姓名替换为先生/女士称呼
- **车牌号脱敏**: 对车牌号进行脱敏处理
- **车架号脱敏**: 对车架号进行脱敏处理  
- **手机号脱敏**: 对手机号进行脱敏处理
- **数据实现方式**: 支持数据库存储和查询实现脱敏两种方式
- **客服IP配置**: 支持服务器IP和租户IP两种来源
- **聊天源配置**: 配置车险、财险、增值服务的聊天源数量
- **资讯配置**: 配置资讯点赞数区间和最低点击率

### 2. 默认保司人员配置 (deployType = 5)
- **保司列表展示**: 显示保司图标和公司名称
- **用户选择**: 为每个保司配置对应的默认人员
- **用户信息展示**: 显示用户账号、用户姓名、微信二维码
- **关系管理**: 支持添加和移除保司与用户的关系
- **数据存储**: 配置关系存储在deployJson字段中

## 技术实现

### 数据结构
```typescript
// 保司数据类型
interface InsuranceCompany {
  id: string;
  name: string;
  iconImg?: string;
}

// 用户数据类型
interface User {
  id: string;
  username: string;
  realname: string;
  wechatQrcode?: string;
}

// 保司人员配置数据类型
interface CompanyUserConfig {
  [companyId: string]: string[]; // 保司ID对应用户ID数组
}
```

### API接口
- **基础配置**: `wechat/sysDeployConfig/` (deployType=1)
- **保司人员配置**: `wechat/sysDeployConfig/` (deployType=5)
- **保司列表**: `corp/pdCompany/list`
- **用户列表**: `sys/user/listAll`

### 组件特性
- 使用Ant Design的Tabs组件实现页签切换
- 使用Transfer组件实现用户选择和配置
- 支持搜索功能，可按用户姓名或账号搜索
- 实时显示已配置用户数量
- 支持独立保存不同类型的配置

## 使用说明

1. **访问页面**: 进入系统管理 -> 系统配置
2. **基础配置**: 在"基础配置"页签中设置系统基本功能
3. **保司人员配置**: 在"默认保司人员配置"页签中为每个保司配置默认人员
4. **保存配置**: 每个页签都有独立的保存按钮，配置保存后立即生效

## 注意事项
- 配置保存后将立即生效，请谨慎操作
- 保司人员配置用于系统自动分配，请确保配置的用户具有相应权限
- 建议定期检查和更新配置，确保系统正常运行
