# 服务商列表优先级顺序配置功能

## 功能概述

在小程序设置中新增了服务商列表优先级顺序配置功能，允许管理员通过拖拽的方式设置不同类型服务商在列表中的显示优先级。

## 功能特性

### 1. 可拖拽的优先级配置
- 支持拖拽排序，直观易用
- 实时显示当前排序状态
- 自动更新排序编号

### 2. 四种服务商类型
- **所有租户**：显示系统中所有的租户服务商
- **地区后勤**：根据用户所在地区显示对应的后勤服务商
- **保司管理后勤**：显示保险公司指定的后勤服务商
- **默认后勤**：显示系统默认的后勤服务商

### 3. 默认优先级顺序
1. 所有租户（优先级最高）
2. 地区后勤
3. 保司管理后勤
4. 默认后勤（优先级最低）

## 技术实现

### 前端实现

#### 1. 数据类型定义
```typescript
// 服务商优先级类型
export interface ServiceProviderPriority {
  key: string;
  label: string;
  order: number;
}

// 小程序配置数据类型
export interface MiniProgramConfig {
  companyMultiSelect?: number;
  propertyInsuranceDisplay?: number;
  valueAddedServiceDisplay?: number;
  serviceProviderPriority?: ServiceProviderPriority[];
}
```

#### 2. 拖拽组件
- 使用 `react-dnd` 库实现拖拽功能
- 支持拖拽排序和实时预览
- 自动更新排序编号

#### 3. UI组件
- 卡片式布局展示配置项
- 清晰的说明文档
- 保存按钮和状态提示

### 后端实现

#### 1. DTO类扩展
```java
// MiniProgramConfigDTO.java
public class MiniProgramConfigDTO {
    private List<ServiceProviderPriority> serviceProviderPriority;
    
    @Data
    public static class ServiceProviderPriority {
        private String key;
        private String label;
        private Integer order;
    }
}
```

#### 2. 配置管理
- 支持配置的保存和读取
- 提供默认配置
- Redis缓存优化

#### 3. 租户列表排序
- 修改 `getTenantDetailsList` 接口
- 根据配置的优先级对租户列表进行排序
- 保持向后兼容性

## 使用方法

### 1. 配置优先级
1. 进入系统配置页面
2. 切换到"小程序设置"标签页
3. 找到"服务商列表优先级顺序"配置项
4. 拖拽项目调整顺序
5. 点击"保存小程序配置"按钮

### 2. 查看效果
- 配置保存后立即生效
- 影响小程序中的服务商列表显示顺序
- 按照配置的优先级从高到低排序

## API接口

### 1. 获取小程序配置
```
GET /wechat/sysDeployConfig/getMiniProgramConfig
```

### 2. 保存小程序配置
```
POST /wechat/sysDeployConfig/edit
```

### 3. 获取租户列表（支持优先级排序）
```
GET /sys/tenant/getTenantDetailsList
```

## 注意事项

1. **配置持久化**：配置保存在数据库中，重启服务不会丢失
2. **缓存机制**：使用Redis缓存提高性能
3. **向后兼容**：未配置优先级时使用默认排序
4. **实时生效**：配置保存后立即影响API返回结果

## 扩展说明

### 1. 自定义服务商类型
如需添加新的服务商类型，需要：
1. 在前端默认配置中添加新类型
2. 在后端排序逻辑中添加识别规则
3. 更新相关文档

### 2. 排序逻辑扩展
当前排序逻辑较为简单，可以根据实际业务需求扩展：
- 根据租户属性判断类型
- 支持更复杂的排序规则
- 添加地理位置等因素

## 测试建议

1. **功能测试**：验证拖拽排序功能
2. **数据持久化测试**：验证配置保存和读取
3. **API测试**：验证租户列表排序效果
4. **兼容性测试**：验证未配置时的默认行为
