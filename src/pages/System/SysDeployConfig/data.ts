// 保司数据类型
export interface InsuranceCompany {
  id: string;
  name: string;
  iconImg?: string;
  enableType?: string;
  isDelete?: string;
  tenantId?: number;
  tenantName?: string;
  createTime?: string;
  updateTime?: string;
}

// 用户数据类型
export interface User {
  id: string;
  username: string;
  realname: string;
  wechatQrcode?: string;
  avatar?: string;
  phone?: string;
  email?: string;
  status?: number;
  createTime?: string;
  orgCodeTxt?: string;
}

// 保司人员配置数据类型（原始格式，用于保存）
export interface CompanyUserConfig {
  [companyId: string]: string[]; // 保司ID对应用户ID数组
}

// 保司人员配置VO（后端返回的结构化数据）
export interface CompanyUserConfigVO {
  companyUsers: CompanyUserItem[];
}

// 保司用户配置项
export interface CompanyUserItem {
  companyId: string;
  companyName: string;
  companyIcon?: string;
  users: UserItem[];
}

// 用户配置项
export interface UserItem {
  userId: string;
  username: string;
  realname: string;
  wechatQrcode?: string;
}

// 系统配置数据类型
export interface SysDeployConfig {
  id?: string;
  deployType: number;
  deployJson: string;
  createTime?: string;
  updateTime?: string;
}

// 基础配置数据类型
export interface BasicConfig {
  nameSwitch?: boolean;
  plateNoMask?: boolean;
  vinNoMask?: boolean;
  phoneSwitch?: boolean;
  queryType?: 'db' | 'query';
  removeQueryMask?: boolean;
  serviceIpType?: 'server' | 'tenant';
  serverIp?: string;
  carInsuranceCount?: number;
  propertyInsuranceCount?: number;
  valueAddedServiceCount?: number;
  newsLikeCountMin?: number;
  newsLikeCountMax?: number;
  clickRate?: number;
}

// API响应数据类型
export interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
  code?: number;
}

// 分页响应数据类型
export interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 地区数据类型
export interface Region {
  id: string;
  code: string;
  name: string;
  parentId?: string;
  level?: number;
}

// 地区配置数据类型（原始格式，用于保存）
export interface RegionConfig {
  headquarterCode?: string; // 总部地区编码
  businessRegions?: string[]; // 业务地区编码数组
  clickChangeLogic?: number; // 今日点击与预约数变化逻辑：0-按照全租户统计，1-按照右侧子公司点击变化
  cityDisplayType?: number; // 预约明细城市展示：0-完整城市名称(省份+城市)，1-城市，2-省份
}

// 地区配置VO（后端返回的结构化数据）
export interface RegionConfigVO {
  headquarterCode?: string;
  headquarterName?: string;
  businessRegions: BusinessRegionItem[];
  clickChangeLogic?: number;
  cityDisplayType?: number;
}

// 业务地区配置项
export interface BusinessRegionItem {
  regionCode: string;
  regionName: string;
}

// 服务商优先级类型
export interface ServiceProviderPriority {
  key: number; // 改为数字：0-所有租户，1-按照城市匹配
  label: string;
  order: number;
}

// 预约成功优先级类型
export interface AppointmentSuccessPriority {
  key: number; // 改为数字：0-默认保司人员，1-地区后勤，2-保司后勤
  label: string;
  order: number;
}

// 小程序配置数据类型（原始格式，用于保存）
export interface MiniProgramConfig {
  companyMultiSelect?: number; // 保司多选开关：0-否，1-是
  propertyInsuranceDisplay?: number; // 财险保司展示开关：0-否，1-是
  valueAddedServiceDisplay?: number; // 增值服务保司展示开关：0-否，1-是
  serviceProviderPriority?: ServiceProviderPriority[]; // 服务商列表优先级顺序
  appointmentSuccessPriority?: AppointmentSuccessPriority[]; // 预约成功优先级顺序
}

// 小程序配置VO（后端返回的结构化数据）
export interface MiniProgramConfigVO {
  companyMultiSelect?: number;
  propertyInsuranceDisplay?: number;
  valueAddedServiceDisplay?: number;
  serviceProviderPriority?: ServiceProviderPriority[];
  appointmentSuccessPriority?: AppointmentSuccessPriority[];
}
