import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Form, Input, Switch, Radio, InputNumber, Button, message, Space, Tooltip, Row, Col, Divider, Select, Tabs, Avatar, List, Transfer, Tree } from 'antd';
import { QuestionCircleOutlined, SaveOutlined, SettingOutlined, TeamOutlined, EnvironmentOutlined, PlusOutlined, DeleteOutlined, MobileOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { querySysDeployConfigById, addSysDeployConfig, editSysDeployConfig, getCompanyUserConfig, getRegionConfig } from '@/services/system/sysDeployConfig';
import { getInsuranceCompanyList } from '@/services/system/insuranceCompany';
import { getUserList } from '@/services/system/user';
import UserSelector from '@/components/UserSelector';
import { getProvinces, getCityTree } from '@/services/system/region';

import type { InsuranceCompany, User, CompanyUserConfig, CompanyUserConfigVO, CompanyUserItem, Region, RegionConfig, RegionConfigVO, MiniProgramConfig, MiniProgramConfigVO, ServiceProviderPriority, AppointmentSuccessPriority } from './data';

const { TabPane } = Tabs;

// 优先级项组件
interface PriorityItemProps {
  item: ServiceProviderPriority;
  index: number;
  total: number;
  onMoveUp: (index: number) => void;
  onMoveDown: (index: number) => void;
}

const PriorityItem: React.FC<PriorityItemProps> = ({ item, index, total, onMoveUp, onMoveDown }) => {
  return (
    <div
      style={{
        padding: '12px 16px',
        margin: '8px 0',
        backgroundColor: '#fafafa',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <span style={{ fontWeight: 500 }}>{item.order}.</span>
        <span>{item.label}</span>
      </div>
      <div style={{ display: 'flex', gap: '8px' }}>
        <Button
          type="text"
          size="small"
          icon={<UpOutlined />}
          disabled={index === 0}
          onClick={() => onMoveUp(index)}
          title="上移"
        />
        <Button
          type="text"
          size="small"
          icon={<DownOutlined />}
          disabled={index === total - 1}
          onClick={() => onMoveDown(index)}
          title="下移"
        />
      </div>
    </div>
  );
};

const SysDeployConfig: React.FC = () => {
  const [basicForm] = Form.useForm();
  const [companyUserForm] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string>('1');
  const [configIds, setConfigIds] = useState<Record<string, string>>({});

  // 保司和用户数据
  const [companies, setCompanies] = useState<InsuranceCompany[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [companyUserConfig, setCompanyUserConfig] = useState<CompanyUserConfig>({});
  const [companyUserConfigVO, setCompanyUserConfigVO] = useState<CompanyUserConfigVO>({ companyUsers: [] });

  // 地区配置数据
  const [provinces, setProvinces] = useState<Region[]>([]);
  const [regionConfig, setRegionConfig] = useState<RegionConfig>({});
  const [regionConfigVO, setRegionConfigVO] = useState<RegionConfigVO>({ businessRegions: [] });

  // 小程序配置数据
  const [miniProgramConfig, setMiniProgramConfig] = useState<MiniProgramConfig>({});
  const [miniProgramConfigVO, setMiniProgramConfigVO] = useState<MiniProgramConfigVO>({});

  // 默认服务商优先级配置
  const defaultServiceProviderPriority: ServiceProviderPriority[] = [
    { key: 0, label: '所有租户', order: 1 },
    { key: 1, label: '按照城市匹配', order: 2 },
  ];

  // 默认预约成功优先级配置
  const defaultAppointmentSuccessPriority: AppointmentSuccessPriority[] = [
    { key: 0, label: '默认保司人员', order: 1 },
    { key: 1, label: '地区后勤', order: 2 },
    { key: 2, label: '保司后勤', order: 3 },
  ];

  // 获取配置信息
  const fetchConfig = async (deployType: number) => {
    try {
      setLoading(true);
      // 根据deployType获取对应的配置
      const response = await querySysDeployConfigById(deployType.toString());

      if (response && response.success && response.result) {
        const { id, deployJson } = response.result;

        // 更新配置ID
        setConfigIds(prev => ({
          ...prev,
          [deployType]: id,
        }));

        // 解析JSON字符串为对象
        if (deployJson) {
          try {
            const configData = JSON.parse(deployJson);

            if (deployType === 1) {
              // 基础配置
              basicForm.setFieldsValue(configData);
            } else if (deployType === 5) {
              // 保司人员配置 - 这里保存原始格式用于提交
              setCompanyUserConfig(configData);
            } else if (deployType === 6) {
              // 地区配置 - 这里保存原始格式用于提交
              setRegionConfig(configData);
            } else if (deployType === 7) {
              // 小程序配置 - 这里保存原始格式用于提交
              setMiniProgramConfig(configData);
              // 同时设置VO格式用于显示
              const serviceProviderPriority = configData.serviceProviderPriority || defaultServiceProviderPriority;
              const appointmentSuccessPriority = configData.appointmentSuccessPriority || defaultAppointmentSuccessPriority;
              setMiniProgramConfigVO({
                companyMultiSelect: configData.companyMultiSelect || 0,
                propertyInsuranceDisplay: configData.propertyInsuranceDisplay || 0,
                valueAddedServiceDisplay: configData.valueAddedServiceDisplay || 0,
                serviceProviderPriority,
                appointmentSuccessPriority,
              });
            }
          } catch (error) {
            console.error('解析配置JSON失败:', error);
            message.error('解析配置信息失败');
          }
        }
      } else {
        // 如果没有找到配置，设置默认值
        if (deployType === 5) {
          setCompanyUserConfig({});
        } else if (deployType === 6) {
          setRegionConfig({});
        } else if (deployType === 7) {
          const defaultConfig = {
            companyMultiSelect: 0,
            propertyInsuranceDisplay: 0,
            valueAddedServiceDisplay: 0,
            serviceProviderPriority: defaultServiceProviderPriority,
            appointmentSuccessPriority: defaultAppointmentSuccessPriority,
          };
          setMiniProgramConfig(defaultConfig);
          setMiniProgramConfigVO(defaultConfig);
        }
      }
    } catch (error) {
      console.error('获取配置信息失败:', error);
      message.error('获取配置信息失败');
      // 设置默认值
      if (deployType === 5) {
        setCompanyUserConfig({});
      } else if (deployType === 6) {
        setRegionConfig({});
      } else if (deployType === 7) {
        const defaultConfig = {
          companyMultiSelect: 0,
          propertyInsuranceDisplay: 0,
          valueAddedServiceDisplay: 0,
          serviceProviderPriority: defaultServiceProviderPriority,
          appointmentSuccessPriority: defaultAppointmentSuccessPriority,
        };
        setMiniProgramConfig(defaultConfig);
        setMiniProgramConfigVO(defaultConfig);
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取保司列表
  const fetchCompanies = async () => {
    try {
      const response = await getInsuranceCompanyList();
      if (response && response.success && response.result) {
        setCompanies(response.result.records || response.result);
      }
    } catch (error) {
      console.error('获取保司列表失败:', error);
      message.error('获取保司列表失败');
    }
  };

  // 获取用户列表（仅用于显示已选用户的详细信息）
  const fetchUsers = async () => {
    try {
      // 只获取少量用户用于显示已配置用户的详细信息
      const response = await getUserList({ pageNo: 1, pageSize: 100 });
      if (response && response.success && response.result) {
        setUsers(response.result.records || response.result);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    }
  };

  // 获取结构化的保司人员配置
  const fetchCompanyUserConfigVO = async () => {
    try {
      const response = await getCompanyUserConfig();
      if (response && response.success && response.result) {
        setCompanyUserConfigVO(response.result);

        // 如果后端返回了数据，同时更新原始配置格式
        if (response.result.companyUsers && response.result.companyUsers.length > 0) {
          const rawConfig: CompanyUserConfig = {};
          response.result.companyUsers.forEach((company: CompanyUserItem) => {
            rawConfig[company.companyId] = company.users.map(user => user.userId);
          });
          setCompanyUserConfig(rawConfig);
        }
      } else {
        // 如果后端没有返回数据，设置空的默认值
        setCompanyUserConfigVO({ companyUsers: [] });
      }
    } catch (error) {
      console.error('获取保司人员配置失败:', error);
      // 不显示错误消息，因为可能是首次访问没有配置
      setCompanyUserConfigVO({ companyUsers: [] });
    }
  };

  // 合并保司数据和配置数据，确保所有保司都显示
  const mergeCompanyUserData = () => {
    if (companies.length === 0) return;

    const existingCompanyIds = new Set(companyUserConfigVO.companyUsers?.map(c => c.companyId) || []);
    const allCompanyUsers = [...(companyUserConfigVO.companyUsers || [])];

    // 添加没有配置的保司
    companies.forEach(company => {
      if (!existingCompanyIds.has(company.id)) {
        allCompanyUsers.push({
          companyId: company.id,
          companyName: company.name, // 使用正确的字段名
          companyIcon: company.iconImg, // 使用正确的字段名
          users: []
        });
      }
    });

    setCompanyUserConfigVO(prev => ({
      ...prev,
      companyUsers: allCompanyUsers
    }));
  };



  // 获取省份列表
  const fetchProvinces = async () => {
    try {
      const response = await getProvinces();
      if (response && response.success && response.result) {
        setProvinces(response.result);
      }
    } catch (error) {
      console.error('获取省份列表失败:', error);
      message.error('获取省份列表失败');
    }
  };

  // 获取结构化的地区配置
  const fetchRegionConfigVO = async () => {
    try {
      const response = await getRegionConfig();
      if (response && response.success && response.result) {
        setRegionConfigVO(response.result);

        // 如果后端返回了数据，同时更新原始配置格式
        if (response.result.headquarterCode || response.result.businessRegions) {
          const rawConfig: RegionConfig = {
            headquarterCode: response.result.headquarterCode,
            businessRegions: response.result.businessRegions?.map((region: any) => region.regionCode) || [],
            clickChangeLogic: response.result.clickChangeLogic,
            cityDisplayType: response.result.cityDisplayType,
          };
          setRegionConfig(rawConfig);
        }
      } else {
        // 如果后端没有返回数据，设置空的默认值
        setRegionConfigVO({ businessRegions: [] });
      }
    } catch (error) {
      console.error('获取地区配置失败:', error);
      // 不显示错误消息，因为可能是首次访问没有配置
      setRegionConfigVO({ businessRegions: [] });
    }
  };





  // 首次加载时获取配置和数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);

        // 获取基础配置
        await fetchConfig(1);

        // 获取保司人员配置（原始格式和结构化格式）
        await fetchConfig(5);
        await fetchCompanyUserConfigVO();

        // 获取地区配置（原始格式和结构化格式）
        await fetchConfig(6);
        await fetchRegionConfigVO();

        // 获取小程序配置（原始格式）
        await fetchConfig(7);

        // 获取基础数据
        await fetchCompanies();
        await fetchUsers();
        await fetchProvinces();

        console.log('所有配置和数据初始化完成');

        // 初始化完成后，合并保司数据
        setTimeout(() => {
          mergeCompanyUserData();
        }, 100);
      } catch (error) {
        console.error('初始化数据失败:', error);
        message.error('初始化数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, []);

  // 当保司数据和配置数据都加载完成后，合并数据
  useEffect(() => {
    if (companies.length > 0) {
      mergeCompanyUserData();
    }
  }, [companies]); // 只依赖companies，避免无限循环

  // 保存配置
  const handleSave = async (deployType: number) => {
    try {
      setLoading(true);

      let deployJson: string;
      let configId: string;

      if (deployType === 1) {
        // 基础配置
        const values = await basicForm.validateFields();
        deployJson = JSON.stringify(values);
        configId = configIds[1] || '';
      } else if (deployType === 5) {
        // 保司人员配置
        deployJson = JSON.stringify(companyUserConfig);
        configId = configIds[5] || '';
      } else if (deployType === 6) {
        // 地区配置
        deployJson = JSON.stringify(regionConfig);
        configId = configIds[6] || '';
      } else if (deployType === 7) {
        // 小程序配置 - 确保包含最新的优先级配置
        const configToSave = {
          ...miniProgramConfig,
          serviceProviderPriority: miniProgramConfigVO.serviceProviderPriority || defaultServiceProviderPriority,
          appointmentSuccessPriority: miniProgramConfigVO.appointmentSuccessPriority || defaultAppointmentSuccessPriority,
        };
        deployJson = JSON.stringify(configToSave);
        configId = configIds[7] || '';
      } else {
        message.error('未知的配置类型');
        return;
      }

      const params = {
        id: configId,
        deployJson,
        deployType,
      };

      // 根据是否有ID决定是新增还是编辑
      const submitFunc = configId ? editSysDeployConfig : addSysDeployConfig;
      const response = await submitFunc(params);

      if (response && response.success) {
        const configName = deployType === 1 ? '基础配置' : deployType === 5 ? '保司人员配置' : deployType === 6 ? '地图配置' : '小程序配置';
        message.success(`${configName}保存成功`);

        // 如果是新增，保存后获取新的ID
        if (!configId && response.result) {
          setConfigIds(prev => ({
            ...prev,
            [deployType]: response.result,
          }));
        }
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 表单布局
  // 处理保司人员配置变更
  const handleCompanyUserChange = (companyId: string, selectedUserIds: string[]) => {
    // 更新原始配置格式
    setCompanyUserConfig(prev => ({
      ...prev,
      [companyId]: selectedUserIds,
    }));

    // 同时更新VO格式用于显示
    setCompanyUserConfigVO(prev => ({
      ...prev,
      companyUsers: prev.companyUsers.map(company => {
        if (company.companyId === companyId) {
          const selectedUsers = users.filter(user => selectedUserIds.includes(user.id));
          return {
            ...company,
            users: selectedUsers.map(user => ({
              userId: user.id,
              username: user.username,
              realname: user.realname,
              wechatQrcode: user.wechatQrcode,
            })),
          };
        }
        return company;
      }),
    }));
  };

  // 获取保司已配置的用户
  const getCompanyUsers = (companyId: string): string[] => {
    return companyUserConfig[companyId] || [];
  };

  // 处理总部地区变更
  const handleHeadquarterChange = (headquarterCode: string) => {
    const province = provinces.find(p => p.code === headquarterCode);
    if (!province) return;

    // 更新原始配置格式
    setRegionConfig(prev => ({
      ...prev,
      headquarterCode,
      businessRegions: prev.businessRegions || [],
    }));

    // 同时更新VO格式用于显示
    setRegionConfigVO(prev => ({
      ...prev,
      headquarterCode,
      headquarterName: province.name,
    }));
  };

  // 处理业务地区变更
  const handleBusinessRegionsChange = (selectedRegionCodes: string[]) => {
    // 更新原始配置格式
    setRegionConfig(prev => ({
      ...prev,
      businessRegions: selectedRegionCodes,
    }));

    // 同时更新VO格式用于显示
    const selectedRegions = selectedRegionCodes.map(regionCode => {
      const province = provinces.find(p => p.code === regionCode);
      return {
        regionCode,
        regionName: province ? province.name : regionCode,
      };
    });

    setRegionConfigVO(prev => ({
      ...prev,
      businessRegions: selectedRegions,
    }));
  };

  // 处理数据大屏配置变化
  const handleDataScreenConfigChange = (field: keyof RegionConfig, value: number) => {
    // 更新原始配置格式
    setRegionConfig(prev => ({
      ...prev,
      [field]: value,
    }));

    // 更新VO格式
    setRegionConfigVO(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 获取已配置的业务地区
  const getBusinessRegions = (): string[] => {
    return regionConfig.businessRegions || [];
  };

  // 处理小程序配置变化
  const handleMiniProgramConfigChange = (field: keyof MiniProgramConfig, value: number) => {
    // 更新原始配置格式
    setMiniProgramConfig(prev => ({
      ...prev,
      [field]: value,
    }));

    // 更新VO格式
    setMiniProgramConfigVO(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 处理服务商优先级上移
  const handleServiceProviderPriorityMoveUp = (index: number) => {
    if (index === 0) return;

    const currentPriority = miniProgramConfigVO.serviceProviderPriority || defaultServiceProviderPriority;
    const newPriority = [...currentPriority];

    // 交换位置
    [newPriority[index], newPriority[index - 1]] = [newPriority[index - 1], newPriority[index]];

    // 重新设置order
    const updatedPriority = newPriority.map((item, idx) => ({
      ...item,
      order: idx + 1,
    }));

    // 更新原始配置格式
    setMiniProgramConfig(prev => ({
      ...prev,
      serviceProviderPriority: updatedPriority,
    }));

    // 更新VO格式
    setMiniProgramConfigVO(prev => ({
      ...prev,
      serviceProviderPriority: updatedPriority,
    }));
  };

  // 处理服务商优先级下移
  const handleServiceProviderPriorityMoveDown = (index: number) => {
    const currentPriority = miniProgramConfigVO.serviceProviderPriority || defaultServiceProviderPriority;
    if (index === currentPriority.length - 1) return;

    const newPriority = [...currentPriority];

    // 交换位置
    [newPriority[index], newPriority[index + 1]] = [newPriority[index + 1], newPriority[index]];

    // 重新设置order
    const updatedPriority = newPriority.map((item, idx) => ({
      ...item,
      order: idx + 1,
    }));

    // 更新原始配置格式
    setMiniProgramConfig(prev => ({
      ...prev,
      serviceProviderPriority: updatedPriority,
    }));

    // 更新VO格式
    setMiniProgramConfigVO(prev => ({
      ...prev,
      serviceProviderPriority: updatedPriority,
    }));
  };

  // 处理预约成功优先级上移
  const handleAppointmentSuccessPriorityMoveUp = (index: number) => {
    if (index === 0) return;

    const currentPriority = miniProgramConfigVO.appointmentSuccessPriority || defaultAppointmentSuccessPriority;
    const newPriority = [...currentPriority];

    // 交换位置
    [newPriority[index], newPriority[index - 1]] = [newPriority[index - 1], newPriority[index]];

    // 重新设置order
    const updatedPriority = newPriority.map((item, idx) => ({
      ...item,
      order: idx + 1,
    }));

    // 更新原始配置格式
    setMiniProgramConfig(prev => ({
      ...prev,
      appointmentSuccessPriority: updatedPriority,
    }));

    // 更新VO格式
    setMiniProgramConfigVO(prev => ({
      ...prev,
      appointmentSuccessPriority: updatedPriority,
    }));
  };

  // 处理预约成功优先级下移
  const handleAppointmentSuccessPriorityMoveDown = (index: number) => {
    const currentPriority = miniProgramConfigVO.appointmentSuccessPriority || defaultAppointmentSuccessPriority;
    if (index === currentPriority.length - 1) return;

    const newPriority = [...currentPriority];

    // 交换位置
    [newPriority[index], newPriority[index + 1]] = [newPriority[index + 1], newPriority[index]];

    // 重新设置order
    const updatedPriority = newPriority.map((item, idx) => ({
      ...item,
      order: idx + 1,
    }));

    // 更新原始配置格式
    setMiniProgramConfig(prev => ({
      ...prev,
      appointmentSuccessPriority: updatedPriority,
    }));

    // 更新VO格式
    setMiniProgramConfigVO(prev => ({
      ...prev,
      appointmentSuccessPriority: updatedPriority,
    }));
  };



  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  return (
    <PageContainer title="系统配置">
      <Card loading={loading}>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          type="card"
        >
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                基础配置
              </span>
            }
            key="1"
          >
            <Form
              form={basicForm}
              {...formItemLayout}
              initialValues={{
                nameSwitch: false,
                plateNoMask: false,
                vinNoMask: false,
                phoneSwitch: false,
                queryType: 'db',
                removeQueryMask: false,
                serviceIpType: 'server',
                serverIp: '',
                carInsuranceCount: 0,
                propertyInsuranceCount: 0,
                valueAddedServiceCount: 0,
                newsLikeCountMin: 50,
                newsLikeCountMax: 500,
                clickRate: 0.5,
              }}
            >
          <Divider orientation="left">基础配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置系统的基本功能和显示方式</p>

          <Form.Item
            name="nameSwitch"
            label="姓名切换为先生/女士"
            valuePropName="checked"
            extra="开启后，系统将自动将姓名替换为先生/女士称呼"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="plateNoMask"
            label="车牌号码脱敏"
            valuePropName="checked"
            extra="开启后，系统将对车牌号码进行脱敏处理，如：粤A****8"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="vinNoMask"
            label="车架号脱敏"
            valuePropName="checked"
            extra="开启后，系统将对车架号进行脱敏处理，如：LSVA****9876"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="phoneSwitch"
            label="手机号脱敏"
            valuePropName="checked"
            extra="开启后，系统将对手机号进行脱敏处理，如：138****8888"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="queryType"
            label={
              <span>
                以上数据实现方式
                <Tooltip title="选择数据实现的方式，只能选择一种">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="说明：数据库存储后期无法还原数据"
          >
            <Radio.Group>
              <Radio value="db">数据库存储</Radio>
              <Radio value="query">查询实现脱敏</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.queryType !== currentValues.queryType}
          >
            {({ getFieldValue }) => {
              // 只在选择“查询实现脱敏”时显示“去除查询脱敏”选项
              return getFieldValue('queryType') === 'query' ? (
                <Form.Item
                  name="removeQueryMask"
                  label={
                    <span>
                      去除查询脱敏
                      <Tooltip title="选择是否去除查询脱敏">
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                  valuePropName="checked"
                  extra="说明：需要为查询实现脱敏，已存储为脱敏数据的无法实现"
                >
                  <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="serviceIpType"
            label={(
              <span>
                客服IP来源
                <Tooltip title="选择客服IP的来源方式，可以使用服务器IP或租户设置中的IP">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            )}
            extra="选择客服IP的来源方式"
          >
            <Select placeholder="请选择客服IP来源">
              <Select.Option value="server">服务器IP</Select.Option>
              <Select.Option value="tenant">租户设置中IP(若租户IP为空取服务器IP)</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.serviceIpType !== currentValues.serviceIpType}
          >
            {({ getFieldValue }) => {
              // 当选择服务器IP或租户IP为空时需要服务器IP，所以始终显示该字段
              return (
                <Form.Item
                  name="serverIp"
                  label="服务器IP"
                  extra="设置服务器的IP地址，当选择租户IP且租户IP为空时使用"
                >
                  <Input placeholder="请输入服务器IP地址" />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Divider orientation="left">聊天源配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置不同业务类型的聊天源数量，需要确保场景库中有足够的场景数量</p>

          <Form.Item
            name="carInsuranceCount"
            label={
              <span>
                车险聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="propertyInsuranceCount"
            label={
              <span>
                财险聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="valueAddedServiceCount"
            label={
              <span>
                增值服务聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Divider orientation="left">资讯配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置资讯相关的参数</p>

          <Form.Item
            label={
              <span>
                资讯点赞数区间
                <Tooltip title="设置资讯点赞数的随机生成区间，系统将在此区间内随机生成点赞数">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={11}>
                <Form.Item
                  name="newsLikeCountMin"
                  noStyle
                  rules={[{ required: true, message: '请输入最小值' }]}
                >
                  <InputNumber
                    placeholder="最小值"
                    min={0}
                    style={{ width: '100%' }}
                    addonAfter="次"
                  />
                </Form.Item>
              </Col>
              <Col span={2} style={{ textAlign: 'center' }}>
                <span>至</span>
              </Col>
              <Col span={11}>
                <Form.Item
                  name="newsLikeCountMax"
                  noStyle
                  rules={[
                    { required: true, message: '请输入最大值' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newsLikeCountMin') <= value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('最大值必须大于或等于最小值'));
                      },
                    }),
                  ]}
                >
                  <InputNumber
                    placeholder="最大值"
                    min={0}
                    style={{ width: '100%' }}
                    addonAfter="次"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item
            name="clickRate"
            label={
              <span>
                最低点击率
                <Tooltip title="设置资讯的最低点击率，系统将确保点击率不低于此值">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="设置资讯的最低点击率阈值，取值范围0-1"
          >
            <InputNumber
              placeholder="请输入最低点击率"
              min={0}
              max={1}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              addonAfter="%"
              formatter={value => value ? `${(Number(value) * 100).toFixed(2)}` : ''}
              parser={value => value ? Number(value) / 100 : 0}
            />
          </Form.Item>

          <Divider orientation="left">操作</Divider>
          <Row>
            <Col span={14} offset={6}>
              <Form.Item>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => handleSave(1)}
                    loading={loading}
                    block
                  >
                    保存基础配置
                  </Button>
                  <div style={{ color: '#999', fontSize: '12px', textAlign: 'center' }}>
                    注意：配置保存后将立即生效
                  </div>
                </Space>
              </Form.Item>
            </Col>
          </Row>
            </Form>
          </TabPane>

          <TabPane
            tab={
              <span>
                <TeamOutlined />
                默认保司人员配置
              </span>
            }
            key="5"
          >
            <div style={{ padding: '20px 0' }}>
              <Divider orientation="left">保司人员配置</Divider>
              <p style={{ marginBottom: '20px' }}>为每个保司配置对应的默认人员，用于系统自动分配</p>

              <List
                grid={{ gutter: 16, column: 1 }}
                dataSource={companyUserConfigVO.companyUsers}
                renderItem={(company) => (
                  <List.Item>
                    <Card
                      size="small"
                      title={
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          {company.companyIcon && (
                            <Avatar
                              src={company.companyIcon}
                              size="small"
                              style={{ marginRight: 8 }}
                            />
                          )}
                          <span>{company.companyName}</span>
                        </div>
                      }
                      extra={
                        <span style={{ color: '#666', fontSize: '12px' }}>
                          已配置 {company.users?.length || 0} 人
                        </span>
                      }
                    >
                      <UserSelector
                        value={getCompanyUsers(company.companyId)}
                        onChange={(targetKeys) => handleCompanyUserChange(company.companyId, targetKeys)}
                        listStyle={{
                          width: 300,
                          height: 300,
                        }}
                        titles={['可选用户', '已配置用户']}
                      />
                    </Card>
                  </List.Item>
                )}
              />

              <Divider orientation="left">操作</Divider>
              <Row>
                <Col span={14} offset={6}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => handleSave(5)}
                      loading={loading}
                      block
                    >
                      保存保司人员配置
                    </Button>
                    <div style={{ color: '#999', fontSize: '12px', textAlign: 'center' }}>
                      注意：配置保存后将立即生效
                    </div>
                  </Space>
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <EnvironmentOutlined />
                地图配置
              </span>
            }
            key="6"
          >
            <div style={{ padding: '20px 0' }}>
              <Divider orientation="left">总部与业务地区配置</Divider>
              <p style={{ marginBottom: '20px' }}>配置总部地区与业务地区的对应关系（一级节点），用于地图展示和业务分配</p>

              {/* 总部地区选择 */}
              <Card size="small" style={{ marginBottom: '20px' }}>
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <span style={{ fontWeight: 'bold' }}>总部地区：</span>
                  </Col>
                  <Col span={8}>
                    <Select
                      placeholder="选择总部地区"
                      style={{ width: '100%' }}
                      value={regionConfigVO.headquarterCode}
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        option?.children?.toLowerCase().includes(input.toLowerCase())
                      }
                      onChange={handleHeadquarterChange}
                    >
                      {provinces.map(province => (
                        <Select.Option key={province.code} value={province.code}>
                          {province.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={12}>
                    <div style={{ color: '#666', fontSize: '12px' }}>
                      {regionConfigVO.headquarterName ?
                        `当前总部：${regionConfigVO.headquarterName}` :
                        '请先选择总部地区'
                      }
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 业务地区配置 */}
              {regionConfigVO.headquarterCode && (
                <Card
                  size="small"
                  title={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <EnvironmentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                      <span>业务地区配置</span>
                      <span style={{ color: '#666', fontSize: '12px', marginLeft: 16 }}>
                        已配置 {regionConfigVO.businessRegions?.length || 0} 个业务地区
                      </span>
                    </div>
                  }
                  style={{ marginBottom: '20px' }}
                >
                  <Transfer
                    dataSource={provinces
                      .filter(province => province.code !== regionConfigVO.headquarterCode)
                      .map(province => ({
                        key: province.code,
                        title: province.name,
                      }))
                    }
                    targetKeys={getBusinessRegions()}
                    onChange={handleBusinessRegionsChange}
                    render={item => item.title}
                    listStyle={{
                      width: 300,
                      height: 400,
                    }}
                    titles={['可选地区', '业务地区']}
                    showSearch
                    filterOption={(inputValue, option) =>
                      option.title.toLowerCase().includes(inputValue.toLowerCase())
                    }
                  />
                </Card>
              )}

              {/* 数据大屏配置 */}
              <Divider orientation="left">数据大屏显示配置</Divider>
              <p style={{ marginBottom: '20px' }}>配置数据大屏中的数据展示逻辑和城市显示方式</p>

              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Card title="数据统计配置" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Form.Item
                          label="今日点击与预约数变化逻辑"
                          style={{ marginBottom: 0 }}
                        >
                          <Select
                            placeholder="请选择变化逻辑"
                            style={{ width: '100%' }}
                            value={regionConfigVO.clickChangeLogic}
                            onChange={(value) => handleDataScreenConfigChange('clickChangeLogic', value)}
                          >
                            <Select.Option value={0}>按照全租户统计</Select.Option>
                            <Select.Option value={1}>按照右侧子公司点击变化</Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="城市显示配置" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Form.Item
                          label="预约明细城市展示"
                          style={{ marginBottom: 0 }}
                        >
                          <Select
                            placeholder="请选择城市展示方式"
                            style={{ width: '100%' }}
                            value={regionConfigVO.cityDisplayType}
                            onChange={(value) => handleDataScreenConfigChange('cityDisplayType', value)}
                          >
                            <Select.Option value={0}>完整城市名称(省份+城市浙江省杭州市)</Select.Option>
                            <Select.Option value={1}>城市(杭州市)</Select.Option>
                            <Select.Option value={2}>省份(浙江省)</Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>

              <Divider orientation="left">操作</Divider>
              <Row>
                <Col span={14} offset={6}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => handleSave(6)}
                      loading={loading}
                      block
                      disabled={!regionConfigVO.headquarterCode}
                    >
                      保存地图配置
                    </Button>
                    <div style={{ color: '#999', fontSize: '12px', textAlign: 'center' }}>
                      注意：配置保存后将立即生效，影响地图展示和业务分配
                    </div>
                  </Space>
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MobileOutlined />
                小程序设置
              </span>
            }
            key="7"
          >
            <div style={{ padding: '20px 0' }}>
              <Divider orientation="left">小程序功能配置</Divider>
              <p style={{ marginBottom: '20px' }}>配置小程序中的功能开关，控制保司展示和选择方式</p>

              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Card title="保司选择配置" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <span>
                            保司多选
                            <Tooltip title="开启后用户可以同时选择多个保司">
                              <QuestionCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
                            </Tooltip>
                          </span>
                          <Switch
                            checked={miniProgramConfigVO.companyMultiSelect === 1}
                            onChange={(checked) => handleMiniProgramConfigChange('companyMultiSelect', checked ? 1 : 0)}
                          />
                        </div>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="保司展示配置" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <span>
                            财险保司展示
                            <Tooltip title="开启后在小程序中显示财险保司">
                              <QuestionCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
                            </Tooltip>
                          </span>
                          <Switch
                            checked={miniProgramConfigVO.propertyInsuranceDisplay === 1}
                            onChange={(checked) => handleMiniProgramConfigChange('propertyInsuranceDisplay', checked ? 1 : 0)}
                          />
                        </div>
                      </Col>
                      <Col span={12}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <span>
                            增值服务保司展示
                            <Tooltip title="开启后在小程序中显示增值服务保司">
                              <QuestionCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
                            </Tooltip>
                          </span>
                          <Switch
                            checked={miniProgramConfigVO.valueAddedServiceDisplay === 1}
                            onChange={(checked) => handleMiniProgramConfigChange('valueAddedServiceDisplay', checked ? 1 : 0)}
                          />
                        </div>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="服务商列表优先级顺序" size="small">
                    <div style={{ marginBottom: '16px' }}>
                      <p style={{ color: '#666', margin: 0 }}>
                        点击上下箭头来调整服务商在列表中的显示优先级，排序越靠前优先级越高
                      </p>
                    </div>
                    <div style={{ maxWidth: '400px' }}>
                      {(miniProgramConfigVO.serviceProviderPriority || defaultServiceProviderPriority).map((item, index) => (
                        <PriorityItem
                          key={item.key}
                          item={item}
                          index={index}
                          total={(miniProgramConfigVO.serviceProviderPriority || defaultServiceProviderPriority).length}
                          onMoveUp={handleServiceProviderPriorityMoveUp}
                          onMoveDown={handleServiceProviderPriorityMoveDown}
                        />
                      ))}
                    </div>
                    <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
                      <div style={{ fontSize: '12px', color: '#52c41a', marginBottom: '8px' }}>
                        <strong>说明：</strong>
                      </div>
                      <ul style={{ fontSize: '12px', color: '#666', margin: 0, paddingLeft: '16px' }}>
                        <li><strong>所有租户</strong>：显示系统中所有的租户服务商</li>
                        <li><strong>按照城市匹配</strong>：根据用户所在城市显示匹配的服务商</li>
                      </ul>
                    </div>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="预约成功优先级顺序" size="small">
                    <div style={{ marginBottom: '16px' }}>
                      <p style={{ color: '#666', margin: 0 }}>
                        点击上下箭头来调整预约成功时的处理优先级，排序越靠前优先级越高
                      </p>
                    </div>
                    <div style={{ maxWidth: '400px' }}>
                      {(miniProgramConfigVO.appointmentSuccessPriority || defaultAppointmentSuccessPriority).map((item, index) => (
                        <PriorityItem
                          key={item.key}
                          item={item}
                          index={index}
                          total={(miniProgramConfigVO.appointmentSuccessPriority || defaultAppointmentSuccessPriority).length}
                          onMoveUp={handleAppointmentSuccessPriorityMoveUp}
                          onMoveDown={handleAppointmentSuccessPriorityMoveDown}
                        />
                      ))}
                    </div>
                    <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#e6f7ff', border: '1px solid #91d5ff', borderRadius: '6px' }}>
                      <div style={{ fontSize: '12px', color: '#1890ff', marginBottom: '8px' }}>
                        <strong>说明：</strong>
                      </div>
                      <ul style={{ fontSize: '12px', color: '#666', margin: 0, paddingLeft: '16px' }}>
                        <li><strong>默认保司人员</strong>：优先分配给保司配置的默认人员</li>
                        <li><strong>地区后勤</strong>：分配给用户所在地区的后勤人员</li>
                        <li><strong>保司后勤</strong>：分配给保险公司的后勤人员</li>
                      </ul>
                    </div>
                  </Card>
                </Col>
              </Row>

              <Divider orientation="left">操作</Divider>
              <Row>
                <Col span={14} offset={6}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => handleSave(7)}
                      loading={loading}
                      block
                    >
                      保存小程序配置
                    </Button>
                    <div style={{ color: '#999', fontSize: '12px', textAlign: 'center' }}>
                      注意：配置保存后将立即生效，影响小程序功能展示
                    </div>
                  </Space>
                </Col>
              </Row>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default SysDeployConfig;
