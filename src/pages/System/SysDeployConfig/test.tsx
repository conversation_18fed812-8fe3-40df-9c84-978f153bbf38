import React from 'react';
import { Card, Tabs, Button, message } from 'antd';
import { SettingOutlined, TeamOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;

const TestSysDeployConfig: React.FC = () => {
  const handleTest = () => {
    message.success('测试成功！页面组件正常工作');
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="系统配置测试页面">
        <Tabs defaultActiveKey="1" type="card">
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                基础配置
              </span>
            }
            key="1"
          >
            <div style={{ padding: '20px' }}>
              <p>基础配置页面内容</p>
              <Button type="primary" onClick={handleTest}>
                测试基础配置
              </Button>
            </div>
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <TeamOutlined />
                默认保司人员配置
              </span>
            }
            key="5"
          >
            <div style={{ padding: '20px' }}>
              <p>保司人员配置页面内容</p>
              <Button type="primary" onClick={handleTest}>
                测试保司人员配置
              </Button>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TestSysDeployConfig;
