# 业务城市为空查询功能更新

## 修改内容

### 前端下拉选项简化
**修改前**：
```javascript
valueEnum: {
  true: { text: '是' },
  false: { text: '否' },
}
```

**修改后**：
```javascript
valueEnum: {
  true: { text: '是' },
}
```

### 参数处理逻辑优化
**修改前**：
```javascript
businessCitiesEmpty: businessCitiesEmpty === 'true' ? true : businessCitiesEmpty === 'false' ? false : undefined
```

**修改后**：
```javascript
businessCitiesEmpty: businessCitiesEmpty === 'true' ? true : undefined
```

## 功能说明

### 用户操作
- 用户可以选择"是"来查询业务城市为空的租户
- 如果不选择任何选项，则不应用此筛选条件
- 简化了用户选择，避免了"否"选项的混淆

### 后端处理
- 当 `businessCitiesEmpty` 为 `true` 时，查询没有业务城市的租户
- 当 `businessCitiesEmpty` 为 `undefined` 时，不应用此筛选条件
- 后端逻辑保持不变，无需修改

## 使用场景

这个功能主要用于：
1. **数据清理**：找出还没有设置业务城市的租户
2. **数据完整性检查**：确保所有租户都有完整的业务信息
3. **运营管理**：识别需要补充业务城市信息的租户

## 测试建议

1. **不选择任何选项**：应该显示所有租户（不应用筛选）
2. **选择"是"**：应该只显示没有设置业务城市的租户
3. **与其他筛选条件组合**：测试与租户名称、业务类型等条件的组合筛选

## 优势

1. **简化用户体验**：只有一个有意义的选项，避免用户困惑
2. **逻辑清晰**：明确表达查询意图
3. **减少误操作**：避免用户误选"否"选项导致的查询结果混淆
