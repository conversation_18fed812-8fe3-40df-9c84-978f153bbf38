import React, { useState, useEffect } from 'react';
import { Modal, Steps, Button, message, Card, Typography, Space, List, Avatar, Tree, Tag, Divider } from 'antd';
import { UserOutlined, EnvironmentOutlined, CheckOutlined, DeleteOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';
import { getTenantUserList } from '@/services/system/tenant';
import type { TenantUser } from '../data';
import type { DataNode } from 'antd/es/tree';

const { Title, Text } = Typography;
const { Step } = Steps;

interface BatchConfigModalProps {
  visible: boolean;
  tenantId: string;
  tenantName?: string;
  insuranceCompanyId: string;
  insuranceCompanyName: string;
  onCancel: () => void;
  onSuccess: () => void;
}

interface Region {
  id: string;
  code: string;
  name: string;
  children?: Region[];
}

interface UserRegionRelation {
  userId: string;
  userName: string;
  userRealname: string;
  regionCode: string;
  regionName: string;
}

const BatchConfigModal: React.FC<BatchConfigModalProps> = ({
  visible,
  tenantId,
  tenantName = '',
  insuranceCompanyId,
  insuranceCompanyName,
  onCancel,
  onSuccess,
}) => {
  // 步骤控制
  const [currentStep, setCurrentStep] = useState(0);

  // 数据状态
  const [userList, setUserList] = useState<TenantUser[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<TenantUser[]>([]);
  const [regionTreeData, setRegionTreeData] = useState<DataNode[]>([]);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [relations, setRelations] = useState<UserRegionRelation[]>([]);

  // 加载状态
  const [userLoading, setUserLoading] = useState(false);
  const [regionLoading, setRegionLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setSelectedUsers([]);
    setSelectedRegions([]);
    setRelations([]);
  };

  // 获取租户用户列表
  const fetchUserList = async () => {
    if (!tenantId) return;

    setUserLoading(true);
    try {
      const response = await getTenantUserList({ pageNo: 1, pageSize: 1000 });
      if (response.success) {
        setUserList(response.result?.records || []);
      } else {
        message.error('获取用户列表失败');
      }
    } catch (error) {
      console.error('获取用户列表出错:', error);
      message.error('获取用户列表失败');
    } finally {
      setUserLoading(false);
    }
  };

  // 获取地区树数据
  const fetchRegionTree = async () => {
    setRegionLoading(true);
    try {
      const response = await request('/wechat/eaRegion/getCityTree', {
        method: 'GET',
      });

      if (response.success) {
        const treeData = convertToTreeData(response.result || []);
        setRegionTreeData(treeData);
      } else {
        message.error('获取地区数据失败');
      }
    } catch (error) {
      console.error('获取地区数据出错:', error);
      message.error('获取地区数据失败');
    } finally {
      setRegionLoading(false);
    }
  };

  // 转换地区数据为Tree组件需要的格式
  const convertToTreeData = (regions: Region[]): DataNode[] => {
    return regions.map(region => ({
      title: region.name,
      key: region.code,
      value: region.code,
      children: region.children ? convertToTreeData(region.children) : undefined,
    }));
  };

  // 根据地区编码获取地区名称
  const getRegionNameByCode = (code: string, treeData: DataNode[]): string => {
    for (const node of treeData) {
      if (node.key === code) {
        return node.title as string;
      }
      if (node.children) {
        const name = getRegionNameByCode(code, node.children);
        if (name) return name;
      }
    }
    return code;
  };

  // 生成用户-地区关系
  const generateRelations = () => {
    const newRelations: UserRegionRelation[] = [];
    selectedUsers.forEach(user => {
      selectedRegions.forEach(regionCode => {
        newRelations.push({
          userId: user.id,
          userName: user.username,
          userRealname: user.realname,
          regionCode,
          regionName: getRegionNameByCode(regionCode, regionTreeData),
        });
      });
    });
    setRelations(newRelations);
  };

  // 删除单个关系
  const removeRelation = (userId: string, regionCode: string) => {
    setRelations(prev => prev.filter(rel =>
      !(rel.userId === userId && rel.regionCode === regionCode)
    ));
  };

  // 批量保存关系
  const handleSave = async () => {
    if (relations.length === 0) {
      message.warning('没有要保存的关系');
      return;
    }

    setSaving(true);
    try {
      // 按用户分组，构造批量保存的数据
      const userRegionMap = new Map<string, string[]>();
      relations.forEach(rel => {
        if (!userRegionMap.has(rel.userId)) {
          userRegionMap.set(rel.userId, []);
        }
        userRegionMap.get(rel.userId)!.push(rel.regionCode);
      });

      // 构造批量数据格式：userId1:regionCode1,regionCode2;userId2:regionCode3
      const batchDataArray: string[] = [];
      for (const [userId, regionCodes] of userRegionMap) {
        batchDataArray.push(`${userId}:${regionCodes.join(',')}`);
      }
      const batchData = batchDataArray.join(';');

      // 调用批量保存接口
      const response = await request('/sys/tenant/logistics/batchAdd', {
        method: 'POST',
        params: {
          userTenantId: tenantId,
          insuranceCompanyId,
          batchData,
        },
      });

      if (response.success) {
        message.success(`成功保存 ${relations.length} 条配置关系`);
        onSuccess();
        handleCancel();
      } else {
        throw new Error(response.message || '批量保存失败');
      }
    } catch (error) {
      console.error('批量保存失败:', error);
      message.error(error instanceof Error ? error.message : '批量保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    resetState();
    onCancel();
  };

  // 步骤操作
  const handleNext = () => {
    if (currentStep === 0) {
      if (selectedUsers.length === 0) {
        message.warning('请至少选择一个用户');
        return;
      }
    } else if (currentStep === 1) {
      if (selectedRegions.length === 0) {
        message.warning('请至少选择一个地区');
        return;
      }
      generateRelations();
    }
    setCurrentStep(prev => prev + 1);
  };

  const handlePrev = () => {
    setCurrentStep(prev => prev - 1);
  };

  // 初始化数据
  useEffect(() => {
    if (visible) {
      fetchUserList();
      fetchRegionTree();
    } else {
      resetState();
    }
  }, [visible, tenantId]);

  // 步骤配置
  const steps = [
    {
      title: '选择用户',
      icon: <UserOutlined />,
    },
    {
      title: '选择地区',
      icon: <EnvironmentOutlined />,
    },
    {
      title: '确认保存',
      icon: <CheckOutlined />,
    },
  ];

  return (
    <Modal
      title={`批量配置后勤人员 - ${insuranceCompanyName}`}
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <div style={{ padding: '20px 0' }}>
        <Steps current={currentStep} items={steps} style={{ marginBottom: 30 }} />

        {/* 步骤内容 */}
        <div style={{ minHeight: 400 }}>
          {/* 步骤1：选择用户 */}
          {currentStep === 0 && (
            <Card title="选择用户" bordered={false}>
              <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                请选择需要配置后勤地区的用户（支持多选）
              </Text>
              <List
                loading={userLoading}
                dataSource={userList}
                renderItem={(user) => (
                  <List.Item
                    key={user.id}
                    onClick={() => {
                      const isSelected = selectedUsers.some(u => u.id === user.id);
                      if (isSelected) {
                        setSelectedUsers(prev => prev.filter(u => u.id !== user.id));
                      } else {
                        setSelectedUsers(prev => [...prev, user]);
                      }
                    }}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedUsers.some(u => u.id === user.id) ? '#e6f7ff' : 'transparent',
                      borderRadius: '6px',
                      padding: '12px',
                      margin: '8px 0',
                      border: selectedUsers.some(u => u.id === user.id) ? '1px solid #1890ff' : '1px solid #f0f0f0',
                    }}
                  >
                    <List.Item.Meta
                      avatar={
                        user.wechatQrcode ? (
                          <img
                            src={user.wechatQrcode}
                            alt="微信二维码"
                            style={{
                              width: 40,
                              height: 40,
                              objectFit: 'cover',
                              borderRadius: '50%',
                              cursor: 'pointer',
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              const newWindow = window.open('', '_blank');
                              if (newWindow) {
                                newWindow.document.write(`
                                  <html>
                                    <head><title>微信二维码</title></head>
                                    <body style="margin:0;padding:20px;text-align:center;background:#f0f0f0;">
                                      <img src="${user.wechatQrcode}" style="max-width:100%;max-height:100%;" alt="微信二维码" />
                                    </body>
                                  </html>
                                `);
                              }
                            }}
                          />
                        ) : (
                          <Avatar icon={<UserOutlined />} />
                        )
                      }
                      title={user.realname}
                      description={`用户名: ${user.username} | 手机: ${user.phone || '未设置'}`}
                    />
                    {selectedUsers.some(u => u.id === user.id) && (
                      <CheckOutlined style={{ color: '#1890ff', fontSize: 16 }} />
                    )}
                  </List.Item>
                )}
              />
              {selectedUsers.length > 0 && (
                <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', borderRadius: 6 }}>
                  <Text strong>已选择 {selectedUsers.length} 个用户：</Text>
                  <div style={{ marginTop: 8 }}>
                    {selectedUsers.map(user => (
                      <Tag key={user.id} color="green" style={{ margin: '2px 4px 2px 0' }}>
                        {user.realname}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* 步骤2：选择地区 */}
          {currentStep === 1 && (
            <Card title="选择地区" bordered={false}>
              <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                请选择这些用户需要负责的地区（支持多选）
              </Text>
              <Tree
                checkable
                loading={regionLoading}
                treeData={regionTreeData}
                checkedKeys={selectedRegions}
                onCheck={(checkedKeys) => {
                  setSelectedRegions(checkedKeys as string[]);
                }}
                showLine
                defaultExpandAll
                height={300}
              />
              {selectedRegions.length > 0 && (
                <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', borderRadius: 6 }}>
                  <Text strong>已选择 {selectedRegions.length} 个地区：</Text>
                  <div style={{ marginTop: 8 }}>
                    {selectedRegions.map(regionCode => (
                      <Tag key={regionCode} color="blue" style={{ margin: '2px 4px 2px 0' }}>
                        {getRegionNameByCode(regionCode, regionTreeData)}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* 步骤3：确认保存 */}
          {currentStep === 2 && (
            <Card title="确认配置" bordered={false}>
              <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                请确认以下配置关系，确认无误后点击保存
              </Text>
              <div style={{ marginBottom: 16 }}>
                <Text strong>将创建 {relations.length} 条配置关系</Text>
              </div>
              <List
                dataSource={relations}
                renderItem={(relation, index) => (
                  <List.Item
                    key={`${relation.userId}-${relation.regionCode}`}
                    actions={[
                      <Button
                        key="delete"
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeRelation(relation.userId, relation.regionCode)}
                      >
                        删除
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={`${relation.userRealname} (${relation.userName})`}
                      description={`负责地区: ${relation.regionName}`}
                    />
                  </List.Item>
                )}
                style={{ maxHeight: 300, overflowY: 'auto' }}
              />
            </Card>
          )}
        </div>

        {/* 底部按钮 */}
        <Divider />
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>取消</Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>上一步</Button>
            )}
            {currentStep < 2 && (
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            )}
            {currentStep === 2 && (
              <Button type="primary" loading={saving} onClick={handleSave}>
                保存配置 ({relations.length}条)
              </Button>
            )}
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default BatchConfigModal;
