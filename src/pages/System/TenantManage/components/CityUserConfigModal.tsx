import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Space, Empty, message, Row, Col, Card, Typography, Tree, List, Avatar, Image } from 'antd';
import { PlusOutlined, DeleteOutlined, UserOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import type { DataNode } from 'antd/es/tree';
import { getCityList } from '@/services/system/common';
import { getTenantUserList } from '@/services/system/tenant';
import { saveCityUserConfig, getCityUserConfig, deleteCityUserConfig } from '../services/cityUserConfig';
import SelectUserModal from './SelectUserModal';
import type { TenantUser } from '../data';

const { Title, Text } = Typography;

interface CityUserConfigModalProps {
  visible: boolean;
  tenantId: string;
  tenantName: string;
  onCancel: () => void;
  onSuccess: () => void;
}

// 地区数据类型
interface Region {
  code: string;
  name: string;
  level: number;
  parentCode?: string;
  children?: Region[];
}

// 地区后勤人员数据类型
interface CityLogisticsStaff {
  id: string;
  userId: string;
  username: string;
  realname: string;
  avatar?: string;
  phone?: string;
  wechatQrcode?: string; // 添加微信二维码字段
  cityCode: string;
  cityName: string;
  status: number;
  createTime?: string;
  tenantId: string;
}

const CityUserConfigModal: React.FC<CityUserConfigModalProps> = ({
  visible,
  tenantId,
  tenantName,
  onCancel,
  onSuccess,
}) => {
  // 状态管理
  const [regionTreeData, setRegionTreeData] = useState<DataNode[]>([]);
  const [filteredRegionTreeData, setFilteredRegionTreeData] = useState<DataNode[]>([]);
  const [regionSearchText, setRegionSearchText] = useState<string>('');
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedRegionNames, setSelectedRegionNames] = useState<string[]>([]);
  const [staffList, setStaffList] = useState<CityLogisticsStaff[]>([]);
  const [staffLoading, setStaffLoading] = useState<boolean>(false);
  const [selectUserModalVisible, setSelectUserModalVisible] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchMode, setBatchMode] = useState<boolean>(false);
  const [allRegionCodes, setAllRegionCodes] = useState<string[]>([]);
  const actionRef = useRef<ActionType>();

  // 初始化加载数据
  useEffect(() => {
    if (visible && tenantId) {
      fetchRegionTree();
      // 重置状态
      setSelectedRegions([]);
      setSelectedRegionNames([]);
      setStaffList([]);
      setRegionTreeData([]);
      setBatchMode(false);
    }
  }, [visible, tenantId]);

  // 监听地区选择变化
  useEffect(() => {
    if (selectedRegions.length === 1 && !batchMode) {
      fetchStaffList();
    } else if (selectedRegions.length === 0) {
      setStaffList([]);
    }
  }, [selectedRegions, batchMode]);

  // 获取地区树数据
  const fetchRegionTree = async () => {
    try {
      const response = await getCityList();
      if (response.success) {
        const treeData = convertToTreeData(response.result || []);
        const allCodes = getAllRegionCodes(response.result || []);
        setRegionTreeData(treeData);
        setFilteredRegionTreeData(treeData);
        setAllRegionCodes(allCodes);
        setRegionSearchText('');
      } else {
        message.error(response.message || '获取地区数据失败');
      }
    } catch (error) {
      console.error('获取地区数据出错:', error);
      message.error('获取地区数据失败');
    }
  };

  // 转换地区数据为Tree组件需要的格式
  const convertToTreeData = (regions: Region[]): DataNode[] => {
    return regions.map(region => ({
      title: region.name,
      key: region.code,
      value: region.code,
      children: region.children ? convertToTreeData(region.children) : undefined,
    }));
  };

  // 获取所有地区编码（用于全选）
  const getAllRegionCodes = (regions: Region[]): string[] => {
    let codes: string[] = [];
    regions.forEach(region => {
      codes.push(region.code);
      if (region.children && region.children.length > 0) {
        codes = codes.concat(getAllRegionCodes(region.children));
      }
    });
    return codes;
  };

  // 地区搜索处理
  const handleRegionSearch = (searchText: string) => {
    setRegionSearchText(searchText);
    if (!searchText.trim()) {
      setFilteredRegionTreeData(regionTreeData);
      return;
    }

    const filteredData = filterTreeData(regionTreeData, searchText.toLowerCase());
    setFilteredRegionTreeData(filteredData);
  };

  // 递归过滤树形数据
  const filterTreeData = (treeData: DataNode[], searchText: string): DataNode[] => {
    return treeData.reduce((filtered: DataNode[], node) => {
      const title = (node.title as string).toLowerCase();
      const hasMatch = title.includes(searchText);

      let filteredChildren: DataNode[] = [];
      if (node.children) {
        filteredChildren = filterTreeData(node.children, searchText);
      }

      // 如果当前节点匹配或有子节点匹配，则包含此节点
      if (hasMatch || filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : node.children,
        });
      }

      return filtered;
    }, []);
  };

  // 获取地区后勤人员列表
  const fetchStaffList = async () => {
    if (selectedRegions.length !== 1 || !tenantId) return;

    const selectedRegion = selectedRegions[0];
    setStaffLoading(true);
    try {
      const response = await getCityUserConfig({
        tenantId: parseInt(tenantId),
        cityCode: selectedRegion,
      });

      if (response.success) {
        // 获取用户详细信息
        const staffRelations = response.result || [];
        if (staffRelations.length > 0) {
          // 获取用户详细信息
          const userIds = staffRelations.map((item: any) => item.userId);
          const userResponse = await getTenantUserList({
            pageNo: 1,
            pageSize: 1000,
            userIdList: userIds.join(',')
          });

          let userMap = new Map();
          if (userResponse.success && userResponse.result?.records) {
            userResponse.result.records.forEach((user: any) => {
              userMap.set(user.id, user);
            });
          }

          // 合并用户信息和关系信息
          const staffData = staffRelations.map((item: any) => {
            const userInfo = userMap.get(item.userId) || {};
            return {
              id: item.id,
              userId: item.userId,
              username: userInfo.username || item.userId,
              realname: userInfo.realname || userInfo.username || item.userId,
              phone: userInfo.phone || '-',
              wechatQrcode: userInfo.wechatQrcode || '', // 添加微信二维码字段
              cityCode: item.cityCode,
              cityName: selectedRegionNames[0] || '',
              status: 1,
              createTime: item.createTime,
              tenantId: item.tenantId,
            };
          });
          setStaffList(staffData);
        } else {
          setStaffList([]);
        }
      } else {
        message.error(response.message || '获取地区后勤人员列表失败');
      }
    } catch (error) {
      console.error('获取地区后勤人员列表出错:', error);
      message.error('获取地区后勤人员列表失败');
    } finally {
      setStaffLoading(false);
    }
  };

  // 处理地区选择
  const handleRegionSelect = (selectedKeys: React.Key[], info: any) => {
    if (batchMode) {
      // 批量模式下支持多选
      const codes = selectedKeys as string[];
      const names = codes.map(code => {
        const findNodeTitle = (nodes: DataNode[], targetKey: string): string => {
          for (const node of nodes) {
            if (node.key === targetKey) {
              return node.title as string;
            }
            if (node.children) {
              const found = findNodeTitle(node.children, targetKey);
              if (found) return found;
            }
          }
          return '';
        };
        return findNodeTitle(regionTreeData, code);
      }).filter(name => name);

      setSelectedRegions(codes);
      setSelectedRegionNames(names);
    } else {
      // 单选模式
      if (selectedKeys.length > 0) {
        const regionCode = selectedKeys[0] as string;
        const regionName = info.node.title;
        setSelectedRegions([regionCode]);
        setSelectedRegionNames([regionName]);
      } else {
        setSelectedRegions([]);
        setSelectedRegionNames([]);
      }
    }
  };

  // 处理全选地区
  const handleSelectAllRegions = () => {
    setBatchMode(true);
    setSelectedRegions([...allRegionCodes]);
    // 获取所有地区名称
    const getAllRegionNames = (nodes: DataNode[]): string[] => {
      let names: string[] = [];
      nodes.forEach(node => {
        names.push(node.title as string);
        if (node.children) {
          names = names.concat(getAllRegionNames(node.children));
        }
      });
      return names;
    };
    setSelectedRegionNames(getAllRegionNames(regionTreeData));
    setStaffList([]); // 清空人员列表
  };

  // 处理清空选择
  const handleClearSelection = () => {
    setBatchMode(false);
    setSelectedRegions([]);
    setSelectedRegionNames([]);
    setStaffList([]);
  };

  // 处理添加后勤人员
  const handleAddStaff = () => {
    if (selectedRegions.length === 0) {
      message.warning('请先选择地区');
      return;
    }
    setSelectUserModalVisible(true);
  };

  // 处理用户选择完成
  const handleUserSelected = async (selectedUsers: TenantUser[]) => {
    if (!selectedUsers || selectedUsers.length === 0) {
      return;
    }

    if (selectedRegions.length === 0) {
      message.error('请先选择地区');
      return;
    }

    try {
      // 如果是批量模式或选择了多个地区，为每个地区都配置这些用户
      const promises = selectedRegions.map(regionCode =>
        saveCityUserConfig({
          tenantId: parseInt(tenantId),
          cityCode: regionCode,
          userIds: selectedUsers.map(user => user.id),
        })
      );

      const results = await Promise.all(promises);
      const failedCount = results.filter(result => !result.success).length;

      if (failedCount === 0) {
        message.success(`成功为 ${selectedRegions.length} 个地区配置了后勤人员`);
        setSelectUserModalVisible(false);
        // 如果是单选模式，刷新列表
        if (selectedRegions.length === 1 && !batchMode) {
          fetchStaffList();
        }
        // 如果是批量模式，可以选择清空选择或保持选择状态
        if (batchMode) {
          message.info('批量配置完成，您可以继续选择其他地区或清空选择');
        }
      } else {
        message.error(`配置失败：${failedCount} 个地区配置失败`);
      }
    } catch (error) {
      console.error('添加地区后勤人员出错:', error);
      message.error('添加地区后勤人员失败');
    }
  };

  // 处理移除后勤人员
  const handleRemoveStaff = async (record: CityLogisticsStaff) => {
    try {
      const response = await deleteCityUserConfig({
        tenantId: parseInt(tenantId),
        cityCode: record.cityCode,
      });

      if (response.success) {
        message.success('移除地区后勤人员成功');
        fetchStaffList();
      } else {
        message.error(response.message || '移除地区后勤人员失败');
      }
    } catch (error) {
      console.error('移除地区后勤人员出错:', error);
      message.error('移除地区后勤人员失败');
    }
  };

  // 处理批量移除后勤人员
  const handleBatchRemove = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要移除的后勤人员');
      return;
    }

    if (selectedRegions.length !== 1) {
      message.warning('请选择单个地区进行移除操作');
      return;
    }

    try {
      const response = await deleteCityUserConfig({
        tenantId: parseInt(tenantId),
        cityCode: selectedRegions[0],
      });

      if (response.success) {
        message.success('批量移除地区后勤人员成功');
        setSelectedRowKeys([]);
        fetchStaffList();
      } else {
        message.error(response.message || '批量移除地区后勤人员失败');
      }
    } catch (error) {
      console.error('批量移除地区后勤人员出错:', error);
      message.error('批量移除地区后勤人员失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '姓名',
      dataIndex: 'realname',
      width: 120,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '微信二维码',
      dataIndex: 'wechatQrcode',
      width: 120,
      render: (text: string) => {
        if (!text) {
          return <span style={{ color: '#999' }}>暂无</span>;
        }
        return (
          <Image
            width={60}
            height={60}
            src={text}
            alt="微信二维码"
            style={{ borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (status: number) => {
        if (status === 1) {
          return <span style={{ color: '#52c41a' }}>启用</span>;
        } else {
          return <span style={{ color: '#ff4d4f' }}>禁用</span>;
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_: any, record: any) => [
        <a key="remove" onClick={() => handleRemoveStaff(record)}>
          移除
        </a>,
      ],
    },
  ];

  return (
    <Modal
      title={`${tenantName || '租户'}地区后勤配置`}
      open={visible}
      onCancel={onCancel}
      width="90%"
      style={{ top: 20 }}
      bodyStyle={{ height: 'calc(100vh - 150px)', padding: '12px' }}
      footer={null}
    >
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 左侧：地区树 */}
        <Col span={8} style={{ height: '100%', overflowY: 'auto' }}>
          <Card
            title="业务地区"
            bordered={false}
            style={{ height: '100%' }}
            extra={
              <Space>
                <Button
                  size="small"
                  type="primary"
                  onClick={handleSelectAllRegions}
                  disabled={allRegionCodes.length === 0}
                >
                  全选
                </Button>
                <Button
                  size="small"
                  onClick={handleClearSelection}
                  disabled={selectedRegions.length === 0}
                >
                  清空
                </Button>
              </Space>
            }
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <EnvironmentOutlined style={{ fontSize: 18, color: '#1890ff', marginRight: 8 }} />
              <Text strong>
                {batchMode ? '批量选择模式' : '单选模式'}
                {selectedRegions.length > 0 && (
                  <span style={{ color: '#1890ff', marginLeft: 8 }}>
                    (已选 {selectedRegions.length} 个地区)
                  </span>
                )}
              </Text>
            </div>

            {/* 显示已选择的地区 */}
            {selectedRegions.length > 0 && (
              <div style={{ marginBottom: 12, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
                <Text strong style={{ color: '#52c41a' }}>已选择地区：</Text>
                <div style={{ marginTop: 4, maxHeight: 60, overflowY: 'auto' }}>
                  {selectedRegionNames.map((name, index) => (
                    <span key={index} style={{
                      display: 'inline-block',
                      margin: '2px 4px 2px 0',
                      padding: '2px 6px',
                      backgroundColor: '#52c41a',
                      color: 'white',
                      borderRadius: 2,
                      fontSize: 12
                    }}>
                      {name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {regionTreeData.length === 0 ? (
              <Empty
                description="暂无地区数据"
                style={{ marginTop: 50 }}
              />
            ) : (
              <div>
                {/* 地区搜索框 */}
                <div style={{ marginBottom: 12 }}>
                  <input
                    type="text"
                    placeholder="搜索地区..."
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                    }}
                    onChange={(e) => handleRegionSearch(e.target.value)}
                  />
                </div>
                <Tree
                  treeData={filteredRegionTreeData}
                  onSelect={handleRegionSelect}
                  selectedKeys={selectedRegions}
                  multiple={batchMode}
                  checkable={batchMode}
                  checkedKeys={batchMode ? selectedRegions : []}
                  onCheck={batchMode ? (checkedKeys) => {
                    const keys = Array.isArray(checkedKeys) ? checkedKeys as string[] : checkedKeys.checked as string[];
                    const names = keys.map(code => {
                      const findNodeTitle = (nodes: DataNode[], targetKey: string): string => {
                        for (const node of nodes) {
                          if (node.key === targetKey) {
                            return node.title as string;
                          }
                          if (node.children) {
                            const found = findNodeTitle(node.children, targetKey);
                            if (found) return found;
                          }
                        }
                        return '';
                      };
                      return findNodeTitle(regionTreeData, code);
                    }).filter(name => name);
                    setSelectedRegions(keys);
                    setSelectedRegionNames(names);
                  } : undefined}
                  showLine
                  showIcon={false}
                  defaultExpandAll
                  height={300}
                />
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：地区后勤人员列表 */}
        <Col span={16} style={{ height: '100%' }}>
          <Card
            title={
              batchMode
                ? `批量配置模式 (已选 ${selectedRegions.length} 个地区)`
                : `${selectedRegionNames[0] || ''}地区后勤人员列表`
            }
            bordered={false}
            style={{ height: '100%' }}
            extra={
              <Space>
                {!batchMode && selectedRegions.length === 1 && (
                  <Button
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchRemove}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量移除
                  </Button>
                )}
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddStaff}
                  disabled={selectedRegions.length === 0}
                >
                  {batchMode ? '批量添加后勤人员' : '添加后勤人员'}
                </Button>
              </Space>
            }
          >
            {selectedRegions.length === 0 ? (
              <Empty
                description="请先选择左侧的地区"
                style={{ marginTop: 100 }}
              />
            ) : batchMode ? (
              <div style={{ padding: '40px 20px', textAlign: 'center' }}>
                <div style={{ fontSize: 16, marginBottom: 20 }}>
                  <UserOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                  <div>批量配置模式</div>
                </div>
                <div style={{ marginBottom: 20 }}>
                  <Text>您已选择了 <Text strong style={{ color: '#1890ff' }}>{selectedRegions.length}</Text> 个地区</Text>
                </div>
                <div style={{ marginBottom: 20, textAlign: 'left', maxHeight: 200, overflowY: 'auto', border: '1px solid #f0f0f0', padding: 12, borderRadius: 4 }}>
                  {selectedRegionNames.map((name, index) => (
                    <div key={index} style={{ padding: '4px 0', borderBottom: index < selectedRegionNames.length - 1 ? '1px solid #f0f0f0' : 'none' }}>
                      <EnvironmentOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                      {name}
                    </div>
                  ))}
                </div>
                <Text type="secondary">
                  点击"批量添加后勤人员"为所有选中的地区配置相同的后勤人员
                </Text>
              </div>
            ) : (
              <ProTable
                actionRef={actionRef}
                rowKey="id"
                search={false}
                options={false}
                loading={staffLoading}
                dataSource={staffList}
                columns={columns}
                rowSelection={{
                  selectedRowKeys,
                  onChange: (keys) => setSelectedRowKeys(keys),
                }}
                pagination={false}
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* 选择用户弹窗 */}
      <SelectUserModal
        visible={selectUserModalVisible}
        tenantId={tenantId}
        onCancel={() => setSelectUserModalVisible(false)}
        onOk={handleUserSelected}
      />
    </Modal>
  );
};

export default CityUserConfigModal;
