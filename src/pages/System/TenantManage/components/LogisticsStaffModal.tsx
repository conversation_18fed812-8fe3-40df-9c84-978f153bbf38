import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Space, Empty, message, Row, Col, Card, Typography, Divider, List, Avatar, Tree } from 'antd';
import { PlusOutlined, DeleteOutlined, UserOutlined, EnvironmentOutlined, BankOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { getTenantUserList } from '@/services/system/tenant';
import { tenantUserColumns } from '../data';
import type { TenantUser } from '../data';
import SelectUserModal from './SelectUserModal';
import BatchConfigModal from './BatchConfigModal';
import type { DataNode } from 'antd/es/tree';

// OSS图片处理工具
const getImageUrl = (iconImg?: string): string => {
  if (!iconImg) return '';
  if (iconImg.startsWith('http')) return iconImg;
  // 这里需要替换为您的实际OSS域名
  return `https://your-oss-domain.com/${iconImg}`;
};


const { Title, Text } = Typography;

// 保险公司数据类型
interface InsuranceCompany {
  id: string;
  name: string;
  iconImg?: string;
  enableType: string;
  createTime: string;
}

// 地区数据类型
interface Region {
  code: string;
  name: string;
  level: number;
  parentCode?: string;
  children?: Region[];
}

// 后勤人员数据类型
interface LogisticsStaff {
  id: string;
  userId: string;
  username: string;
  realname: string;
  avatar?: string;
  phone?: string;
  regionCode: string;
  regionName: string;
  status: number;
  createTime?: string;
  tenantId: string;
  insuranceCompanyId: string;
}

interface LogisticsStaffModalProps {
  visible: boolean;
  tenantId: string;
  tenantName?: string;
  onCancel: () => void;
}

/**
 * 后勤人员配置弹框
 */
const LogisticsStaffModal: React.FC<LogisticsStaffModalProps> = ({
  visible,
  tenantId,
  tenantName = '',
  onCancel,
}) => {
  // 状态管理
  const [insuranceCompanies, setInsuranceCompanies] = useState<InsuranceCompany[]>([]);
  const [selectedInsuranceCompany, setSelectedInsuranceCompany] = useState<InsuranceCompany | null>(null);
  const [regionTreeData, setRegionTreeData] = useState<DataNode[]>([]);
  const [filteredRegionTreeData, setFilteredRegionTreeData] = useState<DataNode[]>([]);
  const [regionSearchText, setRegionSearchText] = useState<string>('');
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [selectedRegionName, setSelectedRegionName] = useState<string>('');
  const [staffList, setStaffList] = useState<LogisticsStaff[]>([]);
  const [staffLoading, setStaffLoading] = useState<boolean>(false);
  const [selectUserModalVisible, setSelectUserModalVisible] = useState<boolean>(false);
  const [batchConfigModalVisible, setBatchConfigModalVisible] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const actionRef = useRef<ActionType>();

  // 初始化加载数据
  useEffect(() => {
    if (visible && tenantId) {
      fetchInsuranceCompanies();
      // 重置状态
      setSelectedInsuranceCompany(null);
      setSelectedRegion('');
      setSelectedRegionName('');
      setStaffList([]);
      setRegionTreeData([]);
    }
  }, [visible, tenantId]);

  // 监听保险公司选择变化
  useEffect(() => {
    if (selectedInsuranceCompany) {
      fetchRegionTree();
      // 重置地区和人员选择
      setSelectedRegion('');
      setSelectedRegionName('');
      setStaffList([]);
    }
  }, [selectedInsuranceCompany]);

  // 监听地区选择变化
  useEffect(() => {
    if (selectedRegion && selectedInsuranceCompany) {
      fetchStaffList();
    }
  }, [selectedRegion, selectedInsuranceCompany]);

  // 获取租户关联的保险公司列表
  const fetchInsuranceCompanies = async () => {
    try {
      const response = await request('/sys/wlTenantInsuranceRel/listByTenantId', {
        method: 'GET',
        params: { tenantId },
      });

      if (response.success) {
        const relations = response.result || [];
        // 获取保险公司详细信息
        const companyPromises = relations.map((rel: any) =>
          request('/corp/pdCompany/queryById', {
            method: 'GET',
            params: { id: rel.insuranceCompanyId },
          })
        );

        const companyResponses = await Promise.all(companyPromises);
        const companies = companyResponses
          .filter(res => res.success)
          .map(res => res.result);

        setInsuranceCompanies(companies);
      } else {
        message.error(response.message || '获取保险公司列表失败');
      }
    } catch (error) {
      console.error('获取保险公司列表出错:', error);
      message.error('获取保险公司列表失败');
    }
  };

  // 获取地区树数据
  const fetchRegionTree = async () => {
    try {
      const response = await request('/wechat/eaRegion/getCityTree', {
        method: 'GET',
      });

      if (response.success) {
        const treeData = convertToTreeData(response.result || []);
        setRegionTreeData(treeData);
        setFilteredRegionTreeData(treeData);
        setRegionSearchText('');
      } else {
        message.error(response.message || '获取地区数据失败');
      }
    } catch (error) {
      console.error('获取地区数据出错:', error);
      message.error('获取地区数据失败');
    }
  };

  // 转换地区数据为Tree组件需要的格式
  const convertToTreeData = (regions: Region[]): DataNode[] => {
    return regions.map(region => ({
      title: region.name,
      key: region.code,
      value: region.code,
      children: region.children ? convertToTreeData(region.children) : undefined,
    }));
  };

  // 地区搜索处理
  const handleRegionSearch = (searchText: string) => {
    setRegionSearchText(searchText);
    if (!searchText.trim()) {
      setFilteredRegionTreeData(regionTreeData);
      return;
    }

    const filteredData = filterTreeData(regionTreeData, searchText.toLowerCase());
    setFilteredRegionTreeData(filteredData);
  };

  // 递归过滤树形数据
  const filterTreeData = (treeData: DataNode[], searchText: string): DataNode[] => {
    return treeData.reduce((filtered: DataNode[], node) => {
      const title = (node.title as string).toLowerCase();
      const hasMatch = title.includes(searchText);

      let filteredChildren: DataNode[] = [];
      if (node.children) {
        filteredChildren = filterTreeData(node.children, searchText);
      }

      // 如果当前节点匹配或有子节点匹配，则包含此节点
      if (hasMatch || filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : node.children,
        });
      }

      return filtered;
    }, []);
  };

  // 获取后勤人员列表
  const fetchStaffList = async () => {
    if (!selectedRegion || !selectedInsuranceCompany || !tenantId) return;

    setStaffLoading(true);
    try {
      const response = await request('/sys/tenant/logistics/list', {
        method: 'GET',
        params: {
          userTenantId: tenantId,
          insuranceCompanyId: selectedInsuranceCompany.id,
          regionCode: selectedRegion,
        },
      });

      if (response.success) {
        setStaffList(response.result || []);
      } else {
        message.error(response.message || '获取后勤人员列表失败');
      }
    } catch (error) {
      console.error('获取后勤人员列表出错:', error);
      message.error('获取后勤人员列表失败');
    } finally {
      setStaffLoading(false);
    }
  };

  // 处理保险公司选择
  const handleInsuranceCompanySelect = (company: InsuranceCompany) => {
    setSelectedInsuranceCompany(company);
  };

  // 处理地区选择
  const handleRegionSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0) {
      const regionCode = selectedKeys[0] as string;
      const regionName = info.node.title;
      setSelectedRegion(regionCode);
      setSelectedRegionName(regionName);
    }
  };

  // 处理添加后勤人员（单个地区）
  const handleAddStaff = () => {
    if (!selectedInsuranceCompany) {
      message.warning('请先选择保险公司');
      return;
    }
    if (!selectedRegion) {
      message.warning('请先选择地区');
      return;
    }
    setSelectUserModalVisible(true);
  };

  // 处理批量配置
  const handleBatchConfig = () => {
    if (!selectedInsuranceCompany) {
      message.warning('请先选择保险公司');
      return;
    }
    setBatchConfigModalVisible(true);
  };

  // 批量配置成功回调
  const handleBatchConfigSuccess = () => {
    fetchStaffList(); // 刷新列表
  };

  // 处理用户选择完成
  const handleUserSelected = async (selectedUsers: TenantUser[]) => {
    if (!selectedUsers || selectedUsers.length === 0) {
      return;
    }

    if (!selectedInsuranceCompany || !selectedRegion) {
      message.error('请先选择保险公司和地区');
      return;
    }

    try {
      const response = await request('/sys/tenant/logistics/add', {
        method: 'POST',
        params: {
          userTenantId: tenantId,
          insuranceCompanyId: selectedInsuranceCompany.id,
          regionCode: selectedRegion,
          userIds: selectedUsers.map(user => user.id).join(','),
        },
      });

      if (response.success) {
        message.success('添加后勤人员成功');
        setSelectUserModalVisible(false);
        fetchStaffList();
      } else {
        message.error(response.message || '添加后勤人员失败');
      }
    } catch (error) {
      console.error('添加后勤人员出错:', error);
      message.error('添加后勤人员失败');
    }
  };

  // 处理移除后勤人员
  const handleRemoveStaff = async (id: string) => {
    try {
      const response = await request('/sys/tenant/logistics/deleteById', {
        method: 'DELETE',
        params: { id },
      });

      if (response.success) {
        message.success('移除后勤人员成功');
        fetchStaffList();
      } else {
        message.error(response.message || '移除后勤人员失败');
      }
    } catch (error) {
      console.error('移除后勤人员出错:', error);
      message.error('移除后勤人员失败');
    }
  };

  // 处理批量移除后勤人员
  const handleBatchRemove = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要移除的后勤人员');
      return;
    }

    try {
      const response = await request('/sys/tenant/logistics/batchDelete', {
        method: 'DELETE',
        params: {
          ids: selectedRowKeys.join(','),
        },
      });

      if (response.success) {
        message.success('批量移除后勤人员成功');
        setSelectedRowKeys([]);
        fetchStaffList();
      } else {
        message.error(response.message || '批量移除后勤人员失败');
      }
    } catch (error) {
      console.error('批量移除后勤人员出错:', error);
      message.error('批量移除后勤人员失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '姓名',
      dataIndex: 'realname',
      width: 120,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '微信二维码',
      dataIndex: 'wechatQrcode',
      width: 120,
      render: (_: any, record: any) => {
        if (record.wechatQrcode) {
          return (
            <img
              src={record.wechatQrcode}
              alt="微信二维码"
              style={{
                width: 60,
                height: 60,
                objectFit: 'cover',
                cursor: 'pointer',
              }}
              onClick={() => {
                // 点击放大显示
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                  newWindow.document.write(`
                    <html>
                      <head><title>微信二维码</title></head>
                      <body style="margin:0;padding:20px;text-align:center;background:#f0f0f0;">
                        <img src="${record.wechatQrcode}" style="max-width:100%;max-height:100%;" alt="微信二维码" />
                      </body>
                    </html>
                  `);
                }
              }}
            />
          );
        }
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      valueEnum: {
        0: { text: '禁用', status: 'error' },
        1: { text: '启用', status: 'success' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_: any, record: any) => [
        <a key="remove" onClick={() => handleRemoveStaff(record.id)}>
          移除
        </a>,
      ],
    },
  ];

  return (
    <Modal
      title={`${tenantName || '租户'}后勤人员配置`}
      open={visible}
      onCancel={onCancel}
      width="100%"
      style={{ top: 0 }}
      bodyStyle={{ height: 'calc(100vh - 108px)', padding: '12px' }}
      footer={null}
    >
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 左侧：保险公司列表 */}
        <Col span={6} style={{ height: '100%', overflowY: 'auto' }}>
          <Card title="保险公司" bordered={false} style={{ height: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <BankOutlined style={{ fontSize: 18, color: '#1890ff', marginRight: 8 }} />
              <Text strong>选择保险公司</Text>
            </div>
            {insuranceCompanies.length === 0 ? (
              <Empty
                description="暂无关联的保险公司"
                style={{ marginTop: 50 }}
              />
            ) : (
              <List
                dataSource={insuranceCompanies}
                renderItem={(company) => (
                  <List.Item
                    key={company.id}
                    onClick={() => handleInsuranceCompanySelect(company)}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedInsuranceCompany?.id === company.id ? '#e6f7ff' : 'transparent',
                      borderRadius: '6px',
                      padding: '8px 12px',
                      marginBottom: '8px',
                      border: selectedInsuranceCompany?.id === company.id ? '1px solid #1890ff' : '1px solid transparent',
                    }}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={getImageUrl(company.iconImg)}
                          icon={<BankOutlined />}
                          size={40}
                        />
                      }
                      title={company.name}
                      description={`状态: ${company.enableType === 'Y' ? '启用' : '禁用'}`}
                    />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>

        {/* 中间：地区树 */}
        <Col span={6} style={{ height: '100%', overflowY: 'auto' }}>
          <Card title="业务地区" bordered={false} style={{ height: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <EnvironmentOutlined style={{ fontSize: 18, color: '#1890ff', marginRight: 8 }} />
              <Text strong>选择业务地区</Text>
            </div>
            {!selectedInsuranceCompany ? (
              <div style={{ textAlign: 'center', marginTop: 50 }}>
                <Empty
                  description={
                    <div>
                      <Text type="secondary">请先选择左侧的保险公司</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        选择保险公司后，将显示对应的业务地区
                      </Text>
                    </div>
                  }
                />
              </div>
            ) : regionTreeData.length === 0 ? (
              <Empty
                description="暂无地区数据"
                style={{ marginTop: 50 }}
              />
            ) : (
              <div>
                {/* 地区搜索框 */}
                <div style={{ marginBottom: 12 }}>
                  <input
                    type="text"
                    placeholder="搜索地区..."
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                    }}
                    onChange={(e) => handleRegionSearch(e.target.value)}
                  />
                </div>
                <Tree
                  treeData={filteredRegionTreeData}
                  onSelect={handleRegionSelect}
                  selectedKeys={selectedRegion ? [selectedRegion] : []}
                  showLine
                  showIcon={false}
                  defaultExpandAll
                  height={400}
                />
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：后勤人员列表 */}
        <Col span={12} style={{ height: '100%' }}>
          <Card
            title={`${selectedRegionName || ''}后勤人员列表`}
            bordered={false}
            style={{ height: '100%' }}
            extra={
              <Space>
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchRemove}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量移除
                </Button>
                <Button
                  type="default"
                  icon={<PlusOutlined />}
                  onClick={handleBatchConfig}
                  disabled={!selectedInsuranceCompany}
                >
                  批量配置
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddStaff}
                  disabled={!selectedInsuranceCompany || !selectedRegion}
                >
                  添加后勤人员
                </Button>
              </Space>
            }
          >
            {!selectedInsuranceCompany || !selectedRegion ? (
              <Empty
                description="请先选择保险公司和地区"
                style={{ marginTop: 100 }}
              />
            ) : (
              <ProTable
                actionRef={actionRef}
                rowKey="id"
                search={false}
                options={false}
                loading={staffLoading}
                dataSource={staffList}
                columns={columns}
                rowSelection={{
                  selectedRowKeys,
                  onChange: (keys) => setSelectedRowKeys(keys),
                }}
                pagination={false}
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* 选择用户弹窗 */}
      <SelectUserModal
        visible={selectUserModalVisible}
        tenantId={tenantId}
        onCancel={() => setSelectUserModalVisible(false)}
        onOk={handleUserSelected}
      />

      {/* 批量配置弹窗 */}
      <BatchConfigModal
        visible={batchConfigModalVisible}
        tenantId={tenantId}
        tenantName={tenantName}
        insuranceCompanyId={selectedInsuranceCompany?.id || ''}
        insuranceCompanyName={selectedInsuranceCompany?.name || ''}
        onCancel={() => setBatchConfigModalVisible(false)}
        onSuccess={handleBatchConfigSuccess}
      />
    </Modal>
  );
};

export default LogisticsStaffModal;
