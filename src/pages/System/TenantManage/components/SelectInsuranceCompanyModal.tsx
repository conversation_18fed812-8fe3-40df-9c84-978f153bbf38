import React, { useState, useRef } from 'react';
import { Modal, message, Button, Avatar } from 'antd';
import { PlusOutlined, BankOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { request } from '@umijs/max';
import OSS_CONFIG from '@/utils/ossConfig';

interface InsuranceCompany {
  id: string;
  name: string;
  iconImg: string;
  enableType: string;
  createTime: string;
}

interface SelectInsuranceCompanyModalProps {
  visible: boolean;
  tenantId: string;
  tenantName?: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const SelectInsuranceCompanyModal: React.FC<SelectInsuranceCompanyModalProps> = ({
  visible,
  tenantId,
  tenantName = '',
  onCancel,
  onSuccess,
}) => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<InsuranceCompany[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 获取保险公司列表
  const fetchInsuranceCompanies = async (params: any) => {
    try {
      const { current, pageSize, ...rest } = params;
      const response = await request('/corp/pdCompany/list', {
        method: 'GET',
        params: {
          pageNo: current,
          pageSize,
          ...rest,
        },
      });

      if (response.success) {
        return {
          data: response.result.records || [],
          success: true,
          total: response.result.total || 0,
        };
      }
      return {
        data: [],
        success: false,
        total: 0,
      };
    } catch (error) {
      console.error('获取保险公司列表失败:', error);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 表格列配置
  const columns: ProColumns<InsuranceCompany>[] = [
    {
      title: '图标',
      dataIndex: 'iconImg',
      key: 'iconImg',
      width: 80,
      search: false,
      render: (_, record) => {
        const imageUrl = OSS_CONFIG.getThumbnailUrl(record.iconImg, 40, 40);

        return (
          <Avatar
            size={40}
            src={imageUrl}
            icon={<BankOutlined />}
            style={{
              backgroundColor: imageUrl ? 'transparent' : '#f5f5f5',
              border: '1px solid #f0f0f0'
            }}
          />
        );
      },
    },
    {
      title: '公司名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'enableType',
      key: 'enableType',
      width: 80,
      search: false,
      valueEnum: {
        Y: { text: '启用', status: 'Success' },
        N: { text: '禁用', status: 'Error' },
      },
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: InsuranceCompany[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
    getCheckboxProps: (record: InsuranceCompany) => ({
      disabled: record.enableType === 'N', // 禁用状态的公司不能选择
    }),
  };

  // 处理确认选择
  const handleConfirm = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择至少一个保险公司');
      return;
    }

    setSubmitting(true);
    try {
      // 调用添加租户保险公司关系的接口
      const response = await request('/sys/wlTenantInsuranceRel/addRelation', {
        method: 'POST',
        params: {
          tenantId,
          insuranceCompanyIds: selectedRowKeys.join(','),
        },
      });

      if (response.success) {
        message.success(`成功为租户"${tenantName}"添加了${selectedRowKeys.length}个保险公司`);
        onSuccess();
      } else {
        message.error(response.message || '添加保险公司关系失败');
      }
    } catch (error) {
      console.error('添加保险公司关系失败:', error);
      message.error('添加保险公司关系失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理弹窗关闭
  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    onCancel();
  };

  return (
    <Modal
      title={`为租户"${tenantName}"添加保险公司`}
      open={visible}
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={submitting}
      width={800}
      destroyOnClose
      okText="确认添加"
      cancelText="取消"
    >
      <ProTable<InsuranceCompany>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 80,
          collapsed: false,
          collapseRender: false,
          searchText: '搜索',
          resetText: '重置',
        }}
        request={fetchInsuranceCompanies}
        columns={columns}
        rowSelection={rowSelection}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
        }}
        scroll={{ y: 400 }}
        size="small"
        options={false}
        toolBarRender={false}
      />

      {selectedRowKeys.length > 0 && (
        <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#f6f6f6', borderRadius: '6px' }}>
          <span style={{ color: '#666' }}>
            已选择 <strong style={{ color: '#1890ff' }}>{selectedRowKeys.length}</strong> 个保险公司：
          </span>
          <div style={{ marginTop: 8 }}>
            {selectedRows.map((row, index) => (
              <span key={row.id} style={{ marginRight: 8 }}>
                {index > 0 && '、'}{row.name}
              </span>
            ))}
          </div>
        </div>
      )}
    </Modal>
  );
};

export default SelectInsuranceCompanyModal;
