import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { getTenantUserList } from '@/services/system/tenant';
import { tenantUserColumns } from '../data';
import type { TenantUser } from '../data';

interface SelectUserModalProps {
  visible: boolean;
  tenantId: string;
  onCancel: () => void;
  onOk: (selectedUsers: TenantUser[]) => void;
}

const SelectUserModal: React.FC<SelectUserModalProps> = ({
  visible,
  tenantId,
  onCancel,
  onOk,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TenantUser[]>([]);
  const actionRef = useRef<ActionType>();

  // 表格列配置
  const columns = [
    ...tenantUserColumns.filter(col =>
      ['username', 'realname', 'phone', 'email'].includes(col.dataIndex as string)
    ),
    {
      title: '微信二维码',
      dataIndex: 'wechatQrcode',
      width: 120,
      search: false,
      render: (_: any, record: TenantUser) => {
        if (record.wechatQrcode) {
          return (
            <img
              src={record.wechatQrcode}
              alt="微信二维码"
              style={{
                width: 60,
                height: 60,
                objectFit: 'cover',
                cursor: 'pointer',
              }}
              onClick={() => {
                // 点击放大显示
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                  newWindow.document.write(`
                    <html>
                      <head><title>微信二维码</title></head>
                      <body style="margin:0;padding:20px;text-align:center;background:#f0f0f0;">
                        <img src="${record.wechatQrcode}" style="max-width:100%;max-height:100%;" alt="微信二维码" />
                      </body>
                    </html>
                  `);
                }
              }}
            />
          );
        }
        return '-';
      },
    },
    ...tenantUserColumns.filter(col =>
      ['status'].includes(col.dataIndex as string)
    ),
  ];

  // 处理确认选择
  const handleOk = () => {
    onOk(selectedRows);
  };

  return (
    <Modal
      title="选择用户"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          onClick={handleOk}
          disabled={selectedRowKeys.length === 0}
        >
          确定
        </Button>,
      ]}
      destroyOnClose
    >
      <ProTable<TenantUser>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        options={false}
        request={async (params) => {
          // 处理分页参数
          const { current, pageSize, ...rest } = params;
          const response = await getTenantUserList({
            pageNo: current,
            pageSize,
            ...rest,
          });

          if (response.success) {
            return {
              data: response.result.records || [],
              success: true,
              total: response.result.total || 0,
            };
          }
          return {
            data: [],
            success: false,
            total: 0,
          };
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        scroll={{ y: 400 }}
      />
    </Modal>
  );
};

export default SelectUserModal;
