import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import { invitationUserJoin } from '@/services/system/tenant';

interface TenantInviteUserModalProps {
  visible: boolean;
  tenantId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const TenantInviteUserModal: React.FC<TenantInviteUserModalProps> = ({
  visible,
  tenantId,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = React.useState<boolean>(false);

  // 当弹窗显示时重置表单
  React.useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 添加租户ID
      const params = {
        phone: values.phone,
        tenantId,
      };

      // 调用API发送邀请
      const response = await invitationUserJoin(params);

      if (response.success) {
        message.success('邀请发送成功');
        onSuccess();
      } else {
        message.error(response.message || '邀请发送失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title="邀请用户加入租户"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={submitting}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误' },
          ]}
        >
          <Input placeholder="请输入用户手机号" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TenantInviteUserModal;
