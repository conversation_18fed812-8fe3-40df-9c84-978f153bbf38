import React, { useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Radio, Upload, message, Divider, Typography, Button, Switch, Tooltip } from 'antd';
import { UploadOutlined, InfoCircleOutlined, BankOutlined, TeamOutlined, ContactsOutlined, FileTextOutlined, EnvironmentOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import type { Tenant } from '../data';
import { addTenant, editTenant, getTenantBusinessCities, saveTenantBusinessCities, getTenantBusinessTypes, saveTenantBusinessTypes } from '@/services/system/tenant';
import { FormBusinessCitySelector } from '@/components/BusinessCitySelector';
import { FormBusinessTypeSelector } from '@/components/BusinessTypeSelector';


const { TextArea } = Input;
const { Title } = Typography;

interface TenantModalProps {
  visible: boolean;
  title: string;
  onCancel: () => void;
  onSuccess: () => void;
  values?: Partial<Tenant>;
}

const TenantModal: React.FC<TenantModalProps> = ({
  visible,
  title,
  onCancel,
  onSuccess,
  values = {},
}) => {
  const [form] = Form.useForm();
  const isUpdate = !!(values && values.id);

  // 当弹窗显示或values变化时，设置表单值
  useEffect(() => {
    console.log('TenantModal useEffect 触发, visible:', visible, 'values:', values);

    if (visible) {
      form.resetFields();
      console.log('表单已重置');

      if (values && values.id) {
        console.log('有效的租户数据，ID:', values.id);

        // 处理日期字段
        const formValues = { ...values };
        console.log('处理前的表单值:', formValues);

        // 注册时间现在是文本输入框，不需要特殊处理
        if (formValues.regTime) {
          console.log('注册时间字段:', formValues.regTime);
        }

        // 确保数值类型字段正确处理
        if (formValues.status !== undefined) {
          if (typeof formValues.status === 'string') {
            formValues.status = parseInt(formValues.status, 10) || 1;
          }
          console.log('状态字段处理后:', formValues.status);
        } else {
          formValues.status = 1; // 默认值
        }

        if (formValues.score !== undefined) {
          if (typeof formValues.score === 'string') {
            formValues.score = parseFloat(formValues.score) || 0;
          }
          console.log('评分字段处理后:', formValues.score);
        }

        if (formValues.clickNum !== undefined) {
          if (typeof formValues.clickNum === 'string') {
            formValues.clickNum = parseInt(formValues.clickNum, 10) || 0;
          }
          console.log('点击数字段处理后:', formValues.clickNum);
        }

        // 加载业务城市和业务类型数据
        const loadBusinessData = async () => {
          try {
            // 加载业务城市数据
            console.log('开始加载租户业务城市数据, tenantId:', values.id);
            const cityResponse = await getTenantBusinessCities(values.id);
            if (cityResponse.success && cityResponse.result) {
              console.log('业务城市数据加载成功:', cityResponse.result);
              formValues.businessCities = cityResponse.result;
            } else {
              console.log('未找到业务城市数据或加载失败');
              formValues.businessCities = [];
            }

            // 加载业务类型数据
            console.log('开始加载租户业务类型数据, tenantId:', values.id);
            const typeResponse = await getTenantBusinessTypes(values.id);
            if (typeResponse.success && typeResponse.result) {
              console.log('业务类型数据加载成功:', typeResponse.result);
              formValues.businessTypes = typeResponse.result;
            } else {
              console.log('未找到业务类型数据或加载失败');
              formValues.businessTypes = [];
            }
          } catch (error) {
            console.error('加载业务数据失败:', error);
            formValues.businessCities = [];
            formValues.businessTypes = [];
          }

          console.log('最终设置的表单值:', formValues);
          form.setFieldsValue(formValues);
          console.log('表单值已设置');
        };

        loadBusinessData();
      } else {
        console.log('没有有效的租户数据或ID');
      }
    }
  }, [visible, values, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      console.log('开始提交表单');
      const formValues = await form.validateFields();
      console.log('表单验证通过，值:', formValues);

      // 注册时间现在是文本输入框，不需要格式化处理
      if (formValues.regTime) {
        console.log('注册时间字段:', formValues.regTime);
      }

      // 确保ID字段存在
      if (isUpdate && !formValues.id && values.id) {
        formValues.id = values.id;
        console.log('添加ID字段:', formValues.id);
      }

      // 提取业务城市和业务类型数据，不包含在租户基本信息中
      const businessCities = formValues.businessCities || [];
      const businessTypes = formValues.businessTypes || [];
      delete formValues.businessCities; // 从租户数据中移除，单独处理
      delete formValues.businessTypes; // 从租户数据中移除，单独处理

      console.log('最终提交的租户数据:', formValues);
      console.log('业务城市数据:', businessCities);
      console.log('业务类型数据:', businessTypes);

      // 调用API保存租户基本数据
      const submitFunc = isUpdate ? editTenant : addTenant;
      console.log('调用API:', isUpdate ? 'editTenant' : 'addTenant');

      const response = await submitFunc(formValues);
      console.log('租户API返回结果:', response);

      if (response.success) {
        // 租户保存成功后，保存业务城市和业务类型数据
        const tenantId = isUpdate ? formValues.id : response.result?.id || response.result;

        // 保存业务城市数据
        if (tenantId && businessCities.length > 0) {
          try {
            console.log('开始保存业务城市数据, tenantId:', tenantId, 'cities:', businessCities);
            const cityResponse = await saveTenantBusinessCities(tenantId, businessCities);
            console.log('业务城市保存结果:', cityResponse);

            if (!cityResponse.success) {
              console.warn('业务城市保存失败，但租户保存成功');
            }
          } catch (cityError) {
            console.error('保存业务城市数据失败:', cityError);
            // 不阻断流程，只记录错误
          }
        }

        // 保存业务类型数据
        if (tenantId && businessTypes.length > 0) {
          try {
            console.log('开始保存业务类型数据, tenantId:', tenantId, 'types:', businessTypes);
            const typeResponse = await saveTenantBusinessTypes(tenantId, businessTypes);
            console.log('业务类型保存结果:', typeResponse);

            if (!typeResponse.success) {
              console.warn('业务类型保存失败，但租户保存成功');
            }
          } catch (typeError) {
            console.error('保存业务类型数据失败:', typeError);
            // 不阻断流程，只记录错误
          }
        }

        message.success(`${isUpdate ? '更新' : '创建'}租户成功`);
        onSuccess();
      } else {
        message.error(response.message || `${isUpdate ? '更新' : '创建'}租户失败`);
      }
    } catch (error) {
      console.error('表单验证或提交失败:', error);
      if (error.errorFields) {
        message.error(`表单验证失败: ${error.errorFields[0]?.errors[0] || '请检查表单'}`);
      } else {
        message.error('提交失败，请稍后重试');
      }
    }
  };

  // 上传组件属性
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/sys/common/upload',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 设置表单字段值
        form.setFieldsValue({
          companyLogo: info.file.response.message,
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  // 获取分组图标
  const getDividerIcon = (field: string) => {
    const iconMap = {
      'divider1': <InfoCircleOutlined />,
      'divider2': <BankOutlined />,
      'divider3': <ContactsOutlined />,
      'divider4': <TeamOutlined />,
      'divider5': <FileTextOutlined />,
    };
    return iconMap[field] || <InfoCircleOutlined />;
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={1000}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 1,
          isShow: 1,
        }}
      >
        {/* 基本信息分组 */}
        <div className="form-divider">
          <span className="divider-icon">
            {getDividerIcon('divider1')}
          </span>
          <span className="divider-text">基本信息</span>
        </div>

        <Form.Item
          name="name"
          label="租户名称"
          rules={[{ required: true, message: '请输入租户名称' }]}
        >
          <Input placeholder="请输入租户名称" />
        </Form.Item>

        {isUpdate && (
          <Form.Item
            name="id"
            label="租户编号(ID)"
            rules={[{ required: true, message: '请输入租户编号' }]}
          >
            <InputNumber
              placeholder="请输入租户编号"
              style={{ width: '100%' }}
              disabled
            />
          </Form.Item>
        )}

        <Form.Item
          name="companyLogo"
          label="组织LOGO"
        >
          <Input placeholder="请输入组织LOGO URL" />
        </Form.Item>

        <Form.Item
          name="wechatLogo"
          label="微信小程序LOGO"
        >
          <Input placeholder="请输入微信小程序LOGO URL" />
        </Form.Item>

        <Form.Item
          name="status"
          label="状态"
        >
          <Radio.Group>
            <Radio value={1}>正常</Radio>
            <Radio value={0}>冻结</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="isShow"
          label={
            <span>
              是否展示
              <Tooltip title="若不展示则数据大屏右侧下拉公司与门户都不展示此主体">
                <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
              </Tooltip>
            </span>
          }
          valuePropName="checked"
          getValueFromEvent={(checked) => checked ? 1 : 2}
          getValueProps={(value) => ({ checked: value === 1 })}
        >
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>

        <Form.Item
          name="score"
          label="评分"
        >
          <InputNumber
            min={0}
            max={5}
            precision={1}
            step={0.1}
            placeholder="请输入评分(0-5分，支持一位小数)"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="clickNum"
          label="点击数"
        >
          <InputNumber
            min={0}
            precision={0}
            placeholder="请输入点击数"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="clickId"
          label="点击ID"
          hidden
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="regTime"
          label="注册时间"
        >
          <Input
            placeholder="请输入注册时间"
            style={{ width: '100%' }}
          />
        </Form.Item>

        {/* 公司信息分组 */}
        <div className="form-divider">
          <span className="divider-icon">
            {getDividerIcon('divider2')}
          </span>
          <span className="divider-text">公司信息</span>
        </div>

        <Form.Item
          name="creditCode"
          label="统一社会信用代码"
        >
          <Input placeholder="请输入统一社会信用代码" />
        </Form.Item>

        <Form.Item
          name="legalPerson"
          label="法人"
        >
          <Input placeholder="请输入法人姓名" />
        </Form.Item>

        <Form.Item
          name="registeredCapital"
          label="注册资本"
        >
          <Input placeholder="请输入注册资本" />
        </Form.Item>

        <Form.Item
          name="companyAddress"
          label="公司地址"
        >
          <TextArea
            placeholder="请输入公司地址"
            rows={3}
          />
        </Form.Item>

        {/* 联系方式分组 */}
        <div className="form-divider">
          <span className="divider-icon">
            {getDividerIcon('divider3')}
          </span>
          <span className="divider-text">联系方式</span>
        </div>

        <Form.Item
          name="phone"
          label="电话"
        >
          <Input
            placeholder="请输入电话"
            addonBefore="+86"
          />
        </Form.Item>

        <Form.Item
          name="email"
          label="企业邮箱"
          rules={[
            {
              type: 'email',
              message: '请输入正确的邮箱格式',
            },
          ]}
        >
          <Input placeholder="请输入企业邮箱" />
        </Form.Item>



        {/* 其他信息分组 */}
        <div className="form-divider">
          <span className="divider-icon">
            {getDividerIcon('divider5')}
          </span>
          <span className="divider-text">其他信息</span>
        </div>

        <FormBusinessCitySelector
          name="businessCities"
          label={
            <span>
              <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
              业务城市
            </span>
          }
          placeholder="请选择业务开展的城市"
          showSelected={true}
          required={false}
        />

        <FormBusinessTypeSelector
          name="businessTypes"
          label={
            <span>
              <BankOutlined style={{ marginRight: 4, color: '#52c41a' }} />
              业务类型
            </span>
          }
          showSelected={true}
          required={false}
        />

        <Form.Item
          name="intro"
          label="公司简介"
        >
          <TextArea
            placeholder="请输入公司简介"
            rows={4}
          />
        </Form.Item>
      </Form>

      <style jsx global>{`
        .form-divider {
          margin: 10px 0 20px;
          font-weight: bold;
          font-size: 15px;
          color: #1890ff;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: 8px;
          position: relative;
        }

        .divider-icon {
          margin-right: 8px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }

        .divider-text {
          font-size: 15px;
        }
      `}</style>
    </Modal>
  );
};

export default TenantModal;
