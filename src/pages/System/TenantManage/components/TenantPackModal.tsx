import React, { useRef, useState } from 'react';
import { Modal, Button, message, Form, Input, Radio, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { getPackList, addPackPermission, editPackPermission, deleteTenantPack } from '@/services/system/tenant';
import { tenantPackColumns } from '../data';
import type { TenantPack } from '../data';

interface TenantPackModalProps {
  visible: boolean;
  tenantId: string;
  onCancel: () => void;
}

const TenantPackModal: React.FC<TenantPackModalProps> = ({
  visible,
  tenantId,
  onCancel,
}) => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [packModalVisible, setPackModalVisible] = useState<boolean>(false);
  const [packModalTitle, setPackModalTitle] = useState<string>('新增产品包');
  const [currentPack, setCurrentPack] = useState<Partial<TenantPack>>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  // 处理新增产品包
  const handleAddPack = () => {
    setPackModalTitle('新增产品包');
    setCurrentPack({});
    form.resetFields();
    setPackModalVisible(true);
  };

  // 处理编辑产品包
  const handleEditPack = (record: TenantPack) => {
    setPackModalTitle('编辑产品包');
    setCurrentPack(record);
    form.setFieldsValue(record);
    setPackModalVisible(true);
  };

  // 处理删除产品包
  const handleDeletePack = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该产品包吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteTenantPack(id);
          if (response.success) {
            message.success('删除成功');
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 处理产品包表单提交
  const handlePackSubmit = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);
      
      // 添加租户ID
      values.tenantId = tenantId;
      
      // 调用API保存数据
      const submitFunc = currentPack.id ? editPackPermission : addPackPermission;
      const response = await submitFunc(values);
      
      if (response.success) {
        message.success(`${currentPack.id ? '更新' : '创建'}产品包成功`);
        setPackModalVisible(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        message.error(response.message || `${currentPack.id ? '更新' : '创建'}产品包失败`);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setConfirmLoading(false);
    }
  };

  // 表格列配置
  const columns = [
    ...tenantPackColumns,
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 150,
      render: (_, record) => [
        <a key="edit" onClick={() => handleEditPack(record)}>
          编辑
        </a>,
        <a key="delete" onClick={() => handleDeletePack(record.id)}>
          删除
        </a>,
        <a key="users" onClick={() => message.info('用户管理功能待实现')}>
          用户
        </a>,
      ],
    },
  ];

  return (
    <>
      <Modal
        title="租户产品包列表"
        open={visible}
        onCancel={onCancel}
        width={1000}
        footer={null}
        destroyOnClose
      >
        <ProTable<TenantPack>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 120,
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddPack}
            >
              新增产品包
            </Button>,
          ]}
          request={async (params) => {
            // 处理分页参数
            const { current, pageSize, ...rest } = params;
            const response = await getPackList({
              pageNo: current,
              pageSize,
              tenantId,
              ...rest,
            });
            
            if (response.success) {
              return {
                data: response.result.records || [],
                success: true,
                total: response.result.total || 0,
              };
            }
            return {
              data: [],
              success: false,
              total: 0,
            };
          }}
          columns={columns}
          scroll={{ y: 400 }}
        />
      </Modal>

      {/* 产品包表单弹窗 */}
      <Modal
        title={packModalTitle}
        open={packModalVisible}
        onCancel={() => setPackModalVisible(false)}
        onOk={handlePackSubmit}
        confirmLoading={confirmLoading}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 1,
          }}
        >
          {currentPack.id && (
            <Form.Item
              name="id"
              label="ID"
              hidden
            >
              <Input />
            </Form.Item>
          )}
          
          <Form.Item
            name="packName"
            label="产品包名称"
            rules={[{ required: true, message: '请输入产品包名称' }]}
          >
            <Input placeholder="请输入产品包名称" />
          </Form.Item>
          
          <Form.Item
            name="packCode"
            label="产品包编码"
            rules={[{ required: true, message: '请输入产品包编码' }]}
          >
            <Input placeholder="请输入产品包编码" />
          </Form.Item>
          
          <Form.Item
            name="status"
            label="状态"
          >
            <Radio.Group>
              <Radio value={1}>启用</Radio>
              <Radio value={0}>禁用</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TenantPackModal;
