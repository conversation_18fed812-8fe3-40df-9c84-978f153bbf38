import React, { useRef, useState } from 'react';
import { Mo<PERSON>, Button, message, Dropdown } from 'antd';
import { DeleteOutlined, RedoOutlined, DownOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { getRecycleBinPageList, deleteLogicDeleted, revertTenantLogic } from '@/services/system/tenant';
import { recycleBinColumns } from '../data';
import type { Tenant } from '../data';

interface TenantRecycleBinModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const TenantRecycleBinModal: React.FC<TenantRecycleBinModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 处理彻底删除
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认彻底删除',
      content: '彻底删除后将无法恢复，确定要删除吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteLogicDeleted(id);
          if (response.success) {
            message.success('删除成功');
            if (actionRef.current) {
              actionRef.current.reload();
            }
            onSuccess();
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 处理还原
  const handleRevert = (id: string) => {
    Modal.confirm({
      title: '确认还原',
      content: '确定要还原该租户吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await revertTenantLogic(id);
          if (response.success) {
            message.success('还原成功');
            if (actionRef.current) {
              actionRef.current.reload();
            }
            onSuccess();
          } else {
            message.error(response.message || '还原失败');
          }
        } catch (error) {
          message.error('还原失败');
        }
      },
    });
  };

  // 处理批量彻底删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的租户');
      return;
    }

    Modal.confirm({
      title: '确认批量彻底删除',
      content: `彻底删除后将无法恢复，确定要删除选中的 ${selectedRowKeys.length} 个租户吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const promises = selectedRowKeys.map(id => deleteLogicDeleted(id as string));
          await Promise.all(promises);
          message.success('批量删除成功');
          setSelectedRowKeys([]);
          if (actionRef.current) {
            actionRef.current.reload();
          }
          onSuccess();
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  };

  // 处理批量还原
  const handleBatchRevert = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要还原的租户');
      return;
    }

    Modal.confirm({
      title: '确认批量还原',
      content: `确定要还原选中的 ${selectedRowKeys.length} 个租户吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const promises = selectedRowKeys.map(id => revertTenantLogic(id as string));
          await Promise.all(promises);
          message.success('批量还原成功');
          setSelectedRowKeys([]);
          if (actionRef.current) {
            actionRef.current.reload();
          }
          onSuccess();
        } catch (error) {
          message.error('批量还原失败');
        }
      },
    });
  };

  // 表格列配置
  const columns = [
    ...recycleBinColumns,
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 150,
      render: (_, record) => [
        <a key="delete" onClick={() => handleDelete(record.id)}>
          彻底删除
        </a>,
        <a key="revert" onClick={() => handleRevert(record.id)}>
          还原
        </a>,
      ],
    },
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys);
    },
  };

  // 批量操作菜单项
  const batchMenuItems = [
    {
      key: 'batchDelete',
      label: (
        <a onClick={handleBatchDelete}>
          <DeleteOutlined /> 批量删除
        </a>
      ),
    },
    {
      key: 'batchRevert',
      label: (
        <a onClick={handleBatchRevert}>
          <RedoOutlined /> 批量还原
        </a>
      ),
    },
  ];

  return (
    <Modal
      title="租户回收站"
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <ProTable<Tenant>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          selectedRowKeys.length > 0 && (
            <Dropdown menu={{ items: batchMenuItems }} key="batchActions">
              <Button>
                批量操作 <DownOutlined />
              </Button>
            </Dropdown>
          ),
        ]}
        request={async (params) => {
          // 处理分页参数
          const { current, pageSize, ...rest } = params;
          const response = await getRecycleBinPageList({
            pageNo: current,
            pageSize,
            ...rest,
          });
          
          if (response.success) {
            return {
              data: response.result.records || [],
              success: true,
              total: response.result.total || 0,
            };
          }
          return {
            data: [],
            success: false,
            total: 0,
          };
        }}
        columns={columns}
        rowSelection={rowSelection}
        scroll={{ y: 400 }}
      />
    </Modal>
  );
};

export default TenantRecycleBinModal;
