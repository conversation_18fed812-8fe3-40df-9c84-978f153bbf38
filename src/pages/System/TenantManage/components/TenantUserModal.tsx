import React, { useRef } from 'react';
import { <PERSON><PERSON>, But<PERSON>, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { getTenantUserList, leaveTenant } from '@/services/system/tenant';
import { tenantUserColumns } from '../data';
import type { TenantUser } from '../data';

interface TenantUserModalProps {
  visible: boolean;
  tenantId: string;
  onCancel: () => void;
}

const TenantUserModal: React.FC<TenantUserModalProps> = ({
  visible,
  tenantId,
  onCancel,
}) => {
  const actionRef = useRef<ActionType>();

  // 处理用户离开租户
  const handleUserLeave = (record: TenantUser) => {
    Modal.confirm({
      title: '请离确认',
      content: `确定要请离用户 ${record.realname || record.username} 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await leaveTenant({ userId: record.id, tenantId });
          if (response.success) {
            message.success('请离成功');
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            message.error(response.message || '请离失败');
          }
        } catch (error) {
          message.error('请离失败');
        }
      },
    });
  };

  // 表格列配置
  const columns = [
    ...tenantUserColumns.filter(col => col.dataIndex !== 'wechatQrcode'),
    {
      title: '微信二维码',
      dataIndex: 'wechatQrcode',
      width: 120,
      search: false,
      render: (_: any, record: TenantUser) => {
        if (record.wechatQrcode) {
          return (
            <img
              src={record.wechatQrcode}
              alt="微信二维码"
              style={{
                width: 60,
                height: 60,
                objectFit: 'cover',
                cursor: 'pointer',
              }}
              onClick={() => {
                // 点击放大显示
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                  newWindow.document.write(`
                    <html>
                      <head><title>微信二维码</title></head>
                      <body style="margin:0;padding:20px;text-align:center;background:#f0f0f0;">
                        <img src="${record.wechatQrcode}" style="max-width:100%;max-height:100%;" alt="微信二维码" />
                      </body>
                    </html>
                  `);
                }
              }}
            />
          );
        }
        return '-';
      },
    },
    {
      title: '操作',
      valueType: 'option' as const,
      key: 'option',
      width: 120,
      render: (_: any, record: TenantUser) => [
        <a key="leave" onClick={() => handleUserLeave(record)}>
          请离
        </a>,
      ],
    },
  ];

  return (
    <Modal
      title="租户用户列表"
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <ProTable<TenantUser>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              message.info('添加用户功能待实现');
            }}
          >
            添加用户
          </Button>,
        ]}
        request={async (params) => {
          // 处理分页参数
          const { current, pageSize, ...rest } = params;
          const response = await getTenantUserList({
            pageNo: current,
            pageSize,
            ...rest,
          });

          if (response.success) {
            return {
              data: response.result.records || [],
              success: true,
              total: response.result.total || 0,
            };
          }
          return {
            data: [],
            success: false,
            total: 0,
          };
        }}
        columns={columns}
        scroll={{ y: 400 }}
      />
    </Modal>
  );
};

export default TenantUserModal;
