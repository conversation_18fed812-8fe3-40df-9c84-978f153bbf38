import React, { useState, useRef, useEffect } from 'react';
import { Drawer, Button, Tabs, Empty, message } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { getRegionList, getLogisticsStaffList, addLogisticsStaff, removeLogisticsStaff, batchRemoveLogisticsStaff } from './logistics.api';
import { logisticsStaffColumns } from './logistics.data';
import type { TenantLogisticsStaff, Region } from './logistics.data';
import SelectUserModal from '../components/SelectUserModal';

const { TabPane } = Tabs;

interface LogisticsStaffDrawerProps {
  visible: boolean;
  tenantId: string;
  onClose: () => void;
}

const LogisticsStaffDrawer: React.FC<LogisticsStaffDrawerProps> = ({
  visible,
  tenantId,
  onClose,
}) => {
  // 状态管理
  const [regions, setRegions] = useState<Region[]>([]);
  const [activeRegion, setActiveRegion] = useState<string>('');
  const [selectUserModalVisible, setSelectUserModalVisible] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const actionRef = useRef<ActionType>();

  // 获取地区列表
  useEffect(() => {
    if (visible) {
      fetchRegions();
    }
  }, [visible]);

  const fetchRegions = async () => {
    try {
      const response = await getRegionList({ level: 1 }); // 获取省级地区
      if (response.success && response.result) {
        setRegions(response.result);
        if (response.result.length > 0) {
          setActiveRegion(response.result[0].code);
        }
      }
    } catch (error) {
      message.error('获取地区列表失败');
    }
  };

  // 处理添加后勤人员
  const handleAddStaff = () => {
    if (!activeRegion) {
      message.warning('请先选择地区');
      return;
    }
    setSelectUserModalVisible(true);
  };

  // 处理用户选择完成
  const handleUserSelected = async (selectedUsers: any[]) => {
    try {
      const regionInfo = regions.find(r => r.code === activeRegion);
      const staffList = selectedUsers.map(user => ({
        userId: user.id,
        regionCode: activeRegion,
        regionName: regionInfo?.name || '',
        tenantId,
      }));
      
      const response = await addLogisticsStaff(staffList);
      if (response.success) {
        message.success('添加后勤人员成功');
        setSelectUserModalVisible(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        message.error(response.message || '添加后勤人员失败');
      }
    } catch (error) {
      message.error('添加后勤人员失败');
    }
  };

  // 处理移除后勤人员
  const handleRemoveStaff = async (id: string) => {
    try {
      const response = await removeLogisticsStaff({ id });
      if (response.success) {
        message.success('移除后勤人员成功');
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        message.error(response.message || '移除后勤人员失败');
      }
    } catch (error) {
      message.error('移除后勤人员失败');
    }
  };

  // 处理批量移除后勤人员
  const handleBatchRemove = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要移除的后勤人员');
      return;
    }

    try {
      const response = await batchRemoveLogisticsStaff({ 
        ids: selectedRowKeys as string[] 
      });
      if (response.success) {
        message.success('批量移除后勤人员成功');
        setSelectedRowKeys([]);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        message.error(response.message || '批量移除后勤人员失败');
      }
    } catch (error) {
      message.error('批量移除后勤人员失败');
    }
  };

  // 表格列配置
  const columns = [
    ...logisticsStaffColumns,
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a key="remove" onClick={() => handleRemoveStaff(record.id)}>
          移除
        </a>,
      ],
    },
  ];

  return (
    <Drawer
      title="后勤人员配置"
      width={1000}
      placement="right"
      onClose={onClose}
      open={visible}
      extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAddStaff}>
          添加后勤人员
        </Button>
      }
    >
      {regions.length > 0 ? (
        <Tabs
          activeKey={activeRegion}
          onChange={setActiveRegion}
          tabPosition="left"
          style={{ height: 'calc(100vh - 120px)' }}
        >
          {regions.map(region => (
            <TabPane tab={region.name} key={region.code}>
              <ProTable<TenantLogisticsStaff>
                actionRef={actionRef}
                rowKey="id"
                search={false}
                options={false}
                headerTitle="后勤人员列表"
                toolBarRender={() => [
                  <Button
                    key="batchRemove"
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchRemove}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量移除
                  </Button>,
                ]}
                request={async () => {
                  const response = await getLogisticsStaffList({
                    tenantId,
                    regionCode: activeRegion,
                  });
                  
                  if (response.success) {
                    return {
                      data: response.result || [],
                      success: true,
                      total: response.result?.length || 0,
                    };
                  }
                  return {
                    data: [],
                    success: false,
                    total: 0,
                  };
                }}
                columns={columns}
                rowSelection={{
                  selectedRowKeys,
                  onChange: (keys) => setSelectedRowKeys(keys),
                }}
                pagination={false}
              />
            </TabPane>
          ))}
        </Tabs>
      ) : (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Empty description="暂无地区数据" />
        </div>
      )}

      {/* 选择用户弹窗 */}
      <SelectUserModal
        visible={selectUserModalVisible}
        tenantId={tenantId}
        onCancel={() => setSelectUserModalVisible(false)}
        onOk={handleUserSelected}
      />
    </Drawer>
  );
};

export default LogisticsStaffDrawer;
