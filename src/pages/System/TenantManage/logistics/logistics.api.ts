import { request } from '@umijs/max';

/**
 * 获取地区列表
 * @param params
 */
export async function getRegionList(params?: any) {
  return request('/sys/region/list', {
    method: 'GET',
    params,
  });
}

/**
 * 获取后勤人员列表
 * @param params
 */
export async function getLogisticsStaffList(params?: any) {
  return request('/sys/tenant/logistics/list', {
    method: 'GET',
    params,
  });
}

/**
 * 添加后勤人员
 * @param data
 */
export async function addLogisticsStaff(data: any) {
  return request('/sys/tenant/logistics/add', {
    method: 'POST',
    data,
  });
}

/**
 * 移除后勤人员
 * @param params
 */
export async function removeLogisticsStaff(params: { id: string }) {
  return request('/sys/tenant/logistics/remove', {
    method: 'DELETE',
    params,
  });
}

/**
 * 批量移除后勤人员
 * @param data
 */
export async function batchRemoveLogisticsStaff(data: { ids: string[] }) {
  return request('/sys/tenant/logistics/batchRemove', {
    method: 'DELETE',
    data,
  });
}
