import type { ProColumns } from '@ant-design/pro-components';

/**
 * 租户后勤人员数据类型
 */
export interface TenantLogisticsStaff {
  id: string;
  userId: string;
  username: string;
  realname: string;
  avatar?: string;
  phone?: string;
  regionCode: string;  // 地区编码
  regionName: string;  // 地区名称
  status: number;  // 状态：0-禁用，1-启用
  createTime?: string;
  tenantId: string;  // 所属租户ID
}

/**
 * 地区数据类型
 */
export interface Region {
  code: string;
  name: string;
  level: number;
  parentCode?: string;
}

/**
 * 后勤人员表格列定义
 */
export const logisticsStaffColumns: ProColumns<TenantLogisticsStaff>[] = [
  {
    title: '姓名',
    dataIndex: 'realname',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    valueEnum: {
      0: { text: '禁用', status: 'error' },
      1: { text: '启用', status: 'success' },
    },
  },
];
