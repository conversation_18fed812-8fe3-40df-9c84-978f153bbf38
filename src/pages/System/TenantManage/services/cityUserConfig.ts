import { request } from '@umijs/max';

export interface CityUserConfigParams {
  tenantId: number;
  cityCode: string;
  userIds: string[];
}

export interface CityUserConfigQuery {
  tenantId: number;
  cityCode: string;
}

export interface CityUserConfigItem {
  id: string;
  tenantId: number;
  cityCode: string;
  userId: string;
  createTime: string;
  updateTime: string;
}

/**
 * 保存地区后勤配置
 */
export async function saveCityUserConfig(params: CityUserConfigParams) {
  return request('/pd/cityUserRel/saveCityUserConfig', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询地区后勤配置
 */
export async function getCityUserConfig(params: CityUserConfigQuery) {
  return request('/pd/cityUserRel/getCityUserConfig', {
    method: 'GET',
    params,
  });
}

/**
 * 查询租户所有地区后勤配置
 */
export async function getTenantCityUserConfig(params: { tenantId: number }) {
  return request('/pd/cityUserRel/getTenantCityUserConfig', {
    method: 'GET',
    params,
  });
}

/**
 * 删除地区后勤配置
 */
export async function deleteCityUserConfig(params: CityUserConfigQuery) {
  return request('/pd/cityUserRel/deleteCityUserConfig', {
    method: 'DELETE',
    params,
  });
}
