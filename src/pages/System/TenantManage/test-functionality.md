# 租户管理功能测试清单

## 已实现的功能

### 1. 后端扩展
- [x] 扩展 `SysTenantDto` 类，添加了以下字段：
  - `businessCities`: 业务城市编码列表
  - `businessTypes`: 业务类型列表 (0-车险,1-财险,2-增值服务)
  - `businessCitiesEmpty`: 是否查询业务城市为空的记录

- [x] 更新 `SysTenantMapper.xml` 查询逻辑：
  - 添加业务城市查询条件（通过 `tenant_business_city_relation` 表关联）
  - 添加业务类型查询条件（通过 `tenant_business_type_relation` 表关联）
  - 添加业务城市为空的查询条件

### 2. 前端表格列配置
- [x] 添加序号列（使用 `valueType: 'indexBorder'`）
- [x] 添加业务城市显示列
- [x] 添加业务类型显示列
- [x] 为搜索功能添加隐藏的表单字段

### 3. 前端搜索功能
- [x] 租户名称搜索（支持模糊查询）
- [x] 业务城市选择（使用 FormCitySelector 组件）
- [x] 业务类型选择（多选下拉框）
- [x] 业务城市为空查询（是/否选择）

### 4. 城市选择器集成
- [x] 使用 SystemCompletion 页面中的 FormCitySelector 组件
- [x] 确保传参使用城市 code 格式
- [x] 支持后端数据源和自动编码转换

## 测试步骤

### 1. 基础功能测试
1. 访问租户管理页面
2. 检查表格是否显示序号列
3. 检查业务城市和业务类型列是否正常显示

### 2. 搜索功能测试
1. 测试租户名称搜索
   - 输入租户名称关键字
   - 验证搜索结果是否正确

2. 测试业务城市筛选
   - 选择一个或多个城市
   - 验证筛选结果是否正确
   - 检查传参是否使用城市编码格式

3. 测试业务类型筛选
   - 选择一个或多个业务类型
   - 验证筛选结果是否正确

4. 测试业务城市为空查询
   - 选择"是"，应该只显示没有设置业务城市的租户
   - 选择"否"，应该只显示已设置业务城市的租户

### 3. 组合查询测试
1. 同时使用多个搜索条件
2. 验证查询结果的准确性
3. 测试重置搜索功能

## 注意事项

1. **数据库表结构**：确保以下表存在并有正确的数据：
   - `tenant_business_city_relation`：租户业务城市关系表
   - `tenant_business_type_relation`：租户业务类型关系表

2. **城市编码格式**：城市选择器返回的格式为 "code:name:type"，需要提取编码部分传给后端

3. **类型转换**：业务城市为空查询的参数需要正确转换为布尔值

## 可能的问题和解决方案

1. **城市显示问题**：如果业务城市列显示的是编码而不是名称，需要添加编码到名称的转换逻辑

2. **关联表不存在**：如果数据库中没有相关的关联表，需要创建或调整查询逻辑

3. **权限问题**：确保当前用户有查询租户列表的权限

4. **性能问题**：如果数据量大，考虑添加索引或优化查询逻辑
