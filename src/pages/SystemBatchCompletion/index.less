.mainContainer {
  background-color: #fff;
  padding: 24px;
  min-height: 500px;
  position: relative;
}

.stepsContainer {
  margin-bottom: 32px;
}

.steps {
  max-width: 800px;
  margin: 0 auto;
}

.contentContainer {
  max-width: 800px;
  margin: 0 auto 24px;
}

.actionContainer {
  max-width: 800px;
  margin: 24px auto 0;
  display: flex;
  justify-content: flex-end;
}

.uploadCard,
.processingCard,
.resultCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.fileInfo {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.fileIcon {
  font-size: 24px;
  margin-right: 8px;
}

.uploadedFiles {
  margin-top: 16px;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mainContainer {
    padding: 16px;
  }
  
  .stepsContainer {
    margin-bottom: 24px;
  }
  
  .contentContainer {
    margin-bottom: 16px;
  }
}
