import React, { useState, useEffect, useRef } from 'react';
import { Page<PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import {
  Form,
  Button,
  Space,
  Typography,
  message,
  Upload,
  Alert,
  Row,
  Col,
  Card,
  Progress,
  Statistic,
  Tabs,
  Table,
  Tag,
  DatePicker,
  InputNumber,
  Radio,
  Checkbox,
  Collapse,
  Spin,
  Tooltip,
} from 'antd';
import {
  FileExcelOutlined,
  InboxOutlined,
  CloudUploadOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BarChartOutlined,
  UploadOutlined,
  SettingOutlined,
  BankOutlined,
  EnvironmentOutlined,
  CarOutlined,
  GiftOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import { uploadBatchSystemCompletionFile, getBatchSystemCompletionProgress, uploadSystemCompletionFiles, DailyConfigDto, validateBatchSystemCompletionFile, BatchImportResultVO, downloadTemplate } from '@/services/corp/tenantConfig';
import { rangeDeletePdLedger } from '@/services/corp/pdLedger';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import TenantSelect from '@/components/TenantSelect';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TenantInfo {
  tenantId: number;
  tenantName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  clickCount?: number;
  status: string;
}

interface TaskProgress {
  totalTasks: number;
  completedTasks: number;
  remainingTasks: number;
  status: string;
  percent: number;
  tenantList: TenantInfo[];
}

const SystemBatchCompletion: React.FC = () => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // 三步骤流程状态
  const [importStep, setImportStep] = useState(1); // 1: 选择文件, 2: 校验结果, 3: 导入完成
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<BatchImportResultVO | null>(null);

  // 任务进度状态
  const [taskProgress, setTaskProgress] = useState<TaskProgress>({
    totalTasks: 0,
    completedTasks: 0,
    remainingTasks: 0,
    status: '',
    percent: 0,
    tenantList: []
  });

  // 当前活动的标签页
  const [activeTab, setActiveTab] = useState('upload');

  // 范围删除表单
  const [rangeDeleteForm] = Form.useForm();

  // 轮询定时器
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [polling, setPolling] = useState(false);

  // 开始轮询任务进度
  const startPolling = () => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
    }

    setPolling(true);

    // 立即执行一次
    fetchTaskProgress();

    // 设置定时器，每3秒轮询一次
    pollingTimerRef.current = setInterval(() => {
      fetchTaskProgress();
    }, 3000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    setPolling(false);
  };

  // 获取任务进度
  const fetchTaskProgress = async () => {
    try {
      const response = await getBatchSystemCompletionProgress();

      if (response && response.success && response.result) {
        const {
          totalTasks,
          completedTasks,
          remainingTasks,
          status,
          tenantList
        } = response.result;

        // 计算百分比
        const percent = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;

        setTaskProgress({
          totalTasks,
          completedTasks,
          remainingTasks,
          status,
          percent,
          tenantList: tenantList || []
        });

        // 如果任务已完成或失败，停止轮询
        if (status === '已完成' || status === '处理失败') {
          stopPolling();
        }
      }
    } catch (error) {
      console.error('获取任务进度失败:', error);
      // 出错时不停止轮询，继续尝试
    }
  };

  // 格式化时间戳为可读时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // 格式化持续时间（毫秒转为分钟和秒）
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  // 组件挂载时获取任务进度
  useEffect(() => {
    // 页面加载时获取任务进度，无论是否有上传成功的记录
    fetchTaskProgress();
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
      }
    };
  }, []);

  // 处理下载模板
  const handleDownloadTemplate = async () => {
    try {
      // 传递后端文件名和显示文件名
      await downloadTemplate('buquan.xls', '根据租户批量补全模板.xls');
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败，请稍后重试');
    }
  };

  // 处理文件校验
  const handleValidateFile = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要上传的文件');
      return;
    }

    const file = fileList[0];
    if (!file.originFileObj) {
      message.error('无法获取文件，请重新上传');
      return;
    }

    try {
      setValidating(true);

      // 调用API校验文件
      const result = await validateBatchSystemCompletionFile(file.originFileObj);

      if (result && result.success && result.result) {
        setValidationResult(result.result);
        setImportStep(2); // 进入第二步：显示校验结果
        message.success('文件校验完成');
      } else {
        message.error(result?.message || '文件校验失败，请稍后重试');
      }
    } catch (error) {
      console.error('文件校验错误:', error);
      message.error('文件校验失败，请稍后重试');
    } finally {
      setValidating(false);
    }
  };

  // 处理表单提交（第三步：确认导入）
  const handleSubmit = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要上传的文件');
      return;
    }

    const file = fileList[0];
    if (!file.originFileObj) {
      message.error('无法获取文件，请重新上传');
      return;
    }

    try {
      setSubmitting(true);

      // 调用API上传文件
      const result = await uploadBatchSystemCompletionFile(file.originFileObj);

      if (result && result.success) {
        setUploadSuccess(true);
        setImportStep(3); // 进入第三步：导入完成
        message.success('批量系统补全任务已提交');

        // 开始轮询任务进度
        startPolling();

        // 切换到任务中心页签
        setActiveTab('taskCenter');
      } else {
        message.error(result?.message || '提交失败，请稍后重试');
      }
    } catch (error) {
      console.error('批量系统补全提交错误:', error);
      message.error('提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setFileList([]);
    setUploadSuccess(false);
    setImportStep(1); // 重置到第一步
    setValidationResult(null);
    stopPolling();
    setTaskProgress({
      totalTasks: 0,
      completedTasks: 0,
      remainingTasks: 0,
      status: '',
      percent: 0,
      tenantList: []
    });
  };

  // 返回上一步
  const handleBackStep = () => {
    if (importStep > 1) {
      setImportStep(importStep - 1);
    }
  };

  // 处理文件上传前检查
  const beforeUpload = (file: RcFile) => {
    const isExcelOrCsv =
      file.type === 'application/vnd.ms-excel' ||
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'text/csv' ||
      /\.(xlsx|xls|csv)$/i.test(file.name);

    if (!isExcelOrCsv) {
      message.error('只能上传 Excel 或 CSV 文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('文件必须小于 20MB!');
      return Upload.LIST_IGNORE;
    }

    // 限制只能上传一个文件
    setFileList([{
      uid: '1',
      name: file.name,
      status: 'done',
      size: file.size,
      type: file.type,
      originFileObj: file,
    }]);

    return false;
  };

  // 处理文件上传变化
  const handleUploadChange: UploadProps['onChange'] = ({ fileList }) => {
    // 只保留最新的一个文件
    if (fileList.length > 0) {
      setFileList([fileList[fileList.length - 1]]);
    } else {
      setFileList([]);
    }
  };

  // 渲染上传页签内容
  const renderUploadTab = () => (
    <Row gutter={[24, 24]}>
      <Col span={24}>
        <Alert
          message="批量系统补全说明"
          description={
            <div>
              <Paragraph>
                通过上传Excel文件，可以一次性为多个公司配置系统补全数据。系统将分三个步骤进行处理：
              </Paragraph>
              <Paragraph>
                <strong>处理步骤：</strong>
                <ol>
                  <li><strong>第一步：导入表格</strong> - 选择并上传Excel文件</li>
                  <li><strong>第二步：校验表格内容</strong> - 系统校验租户是否存在、城市比例和城市是否有填写</li>
                  <li><strong>第三步：开始导入</strong> - 校验通过后确认导入，开始批量补全任务</li>
                </ol>
              </Paragraph>
              <Paragraph>
                <strong>文件要求：</strong>
                <ul>
                  <li>文件格式：Excel (.xlsx, .xls) 或 CSV (.csv)</li>
                  <li>文件大小：不超过20MB</li>
                  <li>每行数据对应一个公司的系统补全配置</li>
                  <li>必须包含：租户ID、城市名称、城市比例等必要字段</li>
                </ul>
              </Paragraph>
              <Paragraph>
                <Button
                  type="link"
                  icon={<DownloadOutlined />}
                  onClick={handleDownloadTemplate}
                  style={{ padding: 0, height: 'auto' }}
                >
                  下载导入模板 (根据租户批量补全模板.xls)
                </Button>
              </Paragraph>
            </div>
          }
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 24 }}
        />
      </Col>

      {/* 文件上传区域 */}
      <Col span={24}>
        <Card title={`第${importStep}步：${importStep === 1 ? '导入表格' : importStep === 2 ? '校验表格内容' : '开始导入'}`}>
          {/* 第一步：选择文件 */}
          {importStep === 1 && (
            <>
              <Upload.Dragger
                name="file"
                fileList={fileList}
                beforeUpload={beforeUpload}
                onChange={handleUploadChange}
                onRemove={() => setFileList([])}
                maxCount={1}
                accept=".xlsx,.xls,.csv"
                style={{ marginBottom: 24 }}
                disabled={validating}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持单个Excel或CSV文件上传，文件大小不超过20MB
                </p>
              </Upload.Dragger>

              {fileList.length > 0 && (
                <div style={{ marginTop: 16, display: 'flex', alignItems: 'center' }}>
                  <Space align="center">
                    <FileExcelOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                    <div>
                      <Text strong>{fileList[0].name}</Text>
                      <br />
                      <Text type="secondary">
                        {(fileList[0].size! / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    </div>
                  </Space>
                </div>
              )}

              <div style={{ marginTop: 24, textAlign: 'right' }}>
                <Space>
                  <Button onClick={handleReset} disabled={validating}>重置</Button>
                  <Button
                    type="primary"
                    onClick={handleValidateFile}
                    disabled={fileList.length === 0 || validating}
                    loading={validating}
                    icon={<CloudUploadOutlined />}
                  >
                    校验文件
                  </Button>
                </Space>
              </div>
            </>
          )}

          {/* 第二步：显示校验结果 */}
          {importStep === 2 && validationResult && (
            <>
              <div style={{ marginBottom: 24 }}>
                <Alert
                  message={validationResult.success ? "文件校验通过" : "文件校验失败"}
                  description={
                    <div>
                      <p>{validationResult.message}</p>
                      {validationResult.success ? (
                        <p>成功校验 <Text strong style={{ color: '#52c41a' }}>{validationResult.successCount}</Text> 条记录，可以开始导入。</p>
                      ) : (
                        <p>失败 <Text strong style={{ color: '#ff4d4f' }}>{validationResult.failureCount}</Text> 条记录，请修正后重新上传。</p>
                      )}
                    </div>
                  }
                  type={validationResult.success ? "success" : "error"}
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                {/* 显示失败详情 */}
                {!validationResult.success && validationResult.failureDetails && validationResult.failureDetails.length > 0 && (
                  <Card title="校验失败详情" size="small">
                    <Table
                      dataSource={validationResult.failureDetails.map((item, index) => ({ ...item, key: index }))}
                      columns={[
                        { title: '行号', dataIndex: 'rowNumber', key: 'rowNumber' },
                        { title: '城市名称', dataIndex: 'cityName', key: 'cityName' },
                        { title: '失败原因', dataIndex: 'reason', key: 'reason' },
                      ]}
                      pagination={false}
                      size="small"
                    />
                  </Card>
                )}
              </div>

              <div style={{ textAlign: 'right' }}>
                <Space>
                  <Button onClick={handleBackStep}>返回上一步</Button>
                  <Button onClick={handleReset}>重置</Button>
                  {validationResult.success && (
                    <Button
                      type="primary"
                      onClick={handleSubmit}
                      disabled={submitting}
                      loading={submitting}
                      icon={<CloudUploadOutlined />}
                    >
                      确认导入
                    </Button>
                  )}
                </Space>
              </div>
            </>
          )}

          {/* 第三步：导入完成 */}
          {importStep === 3 && (
            <>
              <Alert
                message="导入任务已提交"
                description={
                  <div>
                    <p>您的文件已成功上传，系统正在处理中。</p>
                    <p>请点击上方的"任务中心"标签页查看处理进度。</p>
                  </div>
                }
                type="success"
                showIcon
                action={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => setActiveTab('taskCenter')}
                  >
                    查看进度
                  </Button>
                }
                style={{ marginBottom: 16 }}
              />

              <div style={{ textAlign: 'right' }}>
                <Button onClick={handleReset}>重新开始</Button>
              </div>
            </>
          )}
        </Card>
      </Col>

    </Row>
  );

  // 渲染系统补全页签内容
  const renderSystemCompletionTab = () => {
    const [systemForm] = Form.useForm();
    const [systemSubmitting, setSystemSubmitting] = useState(false);
    const [inputMode, setInputMode] = useState<'count' | 'ratio'>('ratio');
    const [calculatedValue, setCalculatedValue] = useState<number | null>(null);
    const [selectedLedgerTypes, setSelectedLedgerTypes] = useState<string[]>(['car']);
    const [activeCollapseKeys, setActiveCollapseKeys] = useState<string[]>(['car']);

    // 计算比例或台账数
    const calculateValue = () => {
      const clickNum = systemForm.getFieldValue('clickNum');
      const recordNum = systemForm.getFieldValue('recordNum');

      if (!clickNum || !recordNum) {
        setCalculatedValue(null);
        return;
      }

      if (inputMode === 'count') {
        // 用户输入的是台账数，计算比例
        // 注意：后端期望的是直接的百分比值，1%就是1，12.5%就是12.5
        const ratio = Math.min(100, ((recordNum / clickNum) * 100));
        setCalculatedValue(parseFloat(ratio.toFixed(1)));
      } else {
        // 用户输入的是比例，计算台账数
        // 用户输入的比例值直接传递给后端，不需要除以100
        const count = Math.round((recordNum / 100) * clickNum);
        setCalculatedValue(count);
      }
    };

    // 处理台账类型选择变化
    const handleLedgerTypeChange = (checkedValues: string[]) => {
      setSelectedLedgerTypes(checkedValues);
      // 根据勾选状态更新折叠面板的展开状态
      setActiveCollapseKeys(checkedValues);
    };

    // 当点击数或台账数/比例变化时重新计算
    useEffect(() => {
      if (systemForm) {
        calculateValue();
      }
    }, [systemForm?.getFieldValue('clickNum'), systemForm?.getFieldValue('recordNum'), inputMode]);

    // 监听表单字段变化，自动展开/折叠对应的区间设置
    useEffect(() => {
      const values = systemForm.getFieldsValue();
      const activeKeys: string[] = [];

      // 根据各台账类型的启用状态决定是否展开对应的折叠面板
      if (values.carInsurance?.enabled) {
        activeKeys.push('car');
      }
      if (values.propertyInsurance?.enabled) {
        activeKeys.push('property');
      }
      if (values.valueAddedService?.enabled) {
        activeKeys.push('valueAdded');
      }

      setActiveCollapseKeys(activeKeys);
    }, [systemForm]);

    // 处理系统补全表单提交
    const handleSystemSubmit = async () => {
      try {
        const values = await systemForm.validateFields();
        setSystemSubmitting(true);

        // 处理日期范围
        const dateRange = values.dateRange || [];
        const startDate = dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : '';
        const endDate = dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : '';

        // 处理城市代码
        const cityCodes = values.provinces ? values.provinces.map((item: string) => {
          const [city] = item.split(':');
          return city;
        }) : [];

        // 计算最终的比例值
        let finalRatio = values.recordNum;
        if (inputMode === 'count') {
          // 如果用户输入的是台账数，转换为比例（保留一位小数）
          const ratio = Math.min(100, ((values.recordNum / values.clickNum) * 100));
          finalRatio = parseFloat(ratio.toFixed(1));
        }
        // 注意：后端期望的是直接的百分比值，1%就是1，12.5%就是12.5

        // 检查是否选择了至少一种台账类型
        const hasCarInsurance = values.carInsurance?.enabled;
        const hasPropertyInsurance = values.propertyInsurance?.enabled;
        const hasValueAddedService = values.valueAddedService?.enabled;

        if (!hasCarInsurance && !hasPropertyInsurance && !hasValueAddedService) {
          message.warning('请至少选择一种台账类型');
          setSystemSubmitting(false);
          return;
        }

        // 构建配置JSON
        const configJson: any = {};

        // 添加台账类型配置 - 按照后端期望的格式构建
        // 车险台账 - carLedger
        if (values.carInsurance?.enabled) {
          configJson.carLedger = {
            ledgerStart: values.carInsurance.ledgerPercentMin || 45,
            ledgerEnd: values.carInsurance.ledgerPercentMax || 55,
            chatUserStart: values.carInsurance.chatPercentMin || 25,
            chatUserEnd: values.carInsurance.chatPercentMax || 35,
          };
        }

        // 财险台账 - financeLedger
        if (values.propertyInsurance?.enabled) {
          configJson.financeLedger = {
            ledgerStart: values.propertyInsurance.ledgerPercentMin || 25,
            ledgerEnd: values.propertyInsurance.ledgerPercentMax || 35,
            chatUserStart: values.propertyInsurance.chatPercentMin || 25,
            chatUserEnd: values.propertyInsurance.chatPercentMax || 35,
          };
        }

        // 增值服务台账 - valueAddedLedger
        if (values.valueAddedService?.enabled) {
          configJson.valueAddedLedger = {
            ledgerStart: values.valueAddedService.ledgerPercentMin || 20,
            ledgerEnd: values.valueAddedService.ledgerPercentMax || 30,
            chatUserStart: values.valueAddedService.chatPercentMin || 25,
            chatUserEnd: values.valueAddedService.chatPercentMax || 35,
          };
        }

        // 构建提交数据，确保与后端 LedgerImportDTO 类型一致
        const submitData = {
          startDate,
          endDate,
          recordNum: finalRatio.toString(), // 直接传递百分比值，1表示1%，12.5表示12.5%
          clickNum: values.clickNum, // 后端需要 Integer 类型
          tenantId: values.tenantIds, // 租户ID列表，后端需要 List<String>
          cityCodes, // 城市代码列表，后端需要 List<String>
          configJson: JSON.stringify(configJson), // 添加台账类型配置
          // 根据选中的台账类型设置linkType
          linkType: values.carInsurance?.enabled ? 0 : (values.propertyInsurance?.enabled ? 1 : 2),
        };

        // 调用API
        console.log('提交数据:', submitData);

        // 模拟延迟，让用户感知到提交过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        const result = await uploadSystemCompletionFiles(submitData);

        if (result && result.success) {
          message.success(result.message || '系统补全任务已创建');

          // 开始轮询任务进度
          startPolling();

          // 切换到任务中心页签
          setActiveTab('taskCenter');
        } else {
          message.error(result?.message || '创建系统补全任务失败');
        }
      } catch (error) {
        console.error('系统补全提交错误:', error);
        message.error('提交失败，请检查表单');
      } finally {
        setSystemSubmitting(false);
      }
    };

    return (
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Alert
            message="系统补全说明"
            description={
              <div>
                <Paragraph>
                  通过设置参数，可以为多个租户生成系统补全数据。请确保填写正确的参数。
                </Paragraph>
                <Paragraph>
                  <strong>参数说明：</strong>
                  <ul>
                    <li>日期范围：生成数据的时间范围</li>
                    <li>台账数量：需要生成的台账数量</li>
                    <li>点击数：每个台账的点击数</li>
                    <li>租户：选择需要生成数据的租户（可多选）</li>
                    <li>城市：选择需要生成数据的城市（可多选）</li>
                  </ul>
                </Paragraph>
              </div>
            }
            type="info"
            showIcon
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 24 }}
          />
        </Col>

        <Col span={24}>
          <Card title="系统补全配置">
            <Form
              form={systemForm}
              layout="horizontal"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 8 }}
            >
              <Form.Item
                name="dateRange"
                label="开始/结束日期"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <DatePicker.RangePicker style={{ width: '350px' }} />
              </Form.Item>

              <Form.Item
                name="clickNum"
                label="点击数"
                rules={[{ required: true, message: '请输入点击数' }]}
              >
                <InputNumber
                  style={{ width: '200px' }}
                  min={1}
                  placeholder="请输入点击数"
                  onChange={() => calculateValue()}
                />
              </Form.Item>

              <Form.Item label="输入方式" style={{ marginBottom: 12 }}>
                <Radio.Group
                  value={inputMode}
                  onChange={(e) => {
                    setInputMode(e.target.value);
                    systemForm.setFieldsValue({ recordNum: undefined });
                    setCalculatedValue(null);
                  }}
                  buttonStyle="solid"
                  optionType="button"
                  size="middle"
                >
                  <Radio.Button value="ratio">按比例</Radio.Button>
                  <Radio.Button value="count">按台账数</Radio.Button>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name="recordNum"
                label={inputMode === 'ratio' ? "台账占比" : "台账数量"}
                rules={[{ required: true, message: inputMode === 'ratio' ? '请输入台账占比' : '请输入台账数量' }]}
                tooltip={inputMode === 'ratio'
                  ? "台账数占点击数的比例，取值范围0.1-100，例如：22.3表示22.3%的点击生成台账"
                  : "生成的台账数量，不能超过点击数"}
              >
                <InputNumber
                  style={{ width: '200px' }}
                  min={inputMode === 'ratio' ? 0.1 : 1}
                  max={inputMode === 'ratio' ? 100 : systemForm.getFieldValue('clickNum')}
                  precision={inputMode === 'ratio' ? 1 : 0}
                  step={inputMode === 'ratio' ? 0.1 : 1}
                  placeholder={inputMode === 'ratio' ? "请输入台账占比" : "请输入台账数量"}
                  onChange={() => calculateValue()}
                  addonAfter={inputMode === 'ratio' ? "%" : "条"}
                />
              </Form.Item>

              {calculatedValue !== null && (
                <Form.Item label="计算结果" style={{ marginBottom: 12 }}>
                  <Tag color="blue" style={{ fontSize: '14px', padding: '2px 8px' }}>
                    {inputMode === 'ratio'
                      ? `${calculatedValue} 条`
                      : `${calculatedValue}%`}
                  </Tag>
                </Form.Item>
              )}

              <Form.Item
                name="tenantIds"
                label="选择租户"
                rules={[{ required: true, message: '请选择租户' }]}
              >
                <TenantSelect
                  mode="multiple"
                  placeholder="请选择租户(可多选)"
                  allowClear
                  style={{ width: '350px' }}
                />
              </Form.Item>

              <Form.Item
                name="provinces"
                label="选择省份/城市"
                rules={[{ required: true, message: '请选择省份/城市' }]}
              >
                <FormCitySelector
                  name="provinces"
                  cityData={cityData}
                  placeholder="请选择省份或城市(可多选)"
                  showSelected={false}
                  required={false}
                  style={{ width: '350px' }}
                />
              </Form.Item>

              {/* 台账类型选择 */}
              <Form.Item
                label="台账类型选择"
                required
                tooltip="请至少选择一种台账类型"
              >
                <Card size="small" style={{ marginBottom: 16 }}>
                  <Form.Item
                    name="ledgerTypes"
                    noStyle
                  >
                    <Checkbox.Group onChange={handleLedgerTypeChange}>
                      <Collapse
                        bordered={false}
                        activeKey={activeCollapseKeys}
                      >
                        {/* 车险台账 */}
                        <Collapse.Panel
                          key="car"
                          header={
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <Form.Item name={['carInsurance', 'enabled']} valuePropName="checked" noStyle>
                                <Checkbox
                                  value="car"
                                  onChange={(e) => {
                                    // 更新折叠面板状态
                                    if (e.target.checked) {
                                      setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'car'), 'car']);
                                    } else {
                                      setActiveCollapseKeys(prev => prev.filter(k => k !== 'car'));
                                    }
                                  }}
                                >
                                  <Space>
                                    <CarOutlined />
                                    <span>车险台账</span>
                                  </Space>
                                </Checkbox>
                              </Form.Item>
                              <Text type="secondary" style={{ marginLeft: 24 }}>包含车辆保险相关记录</Text>
                            </div>
                          }

                        >
                          <Row gutter={[16, 8]}>
                            <Col span={24}>
                              <Card size="small">
                                <Row gutter={16}>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>台账数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['carInsurance', 'ledgerPercentMin']} noStyle initialValue={45}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['carInsurance', 'ledgerPercentMax']} noStyle initialValue={55}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                                    </div>
                                  </Col>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>聊天用户数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['carInsurance', 'chatPercentMin']} noStyle initialValue={25}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['carInsurance', 'chatPercentMax']} noStyle initialValue={35}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                                    </div>
                                  </Col>
                                </Row>
                              </Card>
                            </Col>
                          </Row>
                        </Collapse.Panel>

                        {/* 财险台账 */}
                        <Collapse.Panel
                          key="property"
                          header={
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <Form.Item name={['propertyInsurance', 'enabled']} valuePropName="checked" noStyle>
                                <Checkbox
                                  value="property"
                                  onChange={(e) => {
                                    // 更新折叠面板状态
                                    if (e.target.checked) {
                                      setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'property'), 'property']);
                                    } else {
                                      setActiveCollapseKeys(prev => prev.filter(k => k !== 'property'));
                                    }
                                  }}
                                >
                                  <Space>
                                    <BankOutlined />
                                    <span>财险台账</span>
                                  </Space>
                                </Checkbox>
                              </Form.Item>
                              <Text type="secondary" style={{ marginLeft: 24 }}>包含财产保险相关记录</Text>
                            </div>
                          }

                        >
                          <Row gutter={[16, 8]}>
                            <Col span={24}>
                              <Card size="small">
                                <Row gutter={16}>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>台账数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['propertyInsurance', 'ledgerPercentMin']} noStyle initialValue={25}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['propertyInsurance', 'ledgerPercentMax']} noStyle initialValue={35}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                                    </div>
                                  </Col>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>聊天用户数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['propertyInsurance', 'chatPercentMin']} noStyle initialValue={25}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['propertyInsurance', 'chatPercentMax']} noStyle initialValue={35}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                                    </div>
                                  </Col>
                                </Row>
                              </Card>
                            </Col>
                          </Row>
                        </Collapse.Panel>

                        {/* 增值服务台账 */}
                        <Collapse.Panel
                          key="valueAdded"
                          header={
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <Form.Item name={['valueAddedService', 'enabled']} valuePropName="checked" noStyle>
                                <Checkbox
                                  value="valueAdded"
                                  onChange={(e) => {
                                    // 更新折叠面板状态
                                    if (e.target.checked) {
                                      setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'valueAdded'), 'valueAdded']);
                                    } else {
                                      setActiveCollapseKeys(prev => prev.filter(k => k !== 'valueAdded'));
                                    }
                                  }}
                                >
                                  <Space>
                                    <GiftOutlined />
                                    <span>增值服务台账</span>
                                  </Space>
                                </Checkbox>
                              </Form.Item>
                              <Text type="secondary" style={{ marginLeft: 24 }}>包含增值服务相关记录</Text>
                            </div>
                          }

                        >
                          <Row gutter={[16, 8]}>
                            <Col span={24}>
                              <Card size="small">
                                <Row gutter={16}>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>台账数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['valueAddedService', 'ledgerPercentMin']} noStyle initialValue={20}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['valueAddedService', 'ledgerPercentMax']} noStyle initialValue={30}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                                    </div>
                                  </Col>
                                  <Col span={12}>
                                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                      <Text strong>聊天用户数占比区间 (%)</Text>
                                      <Space>
                                        <Form.Item name={['valueAddedService', 'chatPercentMin']} noStyle initialValue={25}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>至</span>
                                        <Form.Item name={['valueAddedService', 'chatPercentMax']} noStyle initialValue={35}>
                                          <InputNumber min={0} max={100} />
                                        </Form.Item>
                                        <span>%</span>
                                      </Space>
                                      <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                                    </div>
                                  </Col>
                                </Row>
                              </Card>
                            </Col>
                          </Row>
                        </Collapse.Panel>
                      </Collapse>
                    </Checkbox.Group>
                  </Form.Item>
                </Card>
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 4, span: 8 }}>
                <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '10px' }}>
                  <Button
                    type="primary"
                    onClick={handleSystemSubmit}
                    loading={systemSubmitting}
                  >
                    提交
                  </Button>
                  <Button
                    onClick={() => systemForm.resetFields()}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染范围删除页签内容
  const renderRangeDeleteTab = () => {
    const [rangeDeleteSubmitting, setRangeDeleteSubmitting] = useState(false);

    // 处理范围删除表单提交
    const handleRangeDeleteSubmit = async () => {
      try {
        const values = await rangeDeleteForm.validateFields();
        setRangeDeleteSubmitting(true);

        // 处理日期范围和租户ID列表
        const data = {
          startDate: values.dateRange[0].format('YYYY-MM-DD'),
          endDate: values.dateRange[1].format('YYYY-MM-DD'),
          tenantIdList: values.tenantIdList.map((id: any) => id.toString()),
        };

        // 调用删除接口
        const response = await rangeDeletePdLedger(data);

        if (response && response.success) {
          message.success('范围删除成功');
          rangeDeleteForm.resetFields();
          // 不再切换到任务中心页签
          // 不再开始轮询任务进度
        } else {
          message.error(response.message || '范围删除失败');
        }
      } catch (error) {
        console.error('范围删除失败:', error);
        message.error('提交失败，请检查表单');
      } finally {
        setRangeDeleteSubmitting(false);
      }
    };

    return (
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Alert
            message="范围删除说明"
            description={
              <div>
                <Paragraph>
                  通过设置日期范围和所属公司，可以批量删除指定范围内的数据。请谨慎操作，删除后数据无法恢复。
                </Paragraph>
                <Paragraph>
                  <strong>删除范围：</strong>将会删除关联台账、预约信息、聊天用户、点击量、聊天记录等相关数据。
                </Paragraph>
                <Paragraph>
                  <strong>参数说明：</strong>
                  <ul>
                    <li>日期范围：要删除数据的时间范围</li>
                    <li>所属公司：要删除数据的公司（可多选）</li>
                  </ul>
                </Paragraph>
              </div>
            }
            type="warning"
            showIcon
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 24 }}
          />
        </Col>

        <Col span={24}>
          <Card title="范围删除配置">
            <Form
              form={rangeDeleteForm}
              layout="horizontal"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 8 }}
            >
              <Form.Item
                name="dateRange"
                label="日期范围"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <DatePicker.RangePicker style={{ width: '350px' }} />
              </Form.Item>

              <Form.Item
                name="tenantIdList"
                label="所属公司"
                rules={[{ required: true, message: '请选择所属公司' }]}
              >
                <TenantSelect
                  mode="multiple"
                  placeholder="请选择所属公司(可多选)"
                  allowClear
                  showSearch
                  style={{ width: '350px' }}
                />
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 4, span: 8 }}>
                <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '10px' }}>
                  <Button
                    type="primary"
                    danger
                    onClick={handleRangeDeleteSubmit}
                    loading={rangeDeleteSubmitting}
                  >
                    删除
                  </Button>
                  <Button
                    onClick={() => rangeDeleteForm.resetFields()}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染任务中心页签内容
  const renderTaskCenterTab = () => (
    <Row gutter={[24, 24]}>
      <Col span={24}>
        <Card title="任务处理进度">
          <div style={{ padding: '16px 0' }}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Progress
                  percent={taskProgress.percent}
                  status={
                    taskProgress.status === '处理失败' ? 'exception' :
                    taskProgress.status === '已完成' ? 'success' : 'active'
                  }
                />
              </Col>
              <Col span={24}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="总任务数"
                        value={taskProgress.totalTasks}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="已完成/处理中"
                        value={taskProgress.completedTasks}
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="剩余任务"
                        value={taskProgress.remainingTasks}
                        valueStyle={{ color: '#faad14' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </Col>
              <Col span={24}>
                <Alert
                  message={`当前状态: ${taskProgress.status || '处理中'}`}
                  type={
                    taskProgress.status === '处理失败' ? 'error' :
                    taskProgress.status === '已完成' ? 'success' : 'info'
                  }
                  showIcon
                />
              </Col>
            </Row>
          </div>
        </Card>
      </Col>

      {/* 租户处理列表 */}
      <Col span={24}>
        <Card title="租户处理列表">
          <Table
            dataSource={taskProgress.tenantList
              .map((item, index) => ({
                ...item,
                key: index,
              }))}
            rowKey="key"
            sortDirections={['ascend', 'descend']}
            showSorterTooltip={true}
            columns={[
              {
                title: '租户ID',
                dataIndex: 'tenantId',
                key: 'tenantId',
                sorter: (a, b) => (a.tenantId || 0) - (b.tenantId || 0),
              },
              {
                title: '租户名称',
                dataIndex: 'tenantName',
                key: 'tenantName',
                sorter: (a, b) => {
                  const aName = a.tenantName || '';
                  const bName = b.tenantName || '';
                  return aName.localeCompare(bName);
                },
              },
              {
                title: '开始时间',
                dataIndex: 'startTime',
                key: 'startTime',
                sorter: (a, b) => (a.startTime || 0) - (b.startTime || 0),
                render: (text) => {
                  if (!text || isNaN(new Date(text).getTime())) {
                    return '-';
                  }
                  return formatTime(text);
                },
              },
              {
                title: '结束时间',
                dataIndex: 'endTime',
                key: 'endTime',
                sorter: (a, b) => {
                  const aTime = a.endTime || 0;
                  const bTime = b.endTime || 0;
                  return aTime - bTime;
                },
                render: (text) => {
                  if (!text || isNaN(new Date(text).getTime())) {
                    return '-';
                  }
                  return formatTime(text);
                },
              },
              {
                title: '处理耗时',
                dataIndex: 'duration',
                key: 'duration',
                sorter: (a, b) => {
                  const aDuration = a.duration || 0;
                  const bDuration = b.duration || 0;
                  return aDuration - bDuration;
                },
                render: (text) => {
                  if (!text || isNaN(Number(text))) {
                    return '-';
                  }
                  return formatDuration(text);
                },
              },
              {
                title: '点击数',
                dataIndex: 'clickCount',
                key: 'clickCount',
                sorter: (a, b) => (a.clickCount || 0) - (b.clickCount || 0),
                render: (text) => {
                  if (!text || isNaN(Number(text))) {
                    return '-';
                  }
                  return (
                    <Tag color="blue">
                      <BarChartOutlined /> {text}
                    </Tag>
                  );
                },
              },

              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                defaultSortOrder: 'ascend',
                sorter: (a, b) => {
                  const statusOrder: Record<string, number> = { '处理中': 0, '排队中': 1, '已完成': 2 };
                  return (statusOrder[a.status as string] || 999) - (statusOrder[b.status as string] || 999);
                },
                render: (text) => {
                  let color = 'default';
                  switch (text) {
                    case '处理中':
                      color = 'processing';
                      break;
                    case '已完成':
                      color = 'success';
                      break;
                    case '排队中':
                      color = 'warning';
                      break;
                    default:
                      color = 'default';
                  }
                  return (
                    <Tag color={color}>
                      {text}
                    </Tag>
                  );
                },
              },
            ]}
            pagination={false}
            size="middle"
          />
        </Card>
      </Col>
    </Row>
  );

  return (
    <PageContainer title="批量系统补全">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="表格补全" key="upload">
          {renderUploadTab()}
        </TabPane>
        <TabPane tab="范围删除" key="rangeDelete">
          {renderRangeDeleteTab()}
        </TabPane>
        <TabPane tab="任务中心" key="taskCenter">
          {renderTaskCenterTab()}
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default SystemBatchCompletion;
