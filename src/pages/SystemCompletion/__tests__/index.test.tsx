import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Form, message } from 'antd';
import SystemCompletion from '../index';

// Mock dependencies
jest.mock('@/services/corp/tenantConfig', () => ({
  uploadSystemCompletionFiles: jest.fn(),
}));

jest.mock('@/components/TenantSelect', () => {
  return function TenantSelect({ onChange, ...props }: any) {
    return (
      <select 
        data-testid="tenant-select" 
        onChange={(e) => onChange?.(e.target.value)}
        {...props}
      >
        <option value="">请选择公司</option>
        <option value="tenant1">公司1</option>
        <option value="tenant2">公司2</option>
      </select>
    );
  };
});

jest.mock('@/components/CitySelector', () => ({
  FormCitySelector: function FormCitySelector({ onChange, ...props }: any) {
    return (
      <select 
        data-testid="city-selector" 
        onChange={(e) => onChange?.([e.target.value])}
        {...props}
      >
        <option value="">请选择城市</option>
        <option value="110000:北京">北京</option>
        <option value="310000:上海">上海</option>
      </select>
    );
  }
}));

jest.mock('@/constants/cityData', () => ({
  cityData: [
    { code: '110000', name: '北京' },
    { code: '310000', name: '上海' }
  ]
}));

// Mock antd message
jest.mock('antd', () => {
  const originalAntd = jest.requireActual('antd');
  return {
    ...originalAntd,
    message: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
    },
  };
});

describe('SystemCompletion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should preserve form data after successful API call', async () => {
    const { uploadSystemCompletionFiles } = require('@/services/corp/tenantConfig');
    uploadSystemCompletionFiles.mockResolvedValue({ success: true });

    render(<SystemCompletion />);

    // 填写表单数据
    const tenantSelect = screen.getByTestId('tenant-select');
    fireEvent.change(tenantSelect, { target: { value: 'tenant1' } });

    const clicksInput = screen.getByPlaceholderText('请输入点击数');
    fireEvent.change(clicksInput, { target: { value: '1000' } });

    // 选择车险预约
    const carInsuranceCheckbox = screen.getByText('车险预约').closest('label')?.querySelector('input');
    if (carInsuranceCheckbox) {
      fireEvent.click(carInsuranceCheckbox);
    }

    // 提交表单
    const submitButton = screen.getByText('开始补全');
    fireEvent.click(submitButton);

    // 等待API调用完成
    await waitFor(() => {
      expect(uploadSystemCompletionFiles).toHaveBeenCalled();
    });

    // 验证成功消息显示
    expect(message.success).toHaveBeenCalledWith('系统补全任务已提交');

    // 验证表单数据仍然保留
    expect(tenantSelect.value).toBe('tenant1');
    expect(clicksInput.value).toBe('1,000'); // 格式化后的值
  });

  test('should clear form data only when component unmounts', () => {
    const { unmount } = render(<SystemCompletion />);

    // 填写一些数据
    const tenantSelect = screen.getByTestId('tenant-select');
    fireEvent.change(tenantSelect, { target: { value: 'tenant1' } });

    // 验证数据存在
    expect(tenantSelect.value).toBe('tenant1');

    // 卸载组件
    unmount();

    // 重新渲染组件，验证数据被清空
    render(<SystemCompletion />);
    const newTenantSelect = screen.getByTestId('tenant-select');
    expect(newTenantSelect.value).toBe('');
  });
});
