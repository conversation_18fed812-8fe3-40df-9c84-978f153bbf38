// 定义变量
@border-color-base: #d9d9d9;
@primary-color: #1890ff;
@primary-color-hover: #40a9ff;
@background-color-light: #f5f5f5;
@background-color-hover: #e6f7ff;

// 主容器样式
.mainContainer {
  max-width: 1000px;
  margin: 0 auto;

  :global {
    .ant-pro-card-body {
      padding: 16px;
    }
  }
}

// 区块卡片样式
.sectionCard {
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

  :global {
    .ant-pro-card-header {
      min-height: 40px;
      padding: 8px 16px;
    }

    .ant-pro-card-title {
      font-size: 15px;
    }

    .ant-pro-card-body {
      padding: 16px;
    }
  }
}

// 折叠面板样式
.ledgerCollapse {
  background: transparent;

  :global {
    .ant-collapse-item {
      margin-bottom: 8px;
      border: 1px solid @border-color-base;
      border-radius: 4px !important;
      overflow: hidden;

      &:hover {
        border-color: @primary-color-hover;
      }
    }

    .ant-collapse-header {
      padding: 8px 16px !important;
      background-color: #fafafa;
    }

    .ant-collapse-content-box {
      padding: 12px !important;
    }
  }
}

.collapsePanel {
  border-radius: 4px;
}

// 紧凑卡片样式
.compactCard {
  :global {
    .ant-card-body {
      padding: 12px;
    }
  }
}

// 百分比设置区域
.percentSetting {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

// 文件上传相关样式
.uploadedFiles {
  margin-top: 8px;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: @background-color-light;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: @background-color-hover;
  }
}

.fileIcon {
  font-size: 20px;
  margin-right: 8px;
}

// 操作按钮区域
.actionCard {
  background: transparent;
  border: none;

  :global {
    .ant-pro-card-body {
      padding: 16px 0;
    }
  }
}

// 紧凑型表单样式
:global {
  .ant-form-item {
    margin-bottom: 12px;
  }

  .ant-form-item-label > label {
    font-size: 14px;
  }

  .ant-card-body {
    padding: 16px;
  }

  .ant-space {
    flex-wrap: wrap;
  }

  .ant-upload-list {
    margin-top: 8px;
  }

  .ant-upload-list-item {
    margin-top: 4px;
  }

  .ant-input-number {
    width: 80px;
  }
}
