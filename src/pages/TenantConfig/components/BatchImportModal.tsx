import React, { useState } from 'react';
import { Modal, Radio, Upload, Button, message, Space, Alert, Typography } from 'antd';
import { UploadOutlined, InboxOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { uploadBatchDailyConfig, uploadBatchLinkConfig } from '@/services/corp/tenantConfig';

const { Dragger } = Upload;
const { Text } = Typography;

// 导入类型
enum ImportType {
  DAILY = 'daily',
  LINK = 'link'
}

interface BatchImportModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const BatchImportModal: React.FC<BatchImportModalProps> = ({ visible, onCancel, onSuccess }) => {
  // 导入类型
  const [importType, setImportType] = useState<ImportType>(ImportType.DAILY);
  // 上传文件
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  // 上传中状态
  const [uploading, setUploading] = useState<boolean>(false);

  // 处理导入类型变更
  const handleImportTypeChange = (e: any) => {
    setImportType(e.target.value);
    // 清空已选文件
    setFileList([]);
  };

  // 处理文件上传前的检查
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 检查文件类型
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                    file.type === 'application/vnd.ms-excel' ||
                    file.name.endsWith('.xlsx') || 
                    file.name.endsWith('.xls');
    
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return Upload.LIST_IGNORE;
    }
    
    // 检查文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
      return Upload.LIST_IGNORE;
    }
    
    // 更新文件列表
    setFileList([file]);
    
    // 阻止自动上传
    return false;
  };

  // 处理文件列表变化
  const handleChange: UploadProps['onChange'] = (info) => {
    // 如果删除文件
    if (info.fileList.length === 0) {
      setFileList([]);
    }
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    const formData = new FormData();
    formData.append('file', fileList[0] as any);

    setUploading(true);
    try {
      // 根据导入类型调用不同的接口
      let response;
      if (importType === ImportType.DAILY) {
        response = await uploadBatchDailyConfig(formData);
      } else {
        response = await uploadBatchLinkConfig(formData);
      }

      if (response.success) {
        message.success('批量导入成功');
        setFileList([]);
        onSuccess();
        onCancel();
      } else {
        message.error(response.message || '批量导入失败');
      }
    } catch (error) {
      console.error('批量导入出错:', error);
      message.error('批量导入失败，请稍后再试');
    } finally {
      setUploading(false);
    }
  };

  // 获取导入类型说明
  const getImportTypeDescription = () => {
    if (importType === ImportType.DAILY) {
      return (
        <Alert
          message="每日设置批量导入说明"
          description={
            <div>
              <p>1. 请下载每日设置模板，按照模板格式填写数据</p>
              <p>2. 每日设置包含城市列表、点击区间、财险台账、增值服务台账和车险台账等配置</p>
              <p>3. 城市名称会自动处理，无需手动去除"省"或"市"字</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      );
    } else {
      return (
        <Alert
          message="链接设置批量导入说明"
          description={
            <div>
              <p>1. 请下载链接设置模板，按照模板格式填写数据</p>
              <p>2. 链接设置包含链接名称、链接类型、预约日期间隔等配置</p>
              <p>3. 请确保链接类型与系统中的类型一致</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      );
    }
  };

  // 获取模板下载链接
  const getTemplateDownloadLink = () => {
    const token = localStorage.getItem('token') || '';
    if (importType === ImportType.DAILY) {
      return `/corp/daily-config/downloadTemplate?token=${token}`;
    } else {
      return `/corp/link-config/downloadTemplate?token=${token}`;
    }
  };

  return (
    <Modal
      title="批量导入配置"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="download" type="link" href={getTemplateDownloadLink()} target="_blank">
          下载模板
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleUpload}
          loading={uploading}
          disabled={fileList.length === 0}
        >
          开始导入
        </Button>,
      ]}
      width={600}
    >
      <div style={{ marginBottom: 16 }}>
        <Text strong>请选择要导入的配置类型：</Text>
        <Radio.Group value={importType} onChange={handleImportTypeChange} style={{ marginLeft: 16 }}>
          <Radio.Button value={ImportType.DAILY}>每日设置</Radio.Button>
          <Radio.Button value={ImportType.LINK}>链接设置</Radio.Button>
        </Radio.Group>
      </div>

      {getImportTypeDescription()}

      <Dragger
        fileList={fileList}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onRemove={() => setFileList([])}
        multiple={false}
        accept=".xlsx,.xls"
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持单个Excel文件上传，文件大小不超过10MB
        </p>
      </Dragger>
    </Modal>
  );
};

export default BatchImportModal;
