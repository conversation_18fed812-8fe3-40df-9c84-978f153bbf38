import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Upload, Button, message, Typography, Space, Card, Switch, Badge, Tooltip, Tag } from 'antd';
import { UploadOutlined, InboxOutlined, QuestionCircleOutlined, CarOutlined, BankOutlined, GiftOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { getTenantById, editTenant } from '@/services/corp/tenantConfig';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface CategorySettingsTabProps {
  tenantId: number;
  tenantName: string;
}

const CategorySettingsTab: React.FC<CategorySettingsTabProps> = ({ tenantId, tenantName }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isTop, setIsTop] = useState<number>(2); // 默认为2（未上架）
  const [statusText, setStatusText] = useState<string>('上架成功');

  // 获取租户信息
  useEffect(() => {
    // 允许tenantId为0，但不允许为null或undefined
    if (tenantId === 0 || tenantId) {
      console.log(`公司信息设置: 租户ID变化为 ${tenantId}，开始获取配置`);

      // 调用接口获取租户信息
      const fetchTenantInfo = async () => {
        try {
          const response = await getTenantById(tenantId);
          console.log('获取租户信息响应:', response);

          if (response.success && response.result) {
            const { companyAddress, companyLogo, busType, isTop, intro } = response.result;

            // 处理业务类型，将逗号分隔的字符串转换为数组
            const businessTypes = busType ? busType.split(',').map(item => parseInt(item, 10)) : [];

            // 设置isTop状态
            const isTopValue = isTop ? parseInt(isTop, 10) : 2; // 默认为2（未上架）
            setIsTop(isTopValue);
            setStatusText(isTopValue === 1 ? '上架中' : '上架成功');

            // 设置表单值
            form.setFieldsValue({
              companyAddress,
              intro,
              isTop: isTopValue === 1,
              businessTypes
            });

            // 如果有logo，设置logo
            if (companyLogo) {
              // 这里可以根据实际情况处理logo的显示
              // 例如，可以设置一个文件列表来显示logo
            }
          } else {
            // 如果获取失败或者没有数据，设置默认值
            setIsTop(2); // 默认为已上架（2）
            setStatusText('上架成功');
            form.resetFields();
          }
        } catch (error) {
          console.error('获取租户信息出错:', error);
          message.error('获取公司信息失败');
          // 出错时设置默认值
          setIsTop(2); // 默认为已上架（2）
          setStatusText('上架成功');
          form.resetFields();
        }
      };

      fetchTenantInfo();
    }
  }, [tenantId, form]);

  // 处理小程序上架状态切换
  const handleTopStatusChange = (checked: boolean) => {
    // 只更新本地状态，不显示提示，等用户点击保存按钮时才提交
    const newValue = checked ? 1 : 2;
    setIsTop(newValue);
    setStatusText(newValue === 1 ? '上架中' : '上架成功');
    // 更新表单字段值，确保表单与状态一致
    form.setFieldValue('isTop', checked);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 转换isTop的值
      const formIsTop = values.isTop ? 1 : 2;

      // 获取业务类型数组并转换为逗号分隔的字符串
      const businessTypes = values.businessTypes || [];
      const busType = businessTypes.join(',');

      // 构建请求数据
      const requestData = {
        id: tenantId,
        companyAddress: values.companyAddress || '',
        companyLogo: '', // 这里需要根据实际情况处理logo的上传
        busType,
        isTop: formIsTop.toString(), // 转换为字符串
        intro: values.intro || ''
      };

      console.log('提交的公司信息数据:', requestData);

      // 调用编辑租户信息接口
      const response = await editTenant(requestData);

      if (response.success) {
        message.success('公司信息设置保存成功');
        // 保存成功后更新状态
        setIsTop(formIsTop);
        setStatusText(formIsTop === 1 ? '上架中' : '上架成功');
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败，请检查输入');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理文件上传前的检查
  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于2MB!');
      return Upload.LIST_IGNORE;
    }

    return false;
  };

  // 处理文件上传变化
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 自定义上传操作
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);

    try {
      // 这里模拟上传成功，实际项目中应该调用API上传文件
      setTimeout(() => {
        onSuccess("上传成功");
        message.success(`${file.name} 文件上传成功`);
        setUploading(false);
      }, 1000);
    } catch (err) {
      onError();
      message.error('文件上传失败');
      setUploading(false);
    }
  };

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          businessTypes: [],
          intro: '',
          companyAddress: '',
        }}
      >
        <ProCard
          title={<>
            <span>公司基本信息</span>
            <Text type="secondary" style={{ fontSize: 14, fontWeight: 'normal', marginLeft: 8 }}>（所有字段均为可选）</Text>
          </>}
          headerBordered
          style={{ marginBottom: 24 }}
        >
          <div style={{ maxWidth: 600 }}>
            <Form.Item
              name="businessTypes"
              label={<>业务类型 <Text type="secondary">（可多选）</Text></>}
            >
              <Select
                mode="multiple"
                placeholder="请选择业务类型"
                optionLabelProp="label"
                style={{ width: '100%' }}
              >
                <Option value={0} label="车险">
                  <Space>
                    <CarOutlined style={{ color: '#1890ff' }} />
                    <span>车险</span>
                  </Space>
                </Option>
                <Option value={1} label="财险">
                  <Space>
                    <BankOutlined style={{ color: '#52c41a' }} />
                    <span>财险</span>
                  </Space>
                </Option>
                <Option value={2} label="增值服务">
                  <Space>
                    <GiftOutlined style={{ color: '#fa8c16' }} />
                    <span>增值服务</span>
                  </Space>
                </Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="companyAddress"
              label={<>公司地址 <Text type="secondary">（可选）</Text></>}
            >
              <Input placeholder="输入公司地址" />
            </Form.Item>

            <Form.Item
              name="intro"
              label={<>公司简介 <Text type="secondary">（可选）</Text></>}
            >
              <TextArea
                rows={4}
                placeholder="输入公司简介..."
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item
              name="logo"
              label={<>公司Logo <Text type="secondary">（可选）</Text></>}
            >
              <Upload
                name="logo"
                listType="picture"
                maxCount={1}
                fileList={fileList}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                customRequest={customUpload}
              >
                <div style={{
                  border: '1px dashed #d9d9d9',
                  padding: 20,
                  textAlign: 'center',
                  backgroundColor: '#fafafa',
                  borderRadius: 4
                }}>
                  <p style={{ marginBottom: 10, color: '#888' }}>点击或拖拽上传Logo</p>
                  <Button icon={<UploadOutlined />} loading={uploading}>
                    选择文件
                  </Button>
                  <p style={{ marginTop: 8, color: '#888', fontSize: 12 }}>
                    支持 JPG, PNG 格式，文件大小不超过2MB
                  </p>
                </div>
              </Upload>
            </Form.Item>

            <Form.Item
              name="isTop"
              label={
                <>
                  小程序上架状态
                  <Tooltip title="控制小程序是否上架展示，开启表示上架中(1)，关闭表示已上架(2)">
                    <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </>
              }
              valuePropName="checked"
            >
              <div>
                <Switch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  checked={isTop === 1}
                  onChange={handleTopStatusChange}
                />
                <div style={{ marginTop: 8 }}>
                  <Badge
                    status={isTop === 1 ? "processing" : "success"}
                    text={<Text type={isTop === 1 ? "warning" : "success"}>{statusText}</Text>}
                  />
                </div>
              </div>
            </Form.Item>
          </div>
        </ProCard>

        <ProCard bordered style={{ marginTop: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text type="secondary">最后更新时间: {new Date().toLocaleString()}</Text>
            </div>
            <Space size="middle">
              <Button onClick={() => form.resetFields()}>取消</Button>
              <Button type="primary" onClick={handleSubmit} loading={submitting}>
                保存公司信息
              </Button>
            </Space>
          </div>
        </ProCard>
      </Form>
    </div>
  );
};

export default CategorySettingsTab;
