import React, { useState, useEffect } from 'react';
import { Form, Button, Tabs, Typography, message, Spin, Alert, Tooltip, Space } from 'antd';
import { LinkOutlined, CarOutlined, BankOutlined, GiftOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { getGenerateConfig, saveGenerateConfig } from '@/services/corp/tenantConfig';
import type { GenerateConfig, MetricGroup } from '../data.d';
import MetricGroup from './MetricGroup';
import {
  generateCarInsuranceRules,
  generatePropertyInsuranceRules,
  generateValueAddedServiceRules,
  normalizeRules
} from '../utils/randomGenerator';

const { Text } = Typography;
const { TabPane } = Tabs;

interface LinkSettingsTabProps {
  tenantId: number;
  tenantName: string;
}

// 定义指标分组
const metricGroups: MetricGroup[] = [
  {
    title: '流量指标',
    key: 'traffic',
    metrics: [
      {
        label: '独立查询数',
        startKey: 'uniqueQueryStart',
        endKey: 'uniqueQueryEnd',
        min: 0,
        max: 10000,
        step: 10,
      },
    ],
  },
  {
    title: '转化指标',
    key: 'conversion',
    metrics: [
      {
        label: '转化率',
        startKey: 'conversionRateStart',
        endKey: 'conversionRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '表单提交数',
        startKey: 'formSubmissionsStart',
        endKey: 'formSubmissionsEnd',
        min: 0,
        max: 10000,
        step: 10,
      },
      {
        label: 'CTR (点击率)',
        startKey: 'ctrStart',
        endKey: 'ctrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '首屏CTR',
        startKey: 'firstScreenCtrStart',
        endKey: 'firstScreenCtrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: 'TOP3 PV-CTR',
        startKey: 'top3PvCtrStart',
        endKey: 'top3PvCtrEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
  {
    title: '用户行为指标',
    key: 'behavior',
    metrics: [
      {
        label: '跳出率',
        startKey: 'bounceRateStart',
        endKey: 'bounceRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '平均停留时长',
        startKey: 'avgStayTimeStart',
        endKey: 'avgStayTimeEnd',
        unit: '秒',
        min: 0,
        max: 3600,
        step: 1,
      },
      {
        label: '返回率',
        startKey: 'returnRateStart',
        endKey: 'returnRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容完成率',
        startKey: 'completionRateStart',
        endKey: 'completionRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容跳出率',
        startKey: 'contentJumpRateStart',
        endKey: 'contentJumpRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '内容返回率',
        startKey: 'contentReturnRateStart',
        endKey: 'contentReturnRateEnd',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
  {
    title: '交互指标',
    key: 'interaction',
    metrics: [
      {
        label: '点击深度',
        startKey: 'clickDepthStart',
        endKey: 'clickDepthEnd',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '点击间隔时间',
        startKey: 'clickIntervalTimeStart',
        endKey: 'clickIntervalTimeEnd',
        unit: '秒',
        min: 0,
        max: 3600,
        step: 0.1,
        precision: 1,
      },
      {
        label: 'Engagement得分',
        startKey: 'engagementScoreStart',
        endKey: 'engagementScoreEnd',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
      {
        label: '翻页率',
        startKey: 'pageStartNum',
        endKey: 'pageEndNum',
        unit: '%',
        min: 0,
        max: 100,
        step: 0.1,
        precision: 1,
      },
    ],
  },
];

// 链接类型配置
const linkTypes = [
  { key: '0', name: '车险', icon: <CarOutlined /> },
  { key: '1', name: '财险', icon: <BankOutlined /> },
  { key: '2', name: '增值服务', icon: <GiftOutlined /> },
];

const LinkSettingsTab: React.FC<LinkSettingsTabProps> = ({ tenantId, tenantName }) => {
  // 创建三个表单实例，分别对应三种链接类型
  const [carForm] = Form.useForm();
  const [propertyForm] = Form.useForm();
  const [valueAddedForm] = Form.useForm();

  const [activeKey, setActiveKey] = useState('0'); // 默认选中车险
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [configs, setConfigs] = useState<GenerateConfig[]>([]);

  // 获取当前表单实例
  const getCurrentForm = () => {
    switch (activeKey) {
      case '0': return carForm;
      case '1': return propertyForm;
      case '2': return valueAddedForm;
      default: return carForm;
    }
  };

  // 随机生成车险链接规则
  const handleGenerateCarRules = () => {
    if (activeKey !== '0') {
      message.warning('请先切换到车险标签页');
      return;
    }

    // 生成随机规则
    const randomRules = generateCarInsuranceRules();

    // 确保结束值大于起始值
    const normalizedRules = normalizeRules(randomRules);

    // 设置表单值
    carForm.setFieldsValue(normalizedRules);

    message.success('已随机生成车险链接规则');
  };

  // 随机生成财险链接规则
  const handleGeneratePropertyRules = () => {
    if (activeKey !== '1') {
      message.warning('请先切换到财险标签页');
      return;
    }

    // 生成随机规则
    const randomRules = generatePropertyInsuranceRules();

    // 确保结束值大于起始值
    const normalizedRules = normalizeRules(randomRules);

    // 设置表单值
    propertyForm.setFieldsValue(normalizedRules);

    message.success('已随机生成财险链接规则');
  };

  // 随机生成增值服务链接规则
  const handleGenerateValueAddedRules = () => {
    if (activeKey !== '2') {
      message.warning('请先切换到增值服务标签页');
      return;
    }

    // 生成随机规则
    const randomRules = generateValueAddedServiceRules();

    // 确保结束值大于起始值
    const normalizedRules = normalizeRules(randomRules);

    // 设置表单值
    valueAddedForm.setFieldsValue(normalizedRules);

    message.success('已随机生成增值服务链接规则');
  };

  // 获取配置数据
  const fetchConfigs = async () => {
    if (tenantId === null || tenantId === undefined) return;

    setLoading(true);
    try {
      const response = await getGenerateConfig(tenantId);
      console.log('获取链接生成规则配置响应:', response);

      if (response.success) {
        const configList = response.result || [];
        setConfigs(configList);

        // 初始化表单数据
        configList.forEach((config: GenerateConfig) => {
          const form = config.linkType === 0 ? carForm :
                      config.linkType === 1 ? propertyForm : valueAddedForm;

          form.setFieldsValue({
            ...config,
          });
        });
      } else {
        message.error(response.message || '获取链接生成规则配置失败');
      }
    } catch (error) {
      console.error('获取链接生成规则配置出错:', error);
      message.error('获取链接生成规则配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 当租户ID变化时重新加载数据
  useEffect(() => {
    if (tenantId === 0 || tenantId) {
      console.log(`链接设置: 租户ID变化为 ${tenantId}，开始获取配置`);
      fetchConfigs();
    }
  }, [tenantId]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      // 获取当前激活的表单
      const form = getCurrentForm();
      const values = await form.validateFields();

      setSubmitting(true);
      console.log('提交的链接设置数据:', values);

      // 构建请求数据
      const requestData = {
        ...values,
        tenantId,
        linkType: parseInt(activeKey, 10),
      };

      // 发送请求
      const response = await saveGenerateConfig(requestData);

      if (response.success) {
        message.success('链接设置保存成功');
        // 重新加载数据
        fetchConfigs();
      } else {
        message.error(response.message || '保存链接设置失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败，请检查输入');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    return (
      <Spin spinning={loading}>
        {metricGroups.map(group => (
          <MetricGroup
            key={group.key}
            group={group}
            disabled={loading || submitting}
          />
        ))}
      </Spin>
    );
  };

  return (
    <div>
      <Alert
        message="链接生成规则配置"
        description="配置不同链接类型的生成规则参数，包括流量指标、转化指标、用户行为指标和交互指标等。每个指标包含起始值和结束值，用于设置指标的有效范围。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          tabPosition="top"
          type="card"
          style={{ marginBottom: 0, flex: 1 }}
          className="tabs-container"
          styles={{
            nav: {
              marginBottom: 12,
            },
            tab: {
              padding: '8px 16px',
            },
          }}
        >
        {linkTypes.map(type => (
          <TabPane
            tab={
              <span>
                {type.icon}
                <span style={{ marginLeft: 8 }}>{type.name}</span>
              </span>
            }
            key={type.key}
          >
            <Form
              form={type.key === '0' ? carForm : type.key === '1' ? propertyForm : valueAddedForm}
              layout="vertical"
              initialValues={{}}
            >
              {renderFormContent()}
            </Form>
          </TabPane>
        ))}
      </Tabs>
      </div>

      {activeKey === '0' && (
        <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
          <Tooltip title="随机生成车险链接规则">
            <Button
              type="primary"
              icon={<ThunderboltOutlined />}
              onClick={handleGenerateCarRules}
              disabled={loading || submitting}
            >
              随机生成车险规则
            </Button>
          </Tooltip>
        </div>
      )}

      {activeKey === '1' && (
        <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
          <Tooltip title="随机生成财险链接规则">
            <Button
              type="primary"
              icon={<ThunderboltOutlined />}
              onClick={handleGeneratePropertyRules}
              disabled={loading || submitting}
            >
              随机生成财险规则
            </Button>
          </Tooltip>
        </div>
      )}

      {activeKey === '2' && (
        <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
          <Tooltip title="随机生成增值服务链接规则">
            <Button
              type="primary"
              icon={<ThunderboltOutlined />}
              onClick={handleGenerateValueAddedRules}
              disabled={loading || submitting}
            >
              随机生成增值服务规则
            </Button>
          </Tooltip>
        </div>
      )}

      <ProCard bordered style={{ marginTop: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Text type="secondary">
              当前配置: {tenantName} - {linkTypes.find(type => type.key === activeKey)?.name}
            </Text>
          </div>
          <div>
            <Button
              onClick={() => getCurrentForm().resetFields()}
              style={{ marginRight: 16 }}
              disabled={loading || submitting}
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<LinkOutlined />}
              onClick={handleSubmit}
              loading={submitting}
              disabled={loading}
            >
              保存链接设置
            </Button>
          </div>
        </div>
      </ProCard>
    </div>
  );
};

export default LinkSettingsTab;
