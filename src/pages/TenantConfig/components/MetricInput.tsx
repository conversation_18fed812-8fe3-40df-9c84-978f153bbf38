import React from 'react';
import { Form, InputNumber, Card, Row, Col, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { Metric } from '../data.d';

const { Text } = Typography;

interface MetricInputProps {
  metric: Metric;
  disabled?: boolean;
}

/**
 * 指标输入组件
 * 包含起始值和结束值两个输入框
 */
const MetricInput: React.FC<MetricInputProps> = ({ metric, disabled }) => {
  const {
    label,
    startKey,
    endKey,
    unit = '',
    min = 0,
    max = 100000,
    step = 1,
    precision = 0
  } = metric;

  return (
    <Card
      size="small"
      title={
        <span>
          {label}
          <Tooltip title={`设置${label}的起始值和结束值`}>
            <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
          </Tooltip>
        </span>
      }
      style={{ marginBottom: 12 }}
      bodyStyle={{ padding: '8px 12px' }}
    >
      <Row gutter={16} align="middle">
        <Col span={11}>
          <Form.Item
            name={startKey}
            label="起始值"
            rules={[{ required: true, message: '请输入起始值' }]}
          >
            <InputNumber
              disabled={disabled}
              style={{ width: '100%' }}
              min={min}
              max={max}
              step={step}
              precision={precision}
              addonAfter={unit}
              controls={false}
            />
          </Form.Item>
        </Col>
        <Col span={2} style={{ textAlign: 'center' }}>
          <span style={{ color: '#999' }}>至</span>
        </Col>
        <Col span={11}>
          <Form.Item
            name={endKey}
            label="结束值"
            rules={[{ required: true, message: '请输入结束值' }]}
          >
            <InputNumber
              disabled={disabled}
              style={{ width: '100%' }}
              min={min}
              max={max}
              step={step}
              precision={precision}
              addonAfter={unit}
              controls={false}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );
};

export default MetricInput;
