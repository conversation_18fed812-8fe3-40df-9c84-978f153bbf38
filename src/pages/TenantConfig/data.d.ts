// 租户数据类型
export interface Tenant {
  id: number;
  name: string;
  status: number;
  tenantType?: number;
  [key: string]: any;
}

// 生成规则配置数据类型
export interface GenerateConfig {
  id?: string;
  tenantId: number;
  linkType: number;



  // 跳出率相关
  bounceRateStart: number;
  bounceRateEnd: number;
  bounceRateRate: number;

  // 转化率相关
  conversionRateStart: number;
  conversionRateEnd: number;
  conversionRateRate: number;

  // 表单提交数相关
  formSubmissionsStart: number;
  formSubmissionsEnd: number;
  formSubmissionsRate: number;

  // CTR相关
  ctrStart: number;
  ctrEnd: number;
  ctrRate: number;

  // 平均停留时长相关
  avgStayTimeStart: number;
  avgStayTimeEnd: number;
  avgStayTimeRate: number;

  // 返回率相关
  returnRateStart: number;
  returnRateEnd: number;
  returnRateRate: number;

  // 内容完成率相关
  completionRateStart: number;
  completionRateEnd: number;
  completionRateRate: number;

  // 首屏CTR相关
  firstScreenCtrStart: number;
  firstScreenCtrEnd: number;
  firstScreenCtrRate: number;

  // 内容跳出率相关
  contentJumpRateStart: number;
  contentJumpRateEnd: number;
  contentJumpRateRate: number;

  // 内容返回率相关
  contentReturnRateStart: number;
  contentReturnRateEnd: number;
  contentReturnRateRate: number;

  // 点击深度相关
  clickDepthStart: number;
  clickDepthEnd: number;
  clickDepthRate: number;

  // 点击间隔时间相关
  clickIntervalTimeStart: number;
  clickIntervalTimeEnd: number;
  clickIntervalTimeRate: number;

  // Engagement得分相关
  engagementScoreStart: number;
  engagementScoreEnd: number;
  engagementScoreRate: number;

  // 翻页率相关
  pageStartNum: number;
  pageEndNum: number;
  pageRate: number;

  // 独立查询数相关
  uniqueQueryStart: number;
  uniqueQueryEnd: number;
  uniqueQueryRate: number;

  // TOP3 PV-CTR相关
  top3PvCtrStart: number;
  top3PvCtrEnd: number;
  top3PvCtrRate: number;

  // 其他字段
  [key: string]: any;
}

// 指标分组
export interface MetricGroup {
  title: string;
  key: string;
  metrics: Metric[];
}

// 指标定义
export interface Metric {
  label: string;
  startKey: string;
  endKey: string;
  unit?: string;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
}
