/**
 * 随机生成区间值的工具函数
 */

// 生成指定范围内的随机整数
export const getRandomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// 生成指定范围内的随机浮点数，保留指定位数的小数
export const getRandomFloat = (min: number, max: number, decimals: number = 1): number => {
  const value = Math.random() * (max - min) + min;
  const multiplier = Math.pow(10, decimals);
  return Math.floor(value * multiplier) / multiplier;
};

// 确保结束值大于起始值
export const ensureEndGreaterThanStart = (start: number, end: number, min: number, max: number): [number, number] => {
  if (end <= start) {
    // 如果结束值小于等于起始值，重新生成一个更大的结束值
    const newEnd = Math.min(start + getRandomInt(1, Math.floor((max - start) / 2)), max);
    return [start, newEnd];
  }
  return [start, end];
};

// 车险链接规则的随机生成配置
export const generateCarInsuranceRules = () => {
  return {
    // 流量指标
    uniqueQueryStart: getRandomInt(500, 1500),
    uniqueQueryEnd: getRandomInt(1500, 5000),

    // 转化指标
    conversionRateStart: getRandomFloat(1.0, 3.0),
    conversionRateEnd: getRandomFloat(3.0, 8.0),

    formSubmissionsStart: getRandomInt(100, 300),
    formSubmissionsEnd: getRandomInt(300, 800),

    ctrStart: getRandomFloat(2.0, 5.0),
    ctrEnd: getRandomFloat(5.0, 12.0),

    firstScreenCtrStart: getRandomFloat(3.0, 8.0),
    firstScreenCtrEnd: getRandomFloat(8.0, 15.0),

    top3PvCtrStart: getRandomFloat(5.0, 10.0),
    top3PvCtrEnd: getRandomFloat(10.0, 20.0),

    // 用户行为指标
    bounceRateStart: getRandomFloat(20.0, 40.0),
    bounceRateEnd: getRandomFloat(40.0, 60.0),

    avgStayTimeStart: getRandomInt(60, 120),
    avgStayTimeEnd: getRandomInt(120, 300),

    returnRateStart: getRandomFloat(10.0, 20.0),
    returnRateEnd: getRandomFloat(20.0, 40.0),

    completionRateStart: getRandomFloat(30.0, 50.0),
    completionRateEnd: getRandomFloat(50.0, 80.0),

    contentJumpRateStart: getRandomFloat(15.0, 30.0),
    contentJumpRateEnd: getRandomFloat(30.0, 50.0),

    contentReturnRateStart: getRandomFloat(5.0, 15.0),
    contentReturnRateEnd: getRandomFloat(15.0, 30.0),

    // 交互指标
    clickDepthStart: getRandomFloat(2.0, 4.0),
    clickDepthEnd: getRandomFloat(4.0, 8.0),

    clickIntervalTimeStart: getRandomFloat(5.0, 15.0),
    clickIntervalTimeEnd: getRandomFloat(15.0, 40.0),

    engagementScoreStart: getRandomFloat(30.0, 50.0),
    engagementScoreEnd: getRandomFloat(50.0, 80.0),

    pageStartNum: getRandomFloat(10.0, 20.0),
    pageEndNum: getRandomFloat(20.0, 40.0),
  };
};

// 财险链接规则的随机生成配置
export const generatePropertyInsuranceRules = () => {
  return {
    // 流量指标
    uniqueQueryStart: getRandomInt(400, 1200),
    uniqueQueryEnd: getRandomInt(1200, 4000),

    // 转化指标
    conversionRateStart: getRandomFloat(1.5, 3.5),
    conversionRateEnd: getRandomFloat(3.5, 9.0),

    formSubmissionsStart: getRandomInt(80, 250),
    formSubmissionsEnd: getRandomInt(250, 700),

    ctrStart: getRandomFloat(2.5, 5.5),
    ctrEnd: getRandomFloat(5.5, 13.0),

    firstScreenCtrStart: getRandomFloat(3.5, 8.5),
    firstScreenCtrEnd: getRandomFloat(8.5, 16.0),

    top3PvCtrStart: getRandomFloat(5.5, 10.5),
    top3PvCtrEnd: getRandomFloat(10.5, 21.0),

    // 用户行为指标
    bounceRateStart: getRandomFloat(22.0, 42.0),
    bounceRateEnd: getRandomFloat(42.0, 62.0),

    avgStayTimeStart: getRandomInt(65, 125),
    avgStayTimeEnd: getRandomInt(125, 310),

    returnRateStart: getRandomFloat(11.0, 21.0),
    returnRateEnd: getRandomFloat(21.0, 41.0),

    completionRateStart: getRandomFloat(32.0, 52.0),
    completionRateEnd: getRandomFloat(52.0, 82.0),

    contentJumpRateStart: getRandomFloat(16.0, 31.0),
    contentJumpRateEnd: getRandomFloat(31.0, 51.0),

    contentReturnRateStart: getRandomFloat(6.0, 16.0),
    contentReturnRateEnd: getRandomFloat(16.0, 31.0),

    // 交互指标
    clickDepthStart: getRandomFloat(2.2, 4.2),
    clickDepthEnd: getRandomFloat(4.2, 8.2),

    clickIntervalTimeStart: getRandomFloat(5.5, 15.5),
    clickIntervalTimeEnd: getRandomFloat(15.5, 41.0),

    engagementScoreStart: getRandomFloat(32.0, 52.0),
    engagementScoreEnd: getRandomFloat(52.0, 82.0),

    pageStartNum: getRandomFloat(11.0, 21.0),
    pageEndNum: getRandomFloat(21.0, 41.0),
  };
};

// 增值服务链接规则的随机生成配置
export const generateValueAddedServiceRules = () => {
  return {
    // 流量指标
    uniqueQueryStart: getRandomInt(300, 1000),
    uniqueQueryEnd: getRandomInt(1000, 3500),

    // 转化指标
    conversionRateStart: getRandomFloat(2.0, 4.0),
    conversionRateEnd: getRandomFloat(4.0, 10.0),

    formSubmissionsStart: getRandomInt(60, 200),
    formSubmissionsEnd: getRandomInt(200, 600),

    ctrStart: getRandomFloat(3.0, 6.0),
    ctrEnd: getRandomFloat(6.0, 14.0),

    firstScreenCtrStart: getRandomFloat(4.0, 9.0),
    firstScreenCtrEnd: getRandomFloat(9.0, 17.0),

    top3PvCtrStart: getRandomFloat(6.0, 11.0),
    top3PvCtrEnd: getRandomFloat(11.0, 22.0),

    // 用户行为指标
    bounceRateStart: getRandomFloat(24.0, 44.0),
    bounceRateEnd: getRandomFloat(44.0, 64.0),

    avgStayTimeStart: getRandomInt(70, 130),
    avgStayTimeEnd: getRandomInt(130, 320),

    returnRateStart: getRandomFloat(12.0, 22.0),
    returnRateEnd: getRandomFloat(22.0, 42.0),

    completionRateStart: getRandomFloat(34.0, 54.0),
    completionRateEnd: getRandomFloat(54.0, 84.0),

    contentJumpRateStart: getRandomFloat(17.0, 32.0),
    contentJumpRateEnd: getRandomFloat(32.0, 52.0),

    contentReturnRateStart: getRandomFloat(7.0, 17.0),
    contentReturnRateEnd: getRandomFloat(17.0, 32.0),

    // 交互指标
    clickDepthStart: getRandomFloat(2.4, 4.4),
    clickDepthEnd: getRandomFloat(4.4, 8.4),

    clickIntervalTimeStart: getRandomFloat(6.0, 16.0),
    clickIntervalTimeEnd: getRandomFloat(16.0, 42.0),

    engagementScoreStart: getRandomFloat(34.0, 54.0),
    engagementScoreEnd: getRandomFloat(54.0, 84.0),

    pageStartNum: getRandomFloat(12.0, 22.0),
    pageEndNum: getRandomFloat(22.0, 42.0),
  };
};

// 确保所有区间值的结束值大于起始值
export const normalizeRules = (rules: any) => {
  const normalized = { ...rules };

  // 流量指标
  [normalized.uniqueQueryStart, normalized.uniqueQueryEnd] = ensureEndGreaterThanStart(
    normalized.uniqueQueryStart, normalized.uniqueQueryEnd, 100, 10000
  );

  // 转化指标
  [normalized.conversionRateStart, normalized.conversionRateEnd] = ensureEndGreaterThanStart(
    normalized.conversionRateStart, normalized.conversionRateEnd, 0.1, 20.0
  );

  [normalized.formSubmissionsStart, normalized.formSubmissionsEnd] = ensureEndGreaterThanStart(
    normalized.formSubmissionsStart, normalized.formSubmissionsEnd, 10, 2000
  );

  [normalized.ctrStart, normalized.ctrEnd] = ensureEndGreaterThanStart(
    normalized.ctrStart, normalized.ctrEnd, 0.1, 30.0
  );

  [normalized.firstScreenCtrStart, normalized.firstScreenCtrEnd] = ensureEndGreaterThanStart(
    normalized.firstScreenCtrStart, normalized.firstScreenCtrEnd, 0.1, 30.0
  );

  [normalized.top3PvCtrStart, normalized.top3PvCtrEnd] = ensureEndGreaterThanStart(
    normalized.top3PvCtrStart, normalized.top3PvCtrEnd, 0.1, 40.0
  );

  // 用户行为指标
  [normalized.bounceRateStart, normalized.bounceRateEnd] = ensureEndGreaterThanStart(
    normalized.bounceRateStart, normalized.bounceRateEnd, 5.0, 90.0
  );

  [normalized.avgStayTimeStart, normalized.avgStayTimeEnd] = ensureEndGreaterThanStart(
    normalized.avgStayTimeStart, normalized.avgStayTimeEnd, 10, 600
  );

  [normalized.returnRateStart, normalized.returnRateEnd] = ensureEndGreaterThanStart(
    normalized.returnRateStart, normalized.returnRateEnd, 1.0, 80.0
  );

  [normalized.completionRateStart, normalized.completionRateEnd] = ensureEndGreaterThanStart(
    normalized.completionRateStart, normalized.completionRateEnd, 10.0, 95.0
  );

  [normalized.contentJumpRateStart, normalized.contentJumpRateEnd] = ensureEndGreaterThanStart(
    normalized.contentJumpRateStart, normalized.contentJumpRateEnd, 5.0, 80.0
  );

  [normalized.contentReturnRateStart, normalized.contentReturnRateEnd] = ensureEndGreaterThanStart(
    normalized.contentReturnRateStart, normalized.contentReturnRateEnd, 1.0, 60.0
  );

  // 交互指标
  [normalized.clickDepthStart, normalized.clickDepthEnd] = ensureEndGreaterThanStart(
    normalized.clickDepthStart, normalized.clickDepthEnd, 1.0, 20.0
  );

  [normalized.clickIntervalTimeStart, normalized.clickIntervalTimeEnd] = ensureEndGreaterThanStart(
    normalized.clickIntervalTimeStart, normalized.clickIntervalTimeEnd, 1.0, 120.0
  );

  [normalized.engagementScoreStart, normalized.engagementScoreEnd] = ensureEndGreaterThanStart(
    normalized.engagementScoreStart, normalized.engagementScoreEnd, 10.0, 95.0
  );

  [normalized.pageStartNum, normalized.pageEndNum] = ensureEndGreaterThanStart(
    normalized.pageStartNum, normalized.pageEndNum, 1.0, 80.0
  );

  return normalized;
};
