import React, { useState } from 'react';
import { Form, Button, Card, message, Space } from 'antd';
import { FormBusinessCitySelector } from '@/components/BusinessCitySelector';

/**
 * 业务城市选择器测试页面
 */
const BusinessCitySelectorTest: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      console.log('表单值:', values);
      message.success('提交成功！请查看控制台输出');
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
  };

  const handleSetTestData = () => {
    // 设置一些测试数据
    form.setFieldsValue({
      businessCities: ['110000', '310000', '440100'] // 北京、上海、广州的编码
    });
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="业务城市选择器测试" style={{ maxWidth: 800 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <FormBusinessCitySelector
            name="businessCities"
            label="业务城市"
            placeholder="请选择业务开展的城市"
            showSelected={true}
            required={true}
          />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                提交测试
              </Button>
              <Button onClick={handleReset}>
                重置表单
              </Button>
              <Button onClick={handleSetTestData}>
                设置测试数据
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default BusinessCitySelectorTest;
