import React, { useState } from 'react';
import { Form, But<PERSON>, Card, message, Space, Divider } from 'antd';
import { FormBusinessTypeSelector } from '@/components/BusinessTypeSelector';

/**
 * 业务类型选择器测试页面
 */
const BusinessTypeSelectorTest: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      console.log('表单值:', values);
      message.success('提交成功！请查看控制台输出');
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
  };

  const handleSetTestData = () => {
    // 设置一些测试数据
    form.setFieldsValue({
      businessTypes: [0, 2] // 车险和增值服务
    });
  };

  const handleSetAllTypes = () => {
    // 设置所有业务类型
    form.setFieldsValue({
      businessTypes: [0, 1, 2] // 车险、财险、增值服务
    });
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="业务类型选择器测试" style={{ maxWidth: 800 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <FormBusinessTypeSelector
            name="businessTypes"
            label="业务类型"
            showSelected={true}
            required={true}
          />

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                提交测试
              </Button>
              <Button onClick={handleReset}>
                重置表单
              </Button>
              <Button onClick={handleSetTestData}>
                设置测试数据
              </Button>
              <Button onClick={handleSetAllTypes}>
                选择全部类型
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Divider />

        <div style={{ backgroundColor: '#f5f5f5', padding: 16, borderRadius: 6 }}>
          <h4>业务类型说明：</h4>
          <ul>
            <li><strong>0 - 车险：</strong>机动车保险业务</li>
            <li><strong>1 - 财险：</strong>财产保险业务</li>
            <li><strong>2 - 增值服务：</strong>保险相关增值服务</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default BusinessTypeSelectorTest;
