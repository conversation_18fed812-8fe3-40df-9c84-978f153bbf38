import React, { useState, useEffect } from 'react';
import { Card, Button, message, Space, List, Input } from 'antd';
import { getUserList } from '@/services/system/user';

const SimpleUserTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [keyword, setKeyword] = useState('');

  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('发送请求，参数:', { keyword, pageNo: 1, pageSize: 10 });
      
      const response = await getUserList({
        keyword: keyword.trim(),
        pageNo: 1,
        pageSize: 10,
      });

      console.log('接口响应:', response);

      if (response && response.success) {
        let userData = [];
        if (response.result) {
          if (response.result.records) {
            // 新的分页格式
            userData = response.result.records;
          } else if (Array.isArray(response.result)) {
            // 旧的数组格式
            userData = response.result;
          }
        }
        
        console.log('处理后的用户数据:', userData);
        setUsers(userData);
        
        if (userData.length === 0) {
          message.info('没有找到用户数据');
        } else {
          message.success(`成功获取 ${userData.length} 个用户`);
        }
      } else {
        console.error('接口返回失败:', response);
        message.error(response?.message || '获取用户列表失败');
        setUsers([]);
      }
    } catch (error) {
      console.error('请求失败:', error);
      message.error('请求失败: ' + error.message);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <Card title="简单用户接口测试">
        <Space style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索关键字"
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
            style={{ width: 200 }}
          />
          <Button type="primary" onClick={fetchUsers} loading={loading}>
            获取用户列表
          </Button>
          <Button onClick={() => {
            setKeyword('');
            setTimeout(fetchUsers, 100);
          }}>
            清空搜索
          </Button>
        </Space>

        <div style={{ marginBottom: 16 }}>
          <p>用户数量: {users.length}</p>
        </div>

        <List
          loading={loading}
          dataSource={users}
          renderItem={(user: any) => (
            <List.Item>
              <List.Item.Meta
                title={user.realname || '未知姓名'}
                description={`账号: ${user.username || '未知账号'} | ID: ${user.id || '未知ID'}`}
              />
            </List.Item>
          )}
          locale={{ emptyText: '暂无用户数据' }}
        />
      </Card>
    </div>
  );
};

export default SimpleUserTest;
