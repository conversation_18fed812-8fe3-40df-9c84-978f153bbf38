import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Table, message, Space, Divider } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { getUserList } from '@/services/system/user';
import UserSelector from '@/components/UserSelector';

const UserListTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [keyword, setKeyword] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchUsers = async (searchKeyword = '', pageNo = 1, pageSize = 10) => {
    try {
      setLoading(true);
      console.log('请求参数:', { keyword: searchKeyword, pageNo, pageSize });

      const response = await getUserList({
        keyword: searchKeyword,
        pageNo,
        pageSize,
      });

      console.log('接口响应:', response);

      if (response && response.success) {
        const data = response.result;
        if (data && data.records) {
          // 新的分页格式
          setUsers(data.records);
          setPagination(prev => ({
            ...prev,
            current: pageNo,
            total: data.total || 0,
          }));
        } else if (Array.isArray(data)) {
          // 旧的数组格式
          setUsers(data);
          setPagination(prev => ({
            ...prev,
            current: pageNo,
            total: data.length,
          }));
        } else {
          setUsers([]);
          setPagination(prev => ({
            ...prev,
            current: pageNo,
            total: 0,
          }));
        }
      } else {
        message.error(response?.message || '获取用户列表失败');
        setUsers([]);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleSearch = () => {
    fetchUsers(keyword, 1, pagination.pageSize);
  };

  const handleTableChange = (paginationInfo: any) => {
    fetchUsers(keyword, paginationInfo.current, paginationInfo.pageSize);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '真实姓名',
      dataIndex: 'realname',
      key: 'realname',
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <Card title="用户列表接口测试">
        <Space style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索用户名或真实姓名"
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
            onPressEnter={handleSearch}
            style={{ width: 300 }}
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={handleSearch}
            loading={loading}
          >
            搜索
          </Button>
          <Button onClick={() => {
            setKeyword('');
            fetchUsers('', 1, pagination.pageSize);
          }}>
            重置
          </Button>
        </Space>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      <Divider />

      <Card title="用户选择组件测试">
        <div style={{ marginBottom: 16 }}>
          <p>已选择用户: {selectedUsers.join(', ')}</p>
        </div>
        <UserSelector
          value={selectedUsers}
          onChange={setSelectedUsers}
          listStyle={{ width: 300, height: 400 }}
        />
      </Card>
    </div>
  );
};

export default UserListTest;
