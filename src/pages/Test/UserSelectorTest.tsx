import React, { useState } from 'react';
import { Card, Button, message, Space } from 'antd';
import UserSelector from '@/components/UserSelector';

const UserSelectorTest: React.FC = () => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  const handleUserChange = (targetKeys: string[]) => {
    console.log('选中的用户ID:', targetKeys);
    setSelectedUsers(targetKeys);
  };

  const handleReset = () => {
    setSelectedUsers([]);
    message.info('已重置选择');
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="用户选择组件测试">
        <Space style={{ marginBottom: 16 }}>
          <Button onClick={handleReset}>重置选择</Button>
          <span>已选择用户数量: {selectedUsers.length}</span>
        </Space>

        <div style={{ marginBottom: 16 }}>
          <p><strong>已选择的用户ID:</strong></p>
          <div style={{ 
            background: '#f5f5f5', 
            padding: '8px', 
            borderRadius: '4px',
            minHeight: '40px',
            fontSize: '12px'
          }}>
            {selectedUsers.length > 0 ? selectedUsers.join(', ') : '暂无选择'}
          </div>
        </div>

        <UserSelector
          value={selectedUsers}
          onChange={handleUserChange}
          listStyle={{ width: 350, height: 400 }}
          titles={['可选用户', '已选用户']}
        />

        <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
          <p><strong>使用说明:</strong></p>
          <ul>
            <li>在搜索框中输入用户姓名或账号进行搜索</li>
            <li>搜索会调用后端接口，支持模糊匹配</li>
            <li>滚动左侧列表可以加载更多用户</li>
            <li>双击或使用箭头按钮在两个列表间移动用户</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default UserSelectorTest;
