/**
 * 未授权页面
 * 当用户尝试访问没有权限的页面时显示
 */

import React from 'react';
import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';
import { getCurrentCompanyName } from '@/config/company';

const Unauthorized: React.FC = () => {
  const companyName = getCurrentCompanyName();
  
  return (
    <Result
      status="403"
      title="无权访问"
      subTitle={`抱歉，您没有权限访问此页面。当前版本为${companyName}公司版本，不包含此功能。`}
      extra={
        <Link to="/">
          <Button type="primary">返回首页</Button>
        </Link>
      }
    />
  );
};

export default Unauthorized;
