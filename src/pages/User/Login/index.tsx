import { Footer } from '@/components';
import { login, getCaptcha, saveLoginCredentials, getLoginCredentials } from '@/services/auth';
import {
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import { FormattedMessage, Helmet, useIntl, useModel } from '@umijs/max';
import { Alert, message } from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useEffect } from 'react';
import CustomSelectLang from '@/components/CustomSelectLang';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';
import systemConfig from '@/config/systemConfig';

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      top: 16,
      zIndex: 1000,
      ':hover': {
        backgroundColor: 'transparent',
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundColor: '#f0f2f5',
    },
  };
});

const Lang = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.lang} data-lang>
      <CustomSelectLang />
    </div>
  );
};

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult & { message?: string }>({});
  const [loginErrorMessage, setLoginErrorMessage] = useState<string>('');
  const { initialState, setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();
  const intl = useIntl();
  const [captchaUrl, setCaptchaUrl] = useState<string>('');
  const [captchaKey, setCaptchaKey] = useState<number>(0);
  // 保存验证码的时间戳，用于登录时的checkKey
  const [captchaTimestamp, setCaptchaTimestamp] = useState<string>('');

  // 获取验证码图片
  const fetchCaptcha = async () => {
    try {
      // 生成时间戳
      const timestamp = new Date().getTime().toString();
      // 保存时间戳，用于登录时的checkKey
      setCaptchaTimestamp(timestamp);

      const response = await getCaptcha(timestamp);
      // 如果返回成功且有result字段（base64格式的图片数据）
      if (response.success && response.result) {
        // 直接使用返回的base64图片数据
        setCaptchaUrl(response.result);
        // 更新key触发图片重新渲染
        setCaptchaKey(prev => prev + 1);
      } else {
        message.error('获取验证码失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('Failed to fetch captcha:', error);
      message.error('获取验证码失败，请刷新页面重试');
    }
  };

  // 组件加载时获取验证码并检查是否有保存的登录凭证
  useEffect(() => {
    // 获取验证码
    fetchCaptcha();
  }, []);

  // 获取登录表单初始值
  const getLoginFormInitialValues = () => {
    // 检查是否有保存的登录凭证
    const savedCredentials = getLoginCredentials();
    if (savedCredentials) {
      return {
        username: savedCredentials.username,
        password: savedCredentials.password,
        autoLogin: true,
      };
    }
    return {
      autoLogin: false,
    };
  };

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams & { captcha?: string }) => {
    try {
      // 清除之前的错误信息
      setLoginErrorMessage('');

      // 保存登录凭证（如果选择了记住密码）
      if (values.autoLogin && values.username && values.password) {
        // 选择了记住密码，保存凭证
        saveLoginCredentials(values.username, values.password, true);
        console.log('已保存登录凭证');
      } else {
        // 如果没有选择记住密码，清除之前保存的凭证
        saveLoginCredentials('', '', false);
        console.log('已清除登录凭证');
      }

      // 登录
      const loginParams = {
        username: values.username || '',
        password: values.password || '',
        captcha: values.captcha,
        checkKey: captchaTimestamp ? Number(captchaTimestamp) : undefined // 验证码的检查键，与获取验证码时的时间戳对应
      };

      console.log('开始登录请求，参数:', loginParams);
      const msg = await login(loginParams);
      console.log('登录请求完成，响应:', JSON.stringify(msg, null, 2));

      if (msg.success) {
        // 保存token和用户名到localStorage
        if (msg.result?.token) {
          localStorage.setItem('token', msg.result.token);
          // 保存用户名，用于显示
          localStorage.setItem('loginUsername', values.username || '');
        }

        const defaultLoginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        message.success(defaultLoginSuccessMessage);

        try {
          // 获取用户信息
          await fetchUserInfo();
          console.log('用户信息已加载到全局状态');

          // 使用页面刷新方式跳转，避免白屏问题
          setTimeout(() => {
            const urlParams = new URL(window.location.href).searchParams;
            const redirectPath = urlParams.get('redirect') || '/';
            console.log('准备跳转到:', redirectPath);
            window.location.href = redirectPath;
          }, 100);
        } catch (error) {
          console.error('获取用户信息或跳转过程中出错:', error);
          message.error('登录过程中出现错误，请刷新页面重试');
        }
        return;
      }

      console.log('登录失败响应详情:', {
        success: msg.success,
        message: msg.message,
        code: msg.code
      });

      // 使用后端返回的错误消息 - 始终优先使用后端消息
      const errorMessage = msg.message || '登录失败';
      console.log('显示的错误消息:', errorMessage);

      // 更新错误状态 - 直接设置错误消息，不使用默认值
      setUserLoginState({
        status: 'error',
        type: 'account',
        message: errorMessage
      });

      // 直接设置错误信息
      setLoginErrorMessage(errorMessage);

      // 显示全局提示
      if (errorMessage.includes('验证码')) {
        // 验证码错误不使用全局提示，只在表单中显示
        console.log('验证码错误，不显示全局提示');
      } else {
        message.error(errorMessage);
      }

      // 刷新验证码
      fetchCaptcha();
    } catch (error: any) {
      console.error('登录过程中捕获到错误:', error);

      // 提取错误消息
      let errorMessage = '';

      // 尝试从 error.response.data 中提取错误信息
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
        console.log('从响应数据中提取的错误消息:', errorMessage);
      }

      console.log('捕获的错误消息最终值:', errorMessage);

      // 更新错误状态 - 直接设置错误消息
      setUserLoginState({
        status: 'error',
        type: 'account',
        message: errorMessage
      });

      // 直接设置错误信息
      setLoginErrorMessage(errorMessage || '登录失败');

      // 显示全局提示
      if (errorMessage && errorMessage.includes('验证码')) {
        // 验证码错误不使用全局提示，只在表单中显示
        console.log('验证码错误，不显示全局提示');
      } else {
        message.error(errorMessage || '登录失败');
      }

      // 刷新验证码
      fetchCaptcha();
    }
  };
  const { status, type: loginType } = userLoginState;
  console.log('当前登录状态:', status, '类型:', loginType, '错误信息:', userLoginState.message);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <Lang />
      <div
        style={{
          flex: '1',
          padding: '0',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          paddingTop: '200px', // 向下移动登录框
        }}
      >
        <LoginForm
          contentStyle={{
            width: 400,
            margin: '0 auto',
            padding: '30px 30px 0',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.08)',
            borderRadius: '8px',
            backgroundColor: '#fff',
          }}
          logo={<img alt="logo" src={systemConfig.logoUrl || '/logo.svg'} style={{ height: '50px', marginBottom: '10px' }} />}
          title={<div style={{ fontSize: '22px', fontWeight: 'bold', color: '#333' }}>{systemConfig.systemName}</div>}
          subTitle={<div style={{ fontSize: '14px', color: '#666' }}>{systemConfig.systemDescription}</div>}
          initialValues={getLoginFormInitialValues()}
          actions={[]}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
          submitter={{
            render: () => null, // 不渲染默认的提交按钮
          }}
        >
          {/* 添加独立的错误信息显示，不依赖状态更新 */}
          {loginErrorMessage && (
            <Alert
              style={{
                marginBottom: 24,
              }}
              message={loginErrorMessage}
              type="error"
              showIcon
            />
          )}

          <div style={{ marginBottom: '20px', textAlign: 'center', fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
            {intl.formatMessage({
              id: 'pages.login.accountLogin.tab',
              defaultMessage: '账户密码登录',
            })}
          </div>

          {status === 'error' && loginType === 'account' && (
            <LoginMessage
              content={userLoginState.message || intl.formatMessage({
                id: 'pages.login.accountLogin.errorMessage',
                defaultMessage: '账户或密码错误',
              })}
            />
          )}

          <div style={{ marginBottom: '20px' }}>
            <ProFormText
              name="username"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined style={{ color: '#1890ff' }} />,
                style: { height: '46px' }
              }}
              placeholder={intl.formatMessage({
                id: 'pages.login.username.placeholder',
                defaultMessage: '请输入用户名',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage
                      id="pages.login.username.required"
                      defaultMessage="请输入用户名!"
                    />
                  ),
                },
              ]}
            />
          </div>

          <div style={{ marginBottom: '20px' }}>
            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined style={{ color: '#1890ff' }} />,
                style: { height: '46px' }
              }}
              placeholder={intl.formatMessage({
                id: 'pages.login.password.placeholder',
                defaultMessage: '请输入密码',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage
                      id="pages.login.password.required"
                      defaultMessage="请输入密码！"
                    />
                  ),
                },
              ]}
            />
          </div>
          {/* 验证码 */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{ display: 'flex', alignItems: 'stretch', justifyContent: 'space-between' }}>
              <ProFormText
                name="captcha"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined style={{ color: '#1890ff' }} />,
                  style: {
                    height: '46px',
                    flex: '1',
                    minWidth: '0',
                    borderTopRightRadius: 0,
                    borderBottomRightRadius: 0
                  }
                }}
                placeholder="请输入验证码"
                rules={[
                  {
                    required: true,
                    message: '请输入验证码！',
                  },
                ]}
              />
              <img
                src={captchaUrl}
                alt="验证码"
                key={captchaKey}
                style={{
                  height: '46px',
                  width: '120px',
                  cursor: 'pointer',
                  border: '1px solid #d9d9d9',
                  borderRadius: '0 4px 4px 0',
                  borderLeft: 'none'
                }}
                onClick={fetchCaptcha}
                onError={(e) => {
                  console.error('Captcha image error:', e);
                  // Error message removed as requested
                }}
              />
            </div>
          </div>

          <div style={{ marginTop: '24px', backgroundColor: '#f5f8ff', padding: '20px', borderRadius: '0 0 8px 8px', marginLeft: '-30px', marginRight: '-30px', marginBottom: '-40px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
              <ProFormCheckbox name="autoLogin" style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '14px' }}>
                  <FormattedMessage id="pages.login.rememberMe" defaultMessage="记住密码" />
                </span>
              </ProFormCheckbox>
            </div>

            <button
              type="submit"
              style={{
                width: '100%',
                height: '46px',
                backgroundColor: '#1890ff',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'all 0.3s',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#40a9ff'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#1890ff'}
            >
              登录
            </button>
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
