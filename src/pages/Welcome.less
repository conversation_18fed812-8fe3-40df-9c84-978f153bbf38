.flip-card {
  background-color: transparent;
  width: 100%;
  height: 300px;
  perspective: 1000px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.1);
  border-radius: 8px;
}

.flip-card-inner.flipped {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flip-card-front {
  background-color: #ffffff;
}

.flip-card-back {
  background-color: #1890ff;
  color: white;
  transform: rotateY(180deg);
}

.flip-card-icon {
  margin-bottom: 16px;
  font-size: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.flip-card-hint {
  margin-top: auto;
  font-size: 12px;
  color: #999;
  padding-top: 16px;
}

.flip-card h4 {
  margin-bottom: 16px;
}

.flip-card-back h4 {
  color: #fff;
}

.flip-card-back p {
  color: #fff;
  text-align: left;
}
