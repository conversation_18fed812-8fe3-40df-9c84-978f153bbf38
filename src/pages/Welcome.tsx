import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Typography, Row, Col, theme } from 'antd';
import React from 'react';
import { BookOutlined, SettingOutlined, DatabaseOutlined, TeamOutlined } from '@ant-design/icons';
import './Welcome.less';

const { Title, Paragraph } = Typography;



const Welcome: React.FC = () => {
  const { token } = theme.useToken();
  const { initialState } = useModel('@@initialState');

  return (
    <PageContainer>
      <Card
        style={{ borderRadius: 8 }}
        styles={{
          body: {
            backgroundImage:
              initialState?.settings?.navTheme === 'realDark'
                ? 'background-image: linear-gradient(75deg, #1A1B1F 0%, #191C1F 100%)'
                : 'background-image: linear-gradient(75deg, #FBFDFF 0%, #F5F7FF 100%)',
          },
        }}
      >
        <div style={{ padding: '24px 0' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 40 }}>系统使用说明</Title>

          <Row gutter={[24, 24]}>
            {/* <Col xs={24} sm={12} md={8} lg={8}>
              <FlipCard
                title="多租户配置"
                icon={<SettingOutlined style={{ fontSize: 36, color: token.colorPrimary }} />}
                frontContent="多租户配置模块允许您为不同的公司设置每日项、链接设置和分类设置，实现个性化的数据管理。"
                backContent="在多租户配置中，您可以设置H5点击数范围、台账类型（车险、财险、增值服务）的占比区间，以及业务城市范围。系统会根据这些配置自动生成符合要求的数据，帮助您快速构建测试环境或模拟真实业务场景。配置完成后，点击保存设置即可应用到选定的公司。"
              />
            </Col> */}
            <Col xs={24} sm={12} md={8} lg={8}>
              <Card
                hoverable
                style={{ height: '100%' }}
                title={<div style={{ display: 'flex', alignItems: 'center' }}>
                  <DatabaseOutlined style={{ fontSize: 24, color: token.colorPrimary, marginRight: 8 }} />
                  <span>台账管理</span>
                </div>}
              >
                <div style={{ marginBottom: 16 }}>
                  <Paragraph>台账管理模块包含车险台账、财险台账和增值服务台账三个子模块，用于管理不同类型的业务数据。</Paragraph>
                </div>
                <Paragraph type="secondary">台账管理可选择特定省份与市区导入后,系统自动关联生成对应ip 地址的预约信息,聊天用户,聊天记录</Paragraph>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Card
                hoverable
                style={{ height: '100%' }}
                title={<div style={{ display: 'flex', alignItems: 'center' }}>
                  <TeamOutlined style={{ fontSize: 24, color: token.colorPrimary, marginRight: 8 }} />
                  <span>信息管理</span>
                </div>}
              >
                <div style={{ marginBottom: 16 }}>
                  <Paragraph>信息管理模块用于管理聊天源和聊天源详情，属于生成聊天用户的源数据,可查看是否符合贵司要求,剔除不符合要求的数据</Paragraph>
                </div>
                <Paragraph type="secondary">信息管理由 ai 生成后到系统中</Paragraph>
              </Card>
            </Col>
          </Row>

          <div style={{ marginTop: 40 }}>
            {/* <Title level={4}>系统概述</Title> */}
            {/* <Paragraph>
              本系统是一个综合性的多租户管理平台，专为保险和增值服务行业设计。通过多租户配置、台账管理和信息管理三大核心模块，系统实现了数据的全生命周期管理。您可以根据业务需求，为不同公司设置个性化的配置参数，生成符合要求的业务数据，并通过台账和信息管理模块进行查询、分析和导出。系统采用了Ant Design的设计规范，提供了直观、高效的用户界面，帮助您轻松完成各项管理任务。
            </Paragraph> */}
            <Paragraph>
              <BookOutlined /> 如需更多帮助，请联系管理员。
            </Paragraph>
          </div>
        </div>
      </Card>
    </PageContainer>
  );
};

export default Welcome;
