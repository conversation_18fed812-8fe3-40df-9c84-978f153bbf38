import React, { useState } from 'react';
import { But<PERSON>, Card, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import NameLibraryModal from './NameLibraryModal';

const NameLibraryDemo: React.FC = () => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);

  // 打开新增模态框
  const handleAdd = () => {
    setIsUpdate(false);
    setCurrentRecord(null);
    setModalVisible(true);
  };

  // 打开编辑模态框
  const handleEdit = () => {
    // 模拟一条已有数据
    const mockRecord = {
      id: '1',
      sceneName: '张三,李四,王五,赵丽颖,刘德华,周杰伦,林俊杰,王菲,李宗盛,周星驰,张艺谋,刘亦菲,黄晓明,杨幂,成龙,李连杰,章子怡,郭德纲,马云,马化腾,李彦宏,雷军,任正非,王健林,马斯克,比尔盖茨,扎克伯格,乔布斯,库克,贝索斯,巴菲特,李嘉诚,王思聪,马明哲,张朝阳,丁磊,李彦宏,刘强东,张一鸣,罗永浩',
      sourceType: '2',
    };

    setIsUpdate(true);
    setCurrentRecord(mockRecord);
    setModalVisible(true);
  };

  // 处理模态框取消
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 处理模态框提交成功
  const handleSuccess = (values: any) => {
    console.log('提交的数据:', values);
    message.success(`${isUpdate ? '编辑' : '新增'}姓名库成功`);
    setModalVisible(false);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="姓名库管理">
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} style={{ marginRight: 16 }}>
          新增姓名库
        </Button>
        <Button onClick={handleEdit}>
          编辑示例
        </Button>
      </Card>

      <NameLibraryModal
        visible={modalVisible}
        onCancel={handleCancel}
        onSuccess={handleSuccess}
        isUpdate={isUpdate}
        record={currentRecord}
      />
    </div>
  );
};

export default NameLibraryDemo;
