<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>姓名库新增弹框原型</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ant-design-vue@3.2.20/dist/antd.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content {
            display: flex;
            gap: 20px;
            min-height: 400px;
        }
        .left-panel, .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .panel-header {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .panel-content {
            flex: 1;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            min-height: 300px;
        }
        .textarea {
            width: 100%;
            height: 100%;
            border: none;
            resize: none;
            outline: none;
            font-size: 14px;
            line-height: 1.5;
        }
        .name-list {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .name-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
        }
        .name-item:hover {
            background-color: #f5f5f5;
        }
        .name-item:last-child {
            border-bottom: none;
        }
        .name-item .delete-btn {
            visibility: hidden;
            cursor: pointer;
            color: #ff4d4f;
        }
        .name-item:hover .delete-btn {
            visibility: visible;
        }
        .footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
            background-color: #fff;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }
        .form-item {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
            margin-left: 8px;
        }
        .stats {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }
        .arrow-icon {
            font-size: 24px;
            color: #1890ff;
            margin: 0 10px;
            align-self: center;
        }
        .middle-actions {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>新增姓名库</h2>
        </div>



        <div class="content">
            <div class="left-panel">
                <div class="panel-header">
                    <h3>输入姓名 <span class="badge">逗号分隔</span></h3>
                    <div class="stats">已输入: <span id="input-count">0</span> 个字符</div>
                </div>
                <div class="panel-content">
                    <textarea id="name-input" class="textarea" placeholder="请输入姓名，多个姓名用逗号分隔，例如：张三,李四,王五,赵丽颖,刘德华,周杰伦,林俊杰,王菲,李宗盛,周星驰,张艺谋,刘亦菲,黄晓明,杨幂,成龙,李连杰,章子怡,郭德纲,马云,马化腾,李彦宏,雷军,任正非,王健林,马斯克,比尔盖茨,扎克伯格,乔布斯,库克,贝索斯,巴菲特,李嘉诚,王思聪,马明哲,张朝阳,丁磊,李彦宏,刘强东,张一鸣,罗永浩"></textarea>
                </div>
            </div>

            <div class="middle-actions">
                <button id="convert-btn" class="btn btn-primary">
                    转换 →
                </button>
                <button id="clear-btn" class="btn">
                    清空
                </button>
            </div>

            <div class="right-panel">
                <div class="panel-header">
                    <h3>处理后的姓名 <span class="badge">共 <span id="name-count">0</span> 个</span></h3>
                    <button id="copy-json-btn" class="btn">复制JSON</button>
                </div>
                <div class="panel-content">
                    <ul id="name-list" class="name-list">
                        <!-- 姓名列表将在这里动态生成 -->
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <button id="cancel-btn" class="btn">取消</button>
            <button id="submit-btn" class="btn btn-primary">确认</button>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const nameInput = document.getElementById('name-input');
        const nameList = document.getElementById('name-list');
        const convertBtn = document.getElementById('convert-btn');
        const clearBtn = document.getElementById('clear-btn');
        const copyJsonBtn = document.getElementById('copy-json-btn');
        const submitBtn = document.getElementById('submit-btn');
        const cancelBtn = document.getElementById('cancel-btn');
        const inputCount = document.getElementById('input-count');
        const nameCount = document.getElementById('name-count');

        // 姓名数组
        let names = [];

        // 更新输入字符计数
        nameInput.addEventListener('input', () => {
            inputCount.textContent = nameInput.value.length;
        });

        // 转换按钮点击事件
        convertBtn.addEventListener('click', () => {
            const inputText = nameInput.value.trim();
            if (!inputText) {
                alert('请输入姓名');
                return;
            }

            // 分割姓名（支持逗号、空格、换行符分隔）
            const inputNames = inputText.split(/[,，、\s]+/).filter(name => name.trim());

            // 去重
            const uniqueNames = [...new Set(inputNames)];

            // 更新姓名数组
            names = uniqueNames;

            // 更新姓名列表
            updateNameList();
        });

        // 清空按钮点击事件
        clearBtn.addEventListener('click', () => {
            nameInput.value = '';
            names = [];
            updateNameList();
            inputCount.textContent = '0';
        });

        // 复制JSON按钮点击事件
        copyJsonBtn.addEventListener('click', () => {
            const json = JSON.stringify(names);
            navigator.clipboard.writeText(json).then(() => {
                alert('JSON已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        });

        // 提交按钮点击事件
        submitBtn.addEventListener('click', () => {
            if (names.length === 0) {
                alert('请先转换姓名');
                return;
            }



            // 构建提交的数据
            const data = {
                sceneName: names.join(','),  // 姓名字符串，逗号分隔
                sourceType: 2,  // 2表示姓名库
                nameList: names  // 姓名数组
            };

            console.log('提交数据:', data);
            alert('提交成功！数据已打印到控制台');
        });

        // 取消按钮点击事件
        cancelBtn.addEventListener('click', () => {
            if (confirm('确定要取消吗？所有输入将丢失')) {
                window.close();
            }
        });

        // 更新姓名列表
        function updateNameList() {
            // 清空列表
            nameList.innerHTML = '';

            // 更新计数
            nameCount.textContent = names.length;

            // 添加姓名项
            names.forEach((name, index) => {
                const li = document.createElement('li');
                li.className = 'name-item';

                const nameSpan = document.createElement('span');
                nameSpan.textContent = `${index + 1}. ${name}`;

                const deleteBtn = document.createElement('span');
                deleteBtn.className = 'delete-btn';
                deleteBtn.textContent = '删除';
                deleteBtn.onclick = () => {
                    names.splice(index, 1);
                    updateNameList();
                };

                li.appendChild(nameSpan);
                li.appendChild(deleteBtn);
                nameList.appendChild(li);
            });
        }
    </script>
</body>
</html>
