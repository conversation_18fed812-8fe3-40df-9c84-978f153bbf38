.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-col {
  flex: 1;
}

.name-editor {
  display: flex;
  gap: 16px;
  min-height: 400px;
}

.name-input-panel, .name-result-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h5 {
    margin: 0;
  }
}

.name-textarea {
  flex: 1;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
}

.name-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.name-list-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}

// 响应式布局
@media (max-width: 768px) {
  .name-editor {
    flex-direction: column;
  }

  .name-actions {
    flex-direction: row;
    margin: 16px 0;
  }
}
