import React, { useState, useRef } from 'react';
import { Modal, Form, Input, Button, Select, Space, List, Typography, Divider, message, Badge, Tooltip } from 'antd';
import { RightOutlined, DeleteOutlined, CopyOutlined, CheckOutlined } from '@ant-design/icons';
import './NameLibraryModal.less';

const { TextArea } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

interface NameLibraryModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (values: any) => void;
  isUpdate?: boolean;
  record?: any;
}

const NameLibraryModal: React.FC<NameLibraryModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  isUpdate = false,
  record = {},
}) => {
  const [form] = Form.useForm();
  const [inputText, setInputText] = useState<string>('');
  const [nameList, setNameList] = useState<string[]>([]);
  const [converting, setConverting] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);

  // 初始化表单数据
  React.useEffect(() => {
    if (visible && isUpdate && record) {
      // 如果有姓名数据，解析并设置
      if (record.sceneName) {
        const names = record.sceneName.split(',').map((name: string) => name.trim()).filter(Boolean);
        setNameList(names);
        setInputText(record.sceneName);
      }
    } else if (visible && !isUpdate) {
      // 新增时重置
      setInputText('');
      setNameList([]);
    }
  }, [visible, isUpdate, record]);

  // 转换姓名
  const handleConvert = () => {
    const text = inputText.trim();
    if (!text) {
      message.warning('请输入姓名');
      return;
    }

    setConverting(true);

    try {
      // 分割姓名（支持逗号、空格、换行符分隔）
      const names = text.split(/[,，、\s]+/).filter(name => name.trim());

      // 去重
      const uniqueNames = [...new Set(names)];

      // 更新姓名列表
      setNameList(uniqueNames);
      message.success(`成功转换 ${uniqueNames.length} 个姓名`);
    } catch (error) {
      console.error('转换姓名出错:', error);
      message.error('转换姓名出错，请检查输入格式');
    } finally {
      setConverting(false);
    }
  };

  // 清空输入
  const handleClear = () => {
    setInputText('');
    setNameList([]);
  };

  // 删除单个姓名
  const handleDeleteName = (index: number) => {
    const newList = [...nameList];
    newList.splice(index, 1);
    setNameList(newList);
  };

  // 复制JSON
  const handleCopyJson = () => {
    if (nameList.length === 0) {
      message.warning('没有可复制的姓名');
      return;
    }

    const json = JSON.stringify(nameList);
    navigator.clipboard.writeText(json).then(() => {
      message.success('JSON已复制到剪贴板');
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }).catch(err => {
      console.error('复制失败:', err);
      message.error('复制失败，请手动复制');
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    if (nameList.length === 0) {
      message.warning('请先转换姓名');
      return;
    }

    try {
      setSubmitting(true);

      // 构建提交的数据
      const data = {
        sceneName: nameList.join(','),  // 姓名字符串，逗号分隔
        sourceType: 2,  // 2表示姓名库
        nameList: nameList,  // 姓名数组
        id: isUpdate ? record.id : undefined,  // 编辑时需要ID
      };

      console.log('提交数据:', data);

      // 调用成功回调
      onSuccess(data);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title={isUpdate ? '编辑姓名库' : '新增姓名库'}
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitting} onClick={handleSubmit}>
          确认
        </Button>,
      ]}
      bodyStyle={{ padding: '24px' }}
    >
      <Form
        form={form}
        layout="vertical"
      >

        <div className="name-editor">
          <div className="name-input-panel">
            <div className="panel-header">
              <Title level={5}>
                输入姓名
                <Badge count="逗号分隔" style={{ backgroundColor: '#1890ff', marginLeft: 8 }} />
              </Title>
              <Text type="secondary">已输入: {inputText.length} 个字符</Text>
            </div>
            <TextArea
              className="name-textarea"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请输入姓名，多个姓名用逗号分隔，例如：张三,李四,王五,赵丽颖,刘德华,周杰伦,林俊杰,王菲,李宗盛,周星驰,张艺谋,刘亦菲,黄晓明,杨幂,成龙,李连杰,章子怡,郭德纲,马云,马化腾,李彦宏,雷军,任正非..."
              autoSize={{ minRows: 10, maxRows: 20 }}
            />
          </div>

          <div className="name-actions">
            <Button
              type="primary"
              icon={<RightOutlined />}
              onClick={handleConvert}
              loading={converting}
            >
              转换
            </Button>
            <Button onClick={handleClear}>清空</Button>
          </div>

          <div className="name-result-panel">
            <div className="panel-header">
              <Title level={5}>
                处理后的姓名
                <Badge count={`共 ${nameList.length} 个`} style={{ backgroundColor: '#52c41a', marginLeft: 8 }} />
              </Title>
              <Tooltip title={copied ? '已复制' : '复制JSON'}>
                <Button
                  icon={copied ? <CheckOutlined /> : <CopyOutlined />}
                  onClick={handleCopyJson}
                  type={copied ? 'primary' : 'default'}
                >
                  {copied ? '已复制' : '复制JSON'}
                </Button>
              </Tooltip>
            </div>
            <div className="name-list-container">
              {nameList.length > 0 ? (
                <List
                  size="small"
                  bordered
                  dataSource={nameList}
                  renderItem={(item, index) => (
                    <List.Item
                      actions={[
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteName(index)}
                        >
                          删除
                        </Button>
                      ]}
                    >
                      <Text>{index + 1}. {item}</Text>
                    </List.Item>
                  )}
                />
              ) : (
                <div className="empty-list">
                  <Text type="secondary">暂无数据，请先转换姓名</Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default NameLibraryModal;
