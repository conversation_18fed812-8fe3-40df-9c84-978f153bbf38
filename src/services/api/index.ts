// API统一管理文件
import { getApiBaseUrl } from '@/utils/env';

// API前缀，移除了/api前缀
export const API_PREFIX = '';

// 不需要前缀的接口前缀（直接访问后端）
export const DIRECT_PREFIX = '';

// 基础URL
export const BASE_URL = getApiBaseUrl();

// 用户相关接口
export const UserAPI = {
  // 登录接口
  LOGIN: `${API_PREFIX}/sys/login`,
  // 退出登录接口
  LOGOUT: `${API_PREFIX}/sys/logout`,
  // 获取当前用户信息
  CURRENT_USER: `${API_PREFIX}/sys/user/getUserInfo`,
  // 获取用户权限信息
  USER_PERMISSION: `${API_PREFIX}/sys/permission/getUserPermissionByToken`,
  // 获取验证码
  GET_CAPTCHA: `${API_PREFIX}/sys/randomImage`,
};

// 系统通知相关接口
export const NoticeAPI = {
  // 获取通知列表
  GET_NOTICES: `${API_PREFIX}/notices`,
};

// 规则相关接口
export const RuleAPI = {
  // 获取规则列表
  GET_RULES: `${API_PREFIX}/rule`,
  // 更新规则
  UPDATE_RULE: `${API_PREFIX}/rule`,
  // 添加规则
  ADD_RULE: `${API_PREFIX}/rule`,
  // 删除规则
  REMOVE_RULE: `${API_PREFIX}/rule`,
};

// 导出所有API
export default {
  UserAPI,
  NoticeAPI,
  RuleAPI,
};
