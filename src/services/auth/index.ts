// 登录认证相关服务
import { request } from '@umijs/max';
import { UserAPI, API_PREFIX } from '@/services/api';
import { getApiBaseUrl } from '@/utils/env';

// 登录接口
export async function login(body: {
  username: string;
  password: string;
  captcha?: string;
  checkKey?: number;
}, options?: { [key: string]: any }) {
  try {
    return await request<{
      success: boolean;
      message: string;
      result?: {
        token: string;
      };
      code: number;
    }>(UserAPI.LOGIN, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      skipErrorHandler: true, // 跳过默认的错误处理
      ...(options || {}),
    });
  } catch (error: any) {
    // 如果是验证码错误，直接抛出错误，让调用者处理
    console.error('登录请求错误:', error);
    throw error;
  }
}

// 退出登录接口
export async function logout(options?: { [key: string]: any }) {
  try {
    console.log('发起退出登录请求...');
    const response = await request<{
      success: boolean;
      message: string;
      code: number;
      result: any;
      timestamp: number;
    }>(UserAPI.LOGOUT, {
      method: 'POST',
      skipErrorHandler: true, // 跳过默认的错误处理，由调用者处理
      timeout: 5000, // 设置超时时间为5秒
      ...(options || {}),
    });

    console.log('退出登录请求响应:', response);
    return response;
  } catch (error) {
    console.error('退出登录请求错误:', error);
    // 返回一个模拟的响应，以便调用者可以统一处理
    return {
      success: false,
      message: '退出登录请求失败',
      code: 500,
      result: null,
      timestamp: new Date().getTime(),
    };
  }
}

// 获取当前用户信息
export async function getCurrentUser(options?: { [key: string]: any }) {
  return request<{
    data: API.CurrentUser;
  }>(UserAPI.CURRENT_USER, {
    method: 'GET',
    ...(options || {}),
  });
}

// 获取用户权限信息
export async function getUserPermission(options?: { [key: string]: any }) {
  // 添加时间戳参数避免缓存
  const timestamp = new Date().getTime();
  try {
    // 先尝试调用新的菜单权限接口
    const menuPermissionResponse = await request<{
      success: boolean;
      message: string;
      code: number;
      result: {
        menu: string[];
        auth: string[];
      };
    }>(`${API_PREFIX}/sys/permission/getMenuPermission?_t=${timestamp}`, {
      method: 'GET',
      skipErrorHandler: true,
      ...(options || {}),
    });

    if (menuPermissionResponse.success) {
      console.log('成功获取菜单权限:', menuPermissionResponse.result);

      // 设置菜单权限到权限服务
      try {
        const { setPermissions } = require('@/services/menuPermission');
        setPermissions({
          menus: menuPermissionResponse.result.menu || [],
          buttons: menuPermissionResponse.result.auth || []
        });
        console.log('菜单权限已设置到权限服务');
      } catch (error) {
        console.error('设置菜单权限失败:', error);
      }

      return {
        success: true,
        message: '获取权限成功',
        code: 200,
        result: {
          allAuth: [],
          auth: menuPermissionResponse.result.auth || [],
          codeList: [],
          menu: menuPermissionResponse.result.menu || [],
          sysSafeMode: false
        }
      };
    }
  } catch (error) {
    console.error('获取菜单权限失败，尝试使用原接口:', error);
  }

  // 如果新接口失败，回退到原来的接口
  return request<{
    success: boolean;
    message: string;
    code: number;
    result: {
      allAuth: any[];
      auth: any[];
      codeList: string[];
      menu: any[];
      sysSafeMode: boolean;
    };
  }>(`${UserAPI.USER_PERMISSION}?_t=${timestamp}`, {
    method: 'GET',
    ...(options || {}),
  });
}

// 获取验证码
export async function getCaptcha(timestamp?: string, options?: { [key: string]: any }) {
  // 如果没有提供时间戳，生成一个新的
  const ts = timestamp || new Date().getTime().toString();

  // 使用环境变量中的 API 基础 URL
  const backendUrl = getApiBaseUrl();
  const captchaUrl = `${backendUrl}${UserAPI.GET_CAPTCHA}/${ts}`;

  return request<{
    success: boolean;
    message: string;
    result: string; // base64格式的图片数据
    code: number;
  }>(captchaUrl, {
    method: 'GET',
    skipErrorHandler: true, // 跳过默认的错误处理
    ...(options || {}),
  });
}

// 记住密码相关功能
const LOCAL_STORAGE_KEY = 'ant_web_login_credentials';

// 保存登录凭证
export function saveLoginCredentials(username: string, password: string, remember: boolean) {
  if (remember) {
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify({
        username,
        password,
      }),
    );
  } else {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
  }
}

// 获取保存的登录凭证
export function getLoginCredentials(): { username: string; password: string } | null {
  const credentialsStr = localStorage.getItem(LOCAL_STORAGE_KEY);
  if (credentialsStr) {
    try {
      return JSON.parse(credentialsStr);
    } catch (e) {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    }
  }
  return null;
}

// 清除保存的登录凭证
export function clearLoginCredentials() {
  localStorage.removeItem(LOCAL_STORAGE_KEY);
}
