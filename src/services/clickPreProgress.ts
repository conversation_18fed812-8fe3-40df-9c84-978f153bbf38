// 点击数预生成进度监控相关API
import { request } from '@umijs/max';

// 定义进度数据类型
export interface ConfigInfo {
  tenantId: number;
  tenantName: string;
  avgClicks: number;
  startTime?: string;
  endTime?: string;
  status: '待开始' | '处理中' | '已完成';
}

export interface ProgressData {
  totalTasks: number;
  completedTasks: number;
  remainingTasks: number;
  status: string;
  configList: ConfigInfo[];
}

// 获取点击数预生成批量处理进度
export async function getClickPreBatchProgress() {
  return request('/clickAuto/pre/clickPreBatchProgress', {
    method: 'GET',
  });
}

// 手动触发点击数预生成（如果需要的话）
export async function triggerClickPreGeneration() {
  return request('/clickAuto/pre/autoCreateClickPreAll', {
    method: 'POST',
  });
}
