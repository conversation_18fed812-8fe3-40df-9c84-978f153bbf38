import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/corp/pdAddedLedger/list',
  add: '/corp/pdAddedLedger/add',
  edit: '/corp/pdAddedLedger/edit',
  delete: '/corp/pdAddedLedger/delete',
  deleteBatch: '/corp/pdAddedLedger/deleteBatch',
  importExcel: '/corp/pdAddedLedger/importExcel',
  exportXls: '/corp/pdAddedLedger/exportXls',
};

/**
 * 获取增值服务台账列表
 * @param params 查询参数
 */
export async function fetchPdAddedLedgerList(params: any) {
  const { current, pageSize, ...restParams } = params;
  return request(`${API.list}/${current}/${pageSize}`, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加增值服务台账
 * @param params 表单数据
 */
export async function addPdAddedLedger(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑增值服务台账
 * @param params 表单数据
 */
export async function editPdAddedLedger(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除增值服务台账
 * @param id 记录ID
 */
export async function deletePdAddedLedger(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除增值服务台账
 * @param ids ID数组
 */
export async function batchDeletePdAddedLedger(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}
