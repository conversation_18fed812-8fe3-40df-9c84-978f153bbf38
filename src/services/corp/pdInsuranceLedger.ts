import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/corp/pdInsuranceLedger/list',
  add: '/corp/pdInsuranceLedger/add',
  edit: '/corp/pdInsuranceLedger/edit',
  delete: '/corp/pdInsuranceLedger/delete',
  deleteBatch: '/corp/pdInsuranceLedger/deleteBatch',
  importExcel: '/corp/pdInsuranceLedger/importExcel',
  exportXls: '/corp/pdInsuranceLedger/exportXls',
};

/**
 * 获取财险台账列表
 * @param params 查询参数
 */
export async function fetchPdInsuranceLedgerList(params: any) {
  const { current, pageSize, ...restParams } = params;
  return request(`${API.list}/${current}/${pageSize}`, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加财险台账
 * @param params 表单数据
 */
export async function addPdInsuranceLedger(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑财险台账
 * @param params 表单数据
 */
export async function editPdInsuranceLedger(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除财险台账
 * @param id 记录ID
 */
export async function deletePdInsuranceLedger(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除财险台账
 * @param ids ID数组
 */
export async function batchDeletePdInsuranceLedger(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}
