// 多租户配置相关API
import { request } from '@umijs/max';
import { uploadFile } from '@/utils/request';
import { getApiBaseUrl } from '@/utils/env';

// 定义每日配置DTO类型
export interface DailyConfigDto {
  configJson?: string;       // 每日配置 JSON 内容
  clickNum?: number;         // 点击数
  linkType?: number;         // 链接类型(0-车险;1-财险;2-增值服务)
  tenantId?: number;         // 租户id
  monthDataStart?: string;   // 日期开始时间
  monthDataEnd?: string;     // 日期结束时间
}

// 添加每日配置
export async function addDailyConfig(data: any) {
  return request('/daily-config/add', {
    method: 'POST',
    data,
  });
}

// 编辑每日配置
export async function editDailyConfig(id: string, data: any) {
  return request(`/daily-config/edit/${id}`, {
    method: 'POST',
    data,
  });
}

// 获取租户列表
// tenantType: 租户类型，0-车险，1-财险，2-增值服务，多选时用逗号隔开，如 "0,1,2"
export async function getTenantList(tenantType?: string | number[], params?: any) {
  // 如果 tenantType 是数组，则转换为逗号分隔的字符串
  const formattedTenantType = Array.isArray(tenantType) ? tenantType.join(',') : tenantType;

  return request('/sys/tenant/getTenantList', {
    method: 'GET',
    params: {
      ...params,
      // 如果没有指定 tenantType，则不传这个参数
      ...(formattedTenantType !== undefined ? { tenantType: formattedTenantType } : {})
    },
  });
}

// 注意: clickAuto/pre/get 接口不再使用

// 获取每日设置详情
export async function getDailyConfigInfo(tenantId: number) {
  return request(`/daily-config/info/${tenantId}`, {
    method: 'GET',
  });
}

// 验证批量导入文件
export async function validateMultipleConfig(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return uploadFile('/daily-config/validateFile/multi', formData);
}

// 批量导入所有公司配置
export async function uploadMultipleConfig(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return uploadFile('/daily-config/uploadFile/multi', formData);
}

// 批量导入结果VO类型定义
export interface BatchImportResultVO {
  success: boolean;
  message: string;
  successCount: number;  // 成功导入的条数
  failureCount: number;  // 失败的条数
  failureDetails: BatchImportFailureDetail[];  // 失败详情
}

// 批量导入失败详情
export interface BatchImportFailureDetail {
  rowNumber: number;     // 表格行号
  cityName: string;      // 城市名称
  reason: string;        // 失败原因
}

// 系统补全 - 上传多个文件
// 支持两种调用格式：
// 1. DailyConfigDto 格式（来自 SystemCompletion 页面）
// 2. LedgerImportDTO 格式（来自 SystemBatchCompletion 页面）
export async function uploadSystemCompletionFiles(data: any) {
  // 检查数据格式，支持两种调用方式：
  // 1. 新格式：DailyConfigDto 格式（来自 SystemCompletion 页面）
  // 2. 旧格式：LedgerImportDTO 格式（来自 SystemBatchCompletion 页面）

  let requestData: any;

  if (data.configJson !== undefined || data.monthDataStart !== undefined) {
    // 新格式：来自 SystemCompletion 页面的 DailyConfigDto 格式
    console.log('检测到新格式数据 (DailyConfigDto):', data);

    // 构建 DailyConfigDto 对象，字段名与后端 DTO 保持一致
    const dto: DailyConfigDto = {
      configJson: data.configJson || '{}',
      clickNum: data.clickNum,
      linkType: data.linkType,
      tenantId: data.tenantId,
      monthDataStart: data.monthDataStart,
      monthDataEnd: data.monthDataEnd,
    };

    // 如果有文件，调用新的接口 /clickAuto/pre/uploadFile/multi
    if (data.files && data.files.length > 0) {
      console.log('检测到文件，调用新接口 /clickAuto/pre/uploadFile/multi:', data.files);
      const formData = new FormData();

      // 添加文件
      data.files.forEach((file: File) => {
        formData.append('files', file);
      });

      // 添加 DTO 参数，使用与后端 DailyConfigDto 一致的字段名
      if (dto.configJson !== undefined && dto.configJson !== null) {
        formData.append('configJson', dto.configJson);
      }
      if (dto.clickNum !== undefined && dto.clickNum !== null) {
        formData.append('clickNum', String(dto.clickNum));
      }
      if (dto.linkType !== undefined && dto.linkType !== null) {
        formData.append('linkType', String(dto.linkType));
      }
      if (dto.tenantId !== undefined && dto.tenantId !== null) {
        formData.append('tenantId', String(dto.tenantId));
      }
      if (dto.monthDataStart !== undefined && dto.monthDataStart !== null) {
        formData.append('monthDataStart', dto.monthDataStart);
      }
      if (dto.monthDataEnd !== undefined && dto.monthDataEnd !== null) {
        formData.append('monthDataEnd', dto.monthDataEnd);
      }

      console.log('提交系统补全数据到新接口 (FormData):', dto);
      console.log('调用接口: /clickAuto/pre/uploadFile/multi');

      // 使用 uploadFile 发送 FormData 到新接口，设置无超时时间
      return uploadFile('/clickAuto/pre/uploadFile/multi', formData, { timeout: 0 });
    } else {
      // 没有文件时，调用原先的接口
      requestData = {
        startDate: dto.monthDataStart,
        endDate: dto.monthDataEnd,
        clickNum: dto.clickNum,
        configJson: dto.configJson,
        linkType: dto.linkType,
        // 注意：后端期望 tenantId 是 List<String>，所以需要转换为数组
        tenantId: Array.isArray(dto.tenantId) ? dto.tenantId.map(String) : [String(dto.tenantId)]
      };

      console.log('没有文件，调用原先接口:', requestData);
      console.log('调用接口: /corp/pdLedger/pageSystemCompletion');

      // 使用 request 发送 JSON 数据
      return request('/corp/pdLedger/pageSystemCompletion', {
        method: 'POST',
        data: requestData,
      });
    }
  } else {
    // 旧格式：来自 SystemBatchCompletion 页面的 LedgerImportDTO 格式
    console.log('检测到旧格式数据 (LedgerImportDTO):', data);
    requestData = {
      startDate: data.startDate,
      endDate: data.endDate,
      clickNum: data.clickNum,
      configJson: data.recordNum || '{}', // 将 recordNum 映射为 configJson
      linkType: data.linkType || 0,
      // 确保 tenantId 是字符串数组格式
      tenantId: Array.isArray(data.tenantId) ? data.tenantId.map(String) : [String(data.tenantId)]
    };

    console.log('提交系统补全数据:', requestData);
    console.log('调用接口: /corp/pdLedger/pageSystemCompletion');

    // 使用 request 发送 JSON 数据，而不是 FormData
    return request('/corp/pdLedger/pageSystemCompletion', {
      method: 'POST',
      data: requestData,
    });
  }
}

// 批量系统补全 - 校验单个Excel文件
export async function validateBatchSystemCompletionFile(file: File) {
  const formData = new FormData();

  // 添加文件
  formData.append('files', file);

  // 调用批量系统补全校验接口
  return uploadFile('/clickAuto/pre/validateFile/multiBatch', formData);
}

// 批量系统补全 - 上传单个Excel文件
export async function uploadBatchSystemCompletionFile(file: File) {
  const formData = new FormData();

  // 添加文件
  formData.append('files', file);

  // 调用批量系统补全接口
  return uploadFile('/clickAuto/pre/uploadFile/multiBatch', formData);
}

// 查询批量系统补全任务处理进度
export async function getBatchSystemCompletionProgress() {
  return request('/clickAuto/pre/batchProgress', {
    method: 'GET',
  });
}

// 获取租户详细信息
export async function getTenantById(id: number) {
  return request('/sys/tenant/queryById', {
    method: 'GET',
    params: { id },
  });
}

// 编辑租户信息
export async function editTenant(data: any) {
  return request('/sys/tenant/edit', {
    method: 'POST',
    data,
  });
}

// 批量导入每日设置
export async function uploadBatchDailyConfig(formData: FormData) {
  return uploadFile('/daily-config/importExcel', formData);
}

// 批量导入链接设置
export async function uploadBatchLinkConfig(formData: FormData) {
  return uploadFile('/corp/pdLinkInfo/importExcel', formData);
}

// 获取链接生成规则配置
export async function getGenerateConfig(tenantId: number) {
  return request(`/generate-config/list/${tenantId}`, {
    method: 'GET',
  });
}

// 保存链接生成规则配置
export async function saveGenerateConfig(data: any) {
  return request('/generate-config/save', {
    method: 'POST',
    data,
  });
}

// 下载Excel模板文件
export async function downloadTemplate(backendFileName: string, displayFileName?: string) {
  const baseURL = getApiBaseUrl();
  const token = localStorage.getItem('token') || '';

  // 构建下载URL，传递后端文件名
  const downloadUrl = `${baseURL}/info/pdScene/downloadTemplate?fileName=${encodeURIComponent(backendFileName)}&X-Access-Token=${encodeURIComponent(token)}`;

  try {
    // 使用fetch方式进行下载
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'X-Access-Token': token,
        'X-Tenant-Id': '0',
        'X-Timestamp': new Date().getTime().toString(),
        'X-Version': 'v3',
      },
    });

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 获取文件blob
    const blob = await response.blob();

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    // 使用显示文件名，如果没有提供则使用后端文件名
    link.download = displayFileName || backendFileName;
    link.style.display = 'none';

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return { success: true };
  } catch (error) {
    console.error('下载模板文件失败:', error);
    throw error;
  }
}
