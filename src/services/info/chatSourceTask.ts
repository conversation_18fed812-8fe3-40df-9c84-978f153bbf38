import { request } from '@/utils/request';

// API 路径
const API = {
  stats: '/info/chatSourceTask/stats',
  statsType: '/info/chatSourceTask/stats',
  trigger: '/info/chatSourceTask/trigger',
};

/**
 * 获取所有类型的队列统计信息
 */
export async function getQueueStats() {
  return request(API.stats, {
    method: 'GET',
  });
}

/**
 * 获取指定类型的队列统计信息
 * @param linkType 链接类型，0-车险，1-财险，2-增值服务
 */
export async function getQueueStatsByType(linkType: number) {
  return request(`${API.statsType}/${linkType}`, {
    method: 'GET',
  });
}

/**
 * 手动触发任务消费者
 * @param linkType 链接类型，0-车险，1-财险，2-增值服务
 */
export async function triggerTaskConsumer(linkType: number) {
  return request(`${API.trigger}/${linkType}`, {
    method: 'POST',
  });
}
