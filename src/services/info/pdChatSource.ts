import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/info/pdChatSource/list',
  add: '/info/pdChatSource/add',
  edit: '/info/pdChatSource/edit',
  delete: '/info/pdChatSource/delete',
  deleteBatch: '/info/pdChatSource/deleteBatch',
  importExcel: '/info/pdChatSource/importExcel',
  exportXls: '/info/pdChatSource/exportXls',
  getChatHistory: '/info/pdChatSourceDet/getByPid',
};

/**
 * 获取聊天源列表
 * @param params 查询参数
 */
export async function fetchPdChatSourceList(params: any) {
  const { current, pageSize, ...restParams } = params;
  return request(`${API.list}/${current}/${pageSize}`, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加聊天源
 * @param params 表单数据
 */
export async function addPdChatSource(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑聊天源
 * @param params 表单数据
 */
export async function editPdChatSource(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除聊天源
 * @param id 记录ID
 */
export async function deletePdChatSource(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除聊天源
 * @param ids ID数组
 */
export async function batchDeletePdChatSource(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 获取聊天记录
 * @param pid 父ID
 */
export async function getChatHistory(pid: string) {
  return request(API.getChatHistory, {
    method: 'GET',
    params: { pid },
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}
