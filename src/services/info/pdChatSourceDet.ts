import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/info/pdChatSourceDet/list',
  add: '/info/pdChatSourceDet/add',
  edit: '/info/pdChatSourceDet/edit',
  delete: '/info/pdChatSourceDet/delete',
  deleteBatch: '/info/pdChatSourceDet/deleteBatch',
  importExcel: '/info/pdChatSourceDet/importExcel',
  exportXls: '/info/pdChatSourceDet/exportXls',
  getByPid: '/info/pdChatSourceDet/getByPid',
};

/**
 * 获取聊天源详情列表
 * @param params 查询参数
 */
export async function fetchPdChatSourceDetList(params: any) {
  const { current, pageSize, ...restParams } = params;
  return request(`${API.list}/${current}/${pageSize}`, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加聊天源详情
 * @param params 表单数据
 */
export async function addPdChatSourceDet(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑聊天源详情
 * @param params 表单数据
 */
export async function editPdChatSourceDet(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除聊天源详情
 * @param id 记录ID
 */
export async function deletePdChatSourceDet(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除聊天源详情
 * @param ids ID数组
 */
export async function batchDeletePdChatSourceDet(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 根据父ID获取聊天记录
 * @param pid 父ID
 */
export async function getByPid(pid: string) {
  return request(API.getByPid, {
    method: 'GET',
    params: { pid },
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}
