import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/info/pdScene/list',
  add: '/info/pdScene/add',
  edit: '/info/pdScene/edit',
  delete: '/info/pdScene/delete',
  deleteBatch: '/info/pdScene/deleteBatch',
  queryById: '/info/pdScene/queryById',
  importExcel: '/info/pdScene/importExcel',
  exportXls: '/info/pdScene/exportXls',
};

/**
 * 获取场景库列表
 * @param params 查询参数
 */
export async function fetchPdSceneList(params: any) {
  return request(API.list, {
    method: 'GET',
    params,
  });
}

/**
 * 添加场景库
 * @param params 表单数据
 */
export async function addPdScene(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑场景库
 * @param params 表单数据
 */
export async function editPdScene(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除场景库
 * @param id 记录ID
 */
export async function deletePdScene(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除场景库
 * @param ids ID数组
 */
export async function batchDeletePdScene(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    params: { ids: ids.join(',') },
  });
}

/**
 * 通过ID查询场景库
 * @param id 记录ID
 */
export async function queryPdSceneById(id: string) {
  return request(API.queryById, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}
