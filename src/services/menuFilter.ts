/**
 * 菜单过滤服务
 * 根据后端配置过滤菜单项
 */

import { request } from '@/utils/request';

// 获取可见的菜单键列表
export const getVisibleMenuKeys = async (): Promise<string[]> => {
  try {
    // 从服务器获取
    console.log('从服务器获取菜单权限...');
    const response = await request('/sys/permission/getMenuPermission', {
      method: 'GET',
      // 添加时间戳参数避免缓存
      params: { _t: new Date().getTime() }
    });

    if (response && response.success && response.result && response.result.menu) {
      console.log('从服务器获取的菜单权限:', response.result.menu);
      return response.result.menu;
    }

    return [];
  } catch (error) {
    console.error('获取菜单权限失败:', error);
    return [];
  }
};

// 检查菜单是否可见
export const isMenuVisible = async (menuKey: string): Promise<boolean> => {
  // 直接从服务器获取最新的菜单权限
  const visibleMenus = await getVisibleMenuKeys();
  return visibleMenus.includes(menuKey);
};

/**
 * 过滤菜单树
 * 根据后端配置的菜单权限过滤菜单项
 * @param menuTree 原始菜单树
 * @returns 过滤后的菜单树
 */
export const filterMenuTree = async (menuTree: any[]) => {
  if (!menuTree || !Array.isArray(menuTree)) {
    return [];
  }

  try {
    // 获取可见菜单键列表
    const visibleKeys = await getVisibleMenuKeys();
    console.log('获取到的可见菜单键:', visibleKeys);

    // 前端过滤菜单树
    const filterMenuItems = (items: any[]): any[] => {
      if (!items || !Array.isArray(items)) return [];

      return items.filter(item => {
        // 跳过空项
        if (!item) return false;

        // 特殊路由始终保留（登录页、首页重定向、404页面、未授权页面）
        if (
          item.path === '/user' ||
          item.path === '/' ||
          item.path === '*' ||
          item.path === '/unauthorized'
        ) {
          return true;
        }

        // 获取菜单键（使用 name 属性）
        const menuKey = item.name;

        // 如果没有 name，则不显示
        if (!menuKey) return false;

        // 检查菜单是否可见
        const isVisible = visibleKeys.includes(menuKey);
        console.log(`菜单 ${menuKey} 是否可见:`, isVisible);

        // 如果菜单不可见，则其子菜单也不可见
        if (!isVisible) {
          return false;
        }

        // 如果菜单可见且有子菜单，递归过滤子菜单
        if (item.routes && item.routes.length > 0) {
          const filteredChildren = filterMenuItems(item.routes);

          // 替换原来的子菜单
          item.routes = filteredChildren;
        }

        // 菜单可见，显示它
        return true;
      });
    };

    const filteredMenu = filterMenuItems(menuTree);
    console.log('前端过滤后的菜单:', filteredMenu);

    return filteredMenu;
  } catch (error) {
    console.error('过滤菜单树失败:', error);
    return menuTree;
  }
};
