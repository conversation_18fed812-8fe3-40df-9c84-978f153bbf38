/**
 * 菜单权限服务
 * 用于处理菜单权限的过滤和检查
 */

// 存储用户权限信息
let userPermissions = {
  menus: [] as string[],  // 可访问的菜单
  buttons: [] as string[] // 可操作的按钮
};

/**
 * 设置权限信息
 * @param permissions 权限信息
 */
export const setPermissions = (permissions: { menus: string[], buttons: string[] }) => {
  userPermissions = permissions;
  localStorage.setItem('permissions', JSON.stringify(permissions));
};

/**
 * 获取权限信息
 * @returns 权限信息
 */
export const getPermissions = () => {
  if (!userPermissions.menus.length) {
    const storedPermissions = localStorage.getItem('permissions');
    if (storedPermissions) {
      userPermissions = JSON.parse(storedPermissions);
    }
  }
  return userPermissions;
};

/**
 * 检查菜单权限
 * @param menuKey 菜单键
 * @returns 是否有权限
 */
export const hasMenuPermission = (menuKey: string): boolean => {
  const { menus } = getPermissions();
  return menus.includes(menuKey);
};

/**
 * 检查按钮权限
 * @param buttonKey 按钮键
 * @returns 是否有权限
 */
export const hasButtonPermission = (buttonKey: string): boolean => {
  const { buttons } = getPermissions();
  return buttons.includes(buttonKey);
};

/**
 * 过滤菜单树
 * @param menuTree 菜单树
 * @returns 过滤后的菜单树
 */
export const filterMenuTree = (menuTree: any[]) => {
  if (!menuTree || !Array.isArray(menuTree)) {
    return [];
  }

  console.log('开始过滤菜单树，原始菜单树:', menuTree);
  console.log('当前权限列表:', getPermissions().menus);

  return menuTree.filter(item => {
    // 特殊路由始终保留
    if (item.path === '/user' || item.path === '/' || item.path === '*' || item.path === '/unauthorized') {
      return true;
    }

    // 获取菜单键（使用 name 或 path 属性）
    let menuKey = item.name;

    // 如果没有 name，尝试使用 path 的最后一部分作为 key
    if (!menuKey && item.path) {
      const pathParts = item.path.split('/');
      menuKey = pathParts[pathParts.length - 1];
    }

    // 如果仍然没有 key，则不显示
    if (!menuKey) {
      console.log('菜单项没有 name 或有效的 path:', item);
      return false;
    }

    // 检查菜单权限
    const hasPermission = hasMenuPermission(menuKey);
    console.log(`菜单 ${menuKey} 是否有权限:`, hasPermission);

    // 如果有子菜单，递归过滤
    if (item.routes && item.routes.length > 0) {
      console.log(`处理 ${menuKey} 的子菜单...`);
      const filteredChildren = filterMenuTree(item.routes);

      // 替换原来的子菜单
      item.routes = filteredChildren;

      // 如果子菜单不为空，则保留父菜单，即使父菜单本身不可见
      if (filteredChildren.length > 0) {
        console.log(`${menuKey} 有可见的子菜单，保留父菜单`);
        return true;
      }
    }

    // 如果没有子菜单，根据菜单本身的可见性决定是否显示
    return hasPermission;
  });
};

/**
 * 清除权限信息
 */
export const clearPermissions = () => {
  userPermissions = {
    menus: [],
    buttons: []
  };
  localStorage.removeItem('permissions');
};
