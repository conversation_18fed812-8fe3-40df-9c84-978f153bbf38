import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/corp/cityConfig/list',
  add: '/corp/cityConfig/add',
  edit: '/corp/cityConfig/edit',
  delete: '/corp/cityConfig/delete',
  deleteBatch: '/corp/cityConfig/deleteBatch',
  queryById: '/corp/cityConfig/queryById',
  getParentCities: '/corp/cityConfig/getParentCities',
  syncCityData: '/corp/cityConfig/syncCityData',
};

export interface CityConfig {
  id?: string;
  cityCode: string;
  regionCityName: string;
  allCityName: string;
  topCityName: string;
  platePrefix: string;
  parentCityCode?: string;
  parentCityName?: string;
}

export interface ParentCity {
  id: number;
  nodeType: number;
  code: string;
  name: string;
  createTime: string;
  parentId: number;
  cityLevel: string;
}

export interface CityConfigSearchDTO {
  cityCode?: string;
  allCityName?: string;
  topCityName?: string;
  platePrefix?: string;
  pageNo?: number;
  pageSize?: number;
}

/**
 * 分页查询城市配置列表
 */
export async function searchCityConfigs(data: CityConfigSearchDTO) {
  return request(API.list, {
    method: 'POST',
    data,
  });
}

/**
 * 添加城市配置
 */
export async function addCityConfig(data: CityConfig) {
  return request(API.add, {
    method: 'POST',
    data,
  });
}

/**
 * 编辑城市配置
 */
export async function editCityConfig(data: CityConfig) {
  return request(API.edit, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除城市配置
 */
export async function deleteCityConfig(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除城市配置
 */
export async function deleteBatchCityConfig(ids: string) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    params: { ids },
  });
}

/**
 * 根据ID查询城市配置
 */
export async function getCityConfigById(id: string) {
  return request(API.queryById, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 获取上级城市列表
 */
export async function getParentCities() {
  return request(API.getParentCities, {
    method: 'GET',
  });
}

/**
 * 同步城市数据
 */
export async function syncCityData() {
  return request(API.syncCityData, {
    method: 'POST',
  });
}
