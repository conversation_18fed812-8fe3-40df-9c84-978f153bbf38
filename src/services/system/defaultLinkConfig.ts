import { request } from '@/utils/request';

// API 路径
const API = {
  add: '/wechat/sysDeployConfig/add',
  edit: '/wechat/sysDeployConfig/edit',
  queryById: '/wechat/sysDeployConfig/queryById',
};

/**
 * 添加默认链接配置
 * @param params 表单数据
 */
export async function addDefaultLinkConfig(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑默认链接配置
 * @param params 表单数据
 */
export async function editDefaultLinkConfig(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 通过ID查询默认链接配置
 * @param id 记录ID
 * @param deployType 配置类型 (2:车险, 3:财险, 4:增值服务)
 */
export async function queryDefaultLinkConfigById(id: string, deployType: number) {
  return request(API.queryById, {
    method: 'GET',
    params: { id, deployType },
  });
}
