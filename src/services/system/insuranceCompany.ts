// 保险公司管理相关API
import { request } from '@/utils/request';

// 获取保险公司列表
export async function getInsuranceCompanyList(params?: any) {
  return request('/corp/pdCompany/list', {
    method: 'GET',
    params,
  });
}

// 获取保险公司详情
export async function getInsuranceCompanyById(id: string) {
  return request('/corp/pdCompany/queryById', {
    method: 'GET',
    params: { id },
  });
}

// 添加租户保险公司关系
export async function addTenantInsuranceRelation(data: {
  tenantId: string;
  insuranceCompanyIds: string;
}) {
  return request('/sys/wlTenantInsuranceRel/addRelation', {
    method: 'POST',
    params: data,
  });
}

// 移除租户保险公司关系
export async function removeTenantInsuranceRelation(data: {
  tenantId: string;
  insuranceCompanyIds: string;
}) {
  return request('/sys/wlTenantInsuranceRel/removeRelation', {
    method: 'DELETE',
    params: data,
  });
}

// 根据租户ID查询保险公司关系列表
export async function getTenantInsuranceRelations(tenantId: string) {
  return request('/sys/wlTenantInsuranceRel/listByTenantId', {
    method: 'GET',
    params: { tenantId },
  });
}

// 根据保险公司ID查询租户关系列表
export async function getInsuranceTenantRelations(insuranceCompanyId: string) {
  return request('/sys/wlTenantInsuranceRel/listByInsuranceCompanyId', {
    method: 'GET',
    params: { insuranceCompanyId },
  });
}
