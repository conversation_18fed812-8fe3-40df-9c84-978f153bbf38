// 定时任务相关API
import { request } from '@umijs/max';

// API路径
const API = {
  list: '/sys/quartzJob/list',
  add: '/sys/quartzJob/add',
  edit: '/sys/quartzJob/edit',
  queryById: '/sys/quartzJob/queryById',
  pause: '/sys/quartzJob/pause',
  resume: '/sys/quartzJob/resume',
  delete: '/sys/quartzJob/delete',
  exportXls: '/sys/quartzJob/exportXls',
  importExcel: '/sys/quartzJob/importExcel',
  execute: '/sys/quartzJob/execute',
  deleteBatch: '/sys/quartzJob/deleteBatch',
};

/**
 * 查询定时任务列表
 * @param params 查询参数
 */
export async function getQuartzJobList(params?: any) {
  return request(API.list, {
    method: 'GET',
    params,
  });
}

/**
 * 添加定时任务
 * @param data 表单数据
 */
export async function addQuartzJob(data: any) {
  return request(API.add, {
    method: 'POST',
    data,
  });
}

/**
 * 编辑定时任务
 * @param data 表单数据
 */
export async function editQuartzJob(data: any) {
  return request(API.edit, {
    method: 'POST',
    data,
  });
}

/**
 * 查询定时任务详情
 * @param id 任务ID
 */
export async function getQuartzJobById(id: string) {
  return request(API.queryById, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 暂停定时任务
 * @param id 任务ID
 */
export async function pauseQuartzJob(id: string) {
  return request(API.pause, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 恢复定时任务
 * @param id 任务ID
 */
export async function resumeQuartzJob(id: string) {
  return request(API.resume, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 删除定时任务
 * @param id 任务ID
 */
export async function deleteQuartzJob(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除定时任务
 * @param ids 任务ID数组
 */
export async function batchDeleteQuartzJob(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 立即执行定时任务
 * @param id 任务ID
 */
export async function executeQuartzJob(id: string) {
  return request(API.execute, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 导出定时任务
 * @param params 查询参数
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 导入定时任务
 */
export function getImportUrl() {
  return API.importExcel;
}
