import { request } from '@/utils/request';

// API 路径
const API = {
  add: '/wechat/sysDeployConfig/add',
  edit: '/wechat/sysDeployConfig/edit',
  queryById: '/wechat/sysDeployConfig/queryById',
  getCompanyUserConfig: '/wechat/sysDeployConfig/getCompanyUserConfig',
  getRegionConfig: '/wechat/sysDeployConfig/getRegionConfig',
  getMiniProgramConfig: '/wechat/sysDeployConfig/getMiniProgramConfig',
};

/**
 * 添加系统配置
 * @param params 表单数据
 */
export async function addSysDeployConfig(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑系统配置
 * @param params 表单数据
 */
export async function editSysDeployConfig(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 通过ID查询系统配置
 * @param id 记录ID
 */
export async function querySysDeployConfigById(id: string) {
  return request(API.queryById, {
    method: 'GET',
    params: { id },
  });
}

/**
 * 获取保司人员配置（结构化数据）
 */
export async function getCompanyUserConfig() {
  return request(API.getCompanyUserConfig, {
    method: 'GET',
  });
}

/**
 * 获取地区配置（结构化数据）
 */
export async function getRegionConfig() {
  return request(API.getRegionConfig, {
    method: 'GET',
  });
}

/**
 * 获取小程序配置（结构化数据）
 */
export async function getMiniProgramConfig() {
  return request(API.getMiniProgramConfig, {
    method: 'GET',
  });
}




