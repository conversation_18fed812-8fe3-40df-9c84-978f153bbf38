import { request } from '@/utils/request';

// API 路径
const API = {
  listAll: '/sys/user/listAllName',
  list: '/sys/user/list',
};

/**
 * 获取所有用户列表（包含姓名，不做租户隔离）
 * 支持分页和关键字搜索，优化大数据量查询性能
 * @param params 查询参数 { keyword?: string, pageNo?: number, pageSize?: number }
 */
export async function getUserList(params?: any) {
  return request(API.listAll, {
    method: 'GET',
    params,
  });
}

/**
 * 获取用户列表（租户隔离）
 * @param params 查询参数
 */
export async function getUserListWithTenant(params?: any) {
  return request(API.list, {
    method: 'GET',
    params,
  });
}
