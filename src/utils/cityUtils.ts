/**
 * 处理城市数据，去除城市名称中的"省"、"市"等后缀
 * @param cityList 城市列表，格式为 ["城市名:类型", ...]
 * @returns 处理后的城市对象数组，格式为 [{city: "城市名", type: 类型值}]
 */
export const processCityData = (cityList: string[]): { city: string; type: number }[] => {
  if (!cityList || !Array.isArray(cityList) || cityList.length === 0) return [];

  return cityList.map((item: string) => {
    const [city, typeStr] = item.split(':');
    const type = typeStr ? parseInt(typeStr, 10) : 1;

    // 去掉"省"、"市"、"自治区"、"特别行政区"等后缀
    let processedCity = city;
    if (city.endsWith('省')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('市')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('自治区')) {
      processedCity = city.slice(0, -3);
    } else if (city.endsWith('特别行政区')) {
      processedCity = city.slice(0, -5);
    }

    return {
      city: processedCity,
      type: isNaN(type) ? 1 : type
    };
  });
};

/**
 * 处理城市数据字符串，去除城市名称中的"省"、"市"等后缀
 * @param cityList 城市列表，格式为 ["城市名:类型", ...]
 * @returns 处理后的城市字符串数组，格式为 ["城市名:类型", ...]
 */
export const processCityStrings = (cityList: string[]): string[] => {
  if (!cityList || !Array.isArray(cityList) || cityList.length === 0) return [];

  return cityList.map((item: string) => {
    const [city, type] = item.split(':');

    // 去掉"省"、"市"、"自治区"、"特别行政区"等后缀
    let processedCity = city;
    if (city.endsWith('省')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('市')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('自治区')) {
      processedCity = city.slice(0, -3);
    } else if (city.endsWith('特别行政区')) {
      processedCity = city.slice(0, -5);
    }

    return `${processedCity}:${type}`;
  });
};

// 城市信息接口
export interface CityInfo {
  code: string;
  name: string;
  type: number;
}

// 缓存城市数据
let cityTreeCache: any[] | null = null;

/**
 * 根据城市代码获取城市信息
 * @param cityCodes 城市代码数组
 * @returns 城市信息数组
 */
export const getCityInfosByCodes = async (cityCodes: string[]): Promise<CityInfo[]> => {
  if (!cityCodes || cityCodes.length === 0) {
    return [];
  }

  try {
    // 如果没有缓存，则获取城市数据
    if (!cityTreeCache) {
      const { getCityTree } = await import('@/services/corp/city');
      const response = await getCityTree();

      if (response && response.success && response.result) {
        cityTreeCache = response.result;
      } else {
        console.error('获取城市数据失败:', response);
        return [];
      }
    }

    const result: CityInfo[] = [];

    // 遍历城市代码，查找对应的城市信息
    cityCodes.forEach(code => {
      // 在省份中查找
      for (const province of cityTreeCache!) {
        if (province.code === code) {
          result.push({
            code: province.code,
            name: province.name,
            type: 1 // 省份类型
          });
          return;
        }

        // 在城市中查找
        if (province.children && Array.isArray(province.children)) {
          for (const city of province.children) {
            if (city.code === code) {
              result.push({
                code: city.code,
                name: city.name,
                type: 2 // 城市类型
              });
              return;
            }
          }
        }
      }

      // 如果没找到，返回原始代码
      console.warn(`未找到城市代码 ${code} 对应的城市信息`);
      result.push({
        code: code,
        name: code,
        type: 1
      });
    });

    return result;
  } catch (error) {
    console.error('获取城市信息失败:', error);
    // 如果出错，返回原始代码作为名称
    return cityCodes.map(code => ({
      code: code,
      name: code,
      type: 1
    }));
  }
};

/**
 * 根据单个城市代码获取城市信息
 * @param cityCode 城市代码
 * @returns 城市信息
 */
export const getCityInfoByCode = async (cityCode: string): Promise<CityInfo | null> => {
  if (!cityCode) {
    return null;
  }

  try {
    const result = await getCityInfosByCodes([cityCode]);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('获取城市信息失败:', error);
    return null;
  }
};

/**
 * 检查数组是否为纯城市编码格式（只包含数字编码，不包含冒号分隔的格式）
 * @param values 要检查的值数组
 * @returns 是否为纯编码格式
 */
export const isPureCityCodeFormat = (values: string[]): boolean => {
  if (!values || values.length === 0) {
    return false;
  }

  // 检查所有值是否都是纯数字编码（不包含冒号）
  return values.every(value => {
    if (typeof value !== 'string') {
      return false;
    }

    // 如果包含冒号，说明已经是格式化的数据
    if (value.includes(':')) {
      return false;
    }

    // 检查是否为纯数字编码（6位数字）
    return /^\d{6}$/.test(value.trim());
  });
};

/**
 * 将纯编码格式转换为标准格式（code:name:type）
 * @param codes 城市编码数组
 * @returns 标准格式的城市数组
 */
export const convertCodesToStandardFormat = async (codes: string[]): Promise<string[]> => {
  if (!codes || codes.length === 0) {
    return [];
  }

  try {
    // 获取城市信息
    const cityInfos = await getCityInfosByCodes(codes);

    // 转换为标准格式
    return cityInfos.map(info => `${info.code}:${info.name}:${info.type}`);
  } catch (error) {
    console.error('转换城市编码失败:', error);
    // 如果转换失败，返回原始编码作为名称
    return codes.map(code => `${code}:${code}:1`);
  }
};
