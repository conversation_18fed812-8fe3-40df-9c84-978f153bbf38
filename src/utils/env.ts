/**
 * 环境变量配置工具
 */

// 获取环境变量
export function getEnv(): string {
  return process.env.NODE_ENV || 'development';
}

// 是否为开发环境
export function isDev(): boolean {
  return getEnv() === 'development';
}

// 是否为生产环境
export function isProd(): boolean {
  return getEnv() === 'production';
}

// 是否为测试环境
export function isTest(): boolean {
  return getEnv() === 'test';
}

// 获取环境类型
export function getEnvType(): 'development' | 'production' | 'test' {
  return getEnv() as 'development' | 'production' | 'test';
}

// 获取API基础URL
export function getApiBaseUrl(): string {
  // 在开发环境中，直接使用 process.env
  if (isDev()) {
    return process.env.VITE_GLOB_DOMAIN_URL || 'http://127.0.0.1:8089/jeecg-boot';
  }

  // 在生产环境中，从 window.__PRODUCTION_ENV__ 获取
  if (typeof window !== 'undefined' && window.__PRODUCTION_ENV__) {
    return window.__PRODUCTION_ENV__.VITE_GLOB_DOMAIN_URL || 'https://www.lunarforge.cn/jeecg-boot';
  }

  // 获取当前公司
  const getCurrentCompany = () => {
    try {
      // 尝试从 window.__PRODUCTION_ENV__ 获取公司标识
      if (typeof window !== 'undefined' && window.__PRODUCTION_ENV__ && window.__PRODUCTION_ENV__.COMPANY_KEY) {
        return window.__PRODUCTION_ENV__.COMPANY_KEY;
      }
    } catch (e) {
      console.error('获取公司标识失败:', e);
    }
    return 'taiyi'; // 默认为太一
  };

  // 根据公司和环境返回对应的API地址
  const company = getCurrentCompany();

  // 获取当前环境类型
  const getEnvTypeFromWindow = () => {
    try {
      if (typeof window !== 'undefined' && window.__PRODUCTION_ENV__ && window.__PRODUCTION_ENV__.NODE_ENV) {
        return window.__PRODUCTION_ENV__.NODE_ENV;
      }
    } catch (e) {
      console.error('获取环境类型失败:', e);
    }
    return 'production'; // 默认为生产环境
  };

  const envType = getEnvTypeFromWindow();

  // 不同环境的API地址配置
  const apiUrls = {
    production: {
      taiyi: 'https://www.lunarforge.cn/jeecg-boot',
      xudong: 'https://www.xdinnovation.cn/jeecg-boot'
    },
    test: {
      taiyi: 'http://101.42.109.196:2601/jeecg-boot',
      xudong: 'http://101.42.109.196:3601/jeecg-boot'
    }
  };

  // 如果找不到对应环境或公司的配置，则使用生产环境太一公司的配置作为默认值
  return apiUrls[envType]?.[company] || apiUrls.production.taiyi;
}
