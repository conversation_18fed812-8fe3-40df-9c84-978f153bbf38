import provinces from './provinces.json';
import cities from './cities.json';

// 类型定义
interface Province {
  code: string;
  name: string;
}

interface City {
  code: string;
  name: string;
  provinceCode: string;
}

interface TreeNode {
  title: string;
  value: string;
  key: string;
  isLeaf?: boolean;
  children?: TreeNode[];
  type: number; // 1=省级, 2=市级
}

// 直辖市列表
const directCities = ['北京市', '天津市', '上海市', '重庆市'];

// 将省市数据转换为树形结构
export const getProvinceTreeData = (): TreeNode[] => {
  // 创建树形结构数据
  const treeData = provinces.map((province) => {
    const { code, name } = province;

    // 判断是否为直辖市
    const isDirect = directCities.includes(name);

    // 如果是直辖市，则作为一级节点
    if (isDirect) {
      return {
        title: name,
        value: name,
        key: code,
        isLeaf: true,
        type: 1, // 省级
      };
    }

    // 获取该省下的所有城市
    const provinceCities = cities.filter((city) => city.provinceCode === code);

    // 将城市作为子节点
    const children = provinceCities.map((city) => ({
      title: city.name,
      value: city.name,
      key: city.code,
      isLeaf: true,
      type: 2, // 市级
    }));

    // 返回省份节点
    return {
      title: name,
      value: name,
      key: code,
      children,
      type: 1, // 省级
    };
  });

  return treeData;
};

// 将选择结果转换为简单的省市名称列表（逗号分隔字符串）
export const formatSelectedToString = (selectedValues: string[]): string => {
  return selectedValues.join(',');
};

// 将选择结果转换为带级别信息的JSON字符串
export const formatSelectedWithLevel = (selectedValues: string[], treeData: TreeNode[]): { city: string; type: number }[] => {
  // 创建一个映射表，用于快速查找节点
  const nodeMap = new Map<string, TreeNode>();

  // 遍历树形数据，将所有节点添加到映射表中
  const traverseTree = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      nodeMap.set(node.value, node);
      if (node.children) {
        traverseTree(node.children);
      }
    });
  };

  traverseTree(treeData);

  // 将选中的值转换为带级别信息的数据
  const result = selectedValues.map((value) => {
    const node = nodeMap.get(value);
    if (node) {
      return {
        city: value,
        type: node.type,
      };
    }
    return null;
  }).filter(Boolean);

  return result;
};
