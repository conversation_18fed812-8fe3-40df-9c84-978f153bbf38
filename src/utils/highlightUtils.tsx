import React from 'react';

/**
 * 高亮搜索关键词
 * @param text 原始文本
 * @param keyword 搜索关键词
 * @param highlightStyle 高亮样式
 * @returns 高亮后的JSX元素
 */
export const highlightKeyword = (
  text: string, 
  keyword: string, 
  highlightStyle: React.CSSProperties = { backgroundColor: '#fff566', padding: '0 2px' }
): React.ReactNode => {
  if (!keyword || !text) return text;

  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, index) => 
    regex.test(part) ? (
      <span key={index} style={highlightStyle}>
        {part}
      </span>
    ) : (
      part
    )
  );
};
