// OSS配置文件
export const OSS_CONFIG = {
  // 阿里云OSS域名配置
  domain: 'https://your-oss-domain.com', // 请替换为您的实际OSS域名
  
  // 获取完整的图片URL
  getImageUrl: (path: string): string => {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    return `${OSS_CONFIG.domain}/${path}`;
  },
  
  // 获取缩略图URL（如果OSS支持图片处理）
  getThumbnailUrl: (path: string, width: number = 100, height: number = 100): string => {
    const baseUrl = OSS_CONFIG.getImageUrl(path);
    if (!baseUrl || baseUrl.startsWith('data:')) return baseUrl;
    // 阿里云OSS图片处理参数示例
    return `${baseUrl}?x-oss-process=image/resize,w_${width},h_${height},m_fill`;
  }
};

export default OSS_CONFIG;
