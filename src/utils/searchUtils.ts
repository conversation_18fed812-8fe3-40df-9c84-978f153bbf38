/**
 * 搜索工具函数
 */

/**
 * 租户搜索函数
 * @param tenants 租户列表
 * @param searchTerm 搜索词
 * @returns 过滤后的租户列表
 */
export const searchTenants = <T extends { id: number | string; name: string }>(
  tenants: T[],
  searchTerm: string
): T[] => {
  if (!searchTerm.trim()) return tenants;

  const term = searchTerm.toLowerCase().trim();

  return tenants.filter(tenant => {
    // 支持按ID搜索（精确匹配和部分匹配）
    const idMatch = tenant.id.toString().toLowerCase().includes(term);

    // 支持按名称搜索（模糊匹配）
    const nameMatch = tenant.name.toLowerCase().includes(term);

    return idMatch || nameMatch;
  });
};

/**
 * 智能搜索排序
 * 优先级：精确匹配 > 开头匹配 > 包含匹配
 * @param items 搜索结果
 * @param searchTerm 搜索词
 * @param getSearchText 获取搜索文本的函数
 * @returns 排序后的结果
 */
export const smartSort = <T>(
  items: T[],
  searchTerm: string,
  getSearchText: (item: T) => string
): T[] => {
  if (!searchTerm.trim()) return items;

  const term = searchTerm.toLowerCase().trim();

  return items.sort((a, b) => {
    const textA = getSearchText(a).toLowerCase();
    const textB = getSearchText(b).toLowerCase();

    // 精确匹配优先级最高
    if (textA === term && textB !== term) return -1;
    if (textB === term && textA !== term) return 1;

    // 开头匹配优先级次之
    const startsWithA = textA.startsWith(term);
    const startsWithB = textB.startsWith(term);

    if (startsWithA && !startsWithB) return -1;
    if (startsWithB && !startsWithA) return 1;

    // 其他情况按字母顺序排序
    return textA.localeCompare(textB);
  });
};
