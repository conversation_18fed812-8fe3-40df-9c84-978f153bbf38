/**
 * 判断当前用户是否是超级管理员（租户ID为0）
 * @returns {boolean} 是否是超级管理员
 */
export function isTenantIdZero(): boolean {
  // 从localStorage获取用户信息
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) return false;
  
  try {
    const userInfo = JSON.parse(userInfoStr);
    return userInfo?.tenantId === 0 || userInfo?.tenantId === '0';
  } catch (error) {
    console.error('解析用户信息失败', error);
    return false;
  }
}
